{"version": 3, "file": "vendor/core/plugins/social-login/css/social-login.css", "mappings": "AAAA,eACI,kBAEA,qBAGI,6BAIA,mBADA,gBADA,oBADA,iBAHA,kBACA,SAKA,CAEA,uBAEI,cAIA,qBALA,eAEA,gBACA,eACA,iBACA,CAIR,mCAEI,gBADA,cACA,CAEA,sCACI,gBAGJ,iDAII,mBAGA,mBAJA,aAFA,eACA,gBAMA,mBAFA,kBAGA,eAJA,UAIA,CAEA,qDAGI,kBADA,eADA,cAEA,CAGJ,gEACI,yBACA,WAGJ,8DACI,sBAEA,yBADA,aACA,CAGJ,6DACI,sBACA,WACA,gBASJ,qMACI,sBAEA,yBADA,aACA,CAGJ,uDAGI,wCAFA,2BACA,cACA,CAKZ,6BAEI,mBADA,aAGA,QADA,uBAGA,SADA,SACA,CAEA,6CACI,sBAEA,gDACI,WAEA,kDAGI,mBAGA,0CALA,aAIA,UAHA,uBAKA,oBACA,qBAJA,UAIA,CAEA,uDACI,cAMhB,gCACI,gBAEA,kCAKI,mBAJA,kBAEA,aACA,uBAFA,eAIA,qBAEA,uCACI,aAGJ,sCAEI,cADA,YACA,CAIR,wCACI,yBACA,WAEA,8CACI,yBAIR,2FAEI,yBACA,WAEA,uGACI,yBAIR,0CACI,yBACA,WAEA,gDACI,yBAIR,wCACI,sBACA,WAEA,8CACI,sBAOpB,wBAEQ,mCACI,UAEA,iDACI,0B", "sources": ["webpack:///./platform/plugins/social-login/resources/sass/social-login.scss"], "sourcesContent": [".login-options {\n    text-align: center;\n\n    &-title {\n        position: relative;\n        z-index: 1;\n        border-top: 1px solid #E0E2E3;\n        padding-top: 40px;\n        padding-bottom: 15px;\n        margin-top: 40px;\n        margin-bottom: 15px;\n\n        p {\n            font-size: 15px;\n            color: #55585B;\n            margin-bottom: 0;\n            padding: 0 20px;\n            position: relative;\n            display: inline-block;\n        }\n    }\n\n    .social-login-basic {\n        padding: 0 50px;\n        list-style: none;\n\n        li {\n            list-style: none;\n        }\n\n        .social-login {\n            font-size: 16px;\n            font-weight: 700;\n            display: flex;\n            align-items: center;\n            width: 100%;\n            padding: 15px 25px;\n            border-radius: 10px;\n            margin-bottom: 20px;\n            transition: 0.3s;\n\n            img {\n                min-width: 28px;\n                max-width: 28px;\n                margin-right: 15px;\n            }\n\n            &.facebook-login {\n                background-color: #1877F2;\n                color: #fff;\n            }\n\n            &.google-login {\n                background-color: #fff;\n                color: #55585B;\n                border: 1px solid #F2F3F4;\n            }\n\n            &.apple-login {\n                background-color: #000000;\n                color: #fff;\n                margin-bottom: 0;\n            }\n\n            &.github-login {\n                background-color: #fff;\n                color: #55585B;\n                border: 1px solid #F2F3F4;\n            }\n\n            &.linkedin-login, &.linkedin-openid-login {\n                background-color: #fff;\n                color: #55585B;\n                border: 1px solid #F2F3F4;\n            }\n\n            &:hover {\n                transform: translateY(-3px);\n                transition: 0.3s;\n                box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.05);\n            }\n        }\n    }\n\n    .social-icons {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        gap: 8px;\n        padding: 0;\n        margin: 0;\n\n        &.social-login-lg {\n            flex-direction: column;\n\n            li {\n                width: 100%;\n\n                > a {\n                    display: flex;\n                    justify-content: center;\n                    align-items: center;\n                    width: 100%;\n                    gap: 0.5rem;\n                    border-radius: var(--bs-border-radius, 8px);\n                    padding: 0.55rem 1rem;\n                    text-decoration: none;\n\n                    span {\n                        display: block;\n                    }\n                }\n            }\n        }\n\n        li {\n            list-style: none;\n\n            > a {\n                border-radius: 2px;\n                padding: 0.25rem;\n                display: flex;\n                justify-content: center;\n                align-items: center;\n                text-decoration: none;\n\n                span {\n                    display: none;\n                }\n\n                svg {\n                    width: 1.5rem;\n                    height: 1.5rem;\n                }\n            }\n\n            .google {\n                background-color: #DB4437;\n                color: #fff;\n\n                &:hover {\n                    background-color: #C13505;\n                }\n            }\n\n            .linkedin,\n            .linkedin-openid {\n                background-color: #0A66C2;\n                color: #fff;\n\n                &:hover {\n                    background-color: #0A5CBA;\n                }\n            }\n\n            .facebook {\n                background-color: #4267B2;\n                color: #fff;\n\n                &:hover {\n                    background-color: #3A5795;\n                }\n            }\n\n            .github {\n                background-color: #333;\n                color: #fff;\n\n                &:hover {\n                    background-color: #222;\n                }\n            }\n        }\n    }\n}\n\n@media (max-width: 768px) {\n    .login-options {\n        .social-login-basic {\n            padding: 0;\n\n            .social-login {\n                font-size: 14px !important;\n            }\n        }\n    }\n}\n"], "names": [], "sourceRoot": ""}