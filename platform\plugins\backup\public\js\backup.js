(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){for(var o=0;o<e.length;o++){var a=e[o];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,n(a.key),a)}}function n(e){var n=function(e,n){if("object"!=t(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var a=o.call(e,n||"default");if("object"!=t(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(e,"string");return"symbol"==t(n)?n:n+""}var o=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)},(n=[{key:"init",value:function(){var t=$("#table-backups");t.on("click",".deleteDialog",(function(t){t.preventDefault(),$(".delete-crud-entry").data("section",$(t.currentTarget).data("section")),$(".modal-confirm-delete").modal("show")})),t.on("click",".restoreBackup",(function(t){t.preventDefault(),$("#restore-backup-button").data("section",$(t.currentTarget).data("section")),$("#restore-backup-modal").modal("show")})),$(".delete-crud-entry").on("click",(function(e){e.preventDefault(),$(".modal-confirm-delete").modal("hide");var n=$(e.currentTarget).data("section");$httpClient.make().delete(n).then((function(e){var o=e.data;t.find("tbody tr").length<=1&&t.load(window.location.href+" #table-backups > *"),t.find('button[data-section="'.concat(n,'"]')).closest("tr").remove(),Shaqi.showSuccess(o.message)}))})),$("#restore-backup-button").on("click",(function(t){t.preventDefault();var e=$(t.currentTarget);Shaqi.showButtonLoading(e),$httpClient.make().get(e.data("section")).then((function(t){var n=t.data;e.closest(".modal").modal("hide"),Shaqi.showSuccess(n.message),window.location.reload()})).finally((function(){Shaqi.hideButtonLoading(e)}))})),$(document).on("click","#generate_backup",(function(t){t.preventDefault(),$("#name").val(""),$("#description").val(""),$("#create-backup-modal").modal("show")})),$("#create-backup-modal").on("click","#create-backup-button",(function(e){e.preventDefault();var n=$(e.currentTarget),o=n.closest("form");$httpClient.make().withButtonLoading(n).post(o.prop("action"),new FormData(o[0])).then((function(e){var n=e.data;t.find(".no-backup-row").remove(),t.find("tbody").append(n.data),Shaqi.showSuccess(n.message)})).finally((function(){n.closest(".modal").modal("hide")}))}))}}])&&e(t.prototype,n),o&&e(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,o}();$((function(){(new o).init()}))})();
//# sourceMappingURL=backup.js.map