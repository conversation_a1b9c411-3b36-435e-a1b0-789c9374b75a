@mixin bg-color($color, $opacity) {
    background-color: rgba($color, $opacity);
}

@mixin tp-placeholder {
    &::-webkit-input-placeholder {
        @content;
    }
    &:-moz-placeholder {
        @content;
    }
    &::-moz-placeholder {
        @content;
    }
    &:-ms-input-placeholder {
        @content;
    }
}

@mixin filter($value) {
    -webkit-filter: $value;
    filter: $value;
}

@mixin appearance($value) {
    -webkit-appearance: $value;
    -moz-appearance: $value;
    -ms-appearance: $value;
    -o-appearance: $value;
    appearance: $value;
}

@mixin keyframes($name) {
    @-webkit-keyframes #{$name} {
        @content;
    }
    @-moz-keyframes #{$name} {
        @content;
    }
    @-ms-keyframes #{$name} {
        @content;
    }
    @keyframes #{$name} {
        @content;
    }
}

@mixin background {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

@mixin transition($time) {
    -webkit-transition: all $time ease-out 0s;
    -moz-transition: all $time ease-out 0s;
    -ms-transition: all $time ease-out 0s;
    -o-transition: all $time ease-out 0s;
    transition: all $time ease-out 0s;
}

@mixin transform($transforms) {
    -webkit-transform: $transforms;
    -moz-transform: $transforms;
    -ms-transform: $transforms;
    -o-transform: $transforms;
    transform: $transforms;
}

@mixin border-radius($man) {
    -webkit-border-radius: $man;
    -moz-border-radius: $man;
    -o-border-radius: $man;
    -ms-border-radius: $man;
    border-radius: $man;
}

@mixin sentence-case() {
    text-transform: lowercase;
    &:first-letter {
        text-transform: uppercase;
    }
}

@mixin flexbox() {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
}

@mixin box-shadow($shadow) {
    -webkit-box-shadow: $shadow;
    -moz-box-shadow: $shadow;
    -ms-box-shadow: $shadow;
    -o-box-shadow: $shadow;
    box-shadow: $shadow;
}

    .tagline {
      background: #fff;
      color: #000;
      display: inline-block;
      font-weight: bold;
      padding: 5px 15px;
      margin-bottom: 15px;
      text-transform: uppercase;
      font-size: 14px;
    }

    .section-title {
      font-size: 36px;
      font-weight: 700;
      margin-bottom: 40px;
    }

    .accordion-button {
      background-color: transparent;
      color: #fff;
      font-weight: bold;
      border-bottom: 1px solid #fff;
    }

    .accordion-button::after {
      filter: invert(1);
    }

    .accordion-button:not(.collapsed) {
      background-color: transparent;
      color: #f1c40f;
    }

    .accordion-body {
      text-align: left;
      background: #111;
      color: #ddd;
    }

    .btn-custom {
      border: 1px solid #fff;
      color: #fff;
      padding: 10px 20px;
      margin-top: 40px;
      text-decoration: none;
      display: inline-block;
    }

    .btn-custom:hover {
      background-color: #f1c40f;
      color: #000;
    }

    .image-column img {
      max-width: 100%;
      border-radius: 8px;
    }
