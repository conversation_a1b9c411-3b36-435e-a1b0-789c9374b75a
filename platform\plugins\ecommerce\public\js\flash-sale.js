$((function(){$(document).on("click",".list-search-data .selectable-item",(function(e){e.preventDefault();var a=$(e.currentTarget),t=a.closest(".box-search-advance").find("input[type=hidden]"),r=[];if($.each(t.val().split(","),(function(e,a){a&&""!==a&&(r[e]=parseInt(a))})),$.inArray(a.data("id"),r)<0){t.val()?t.val("".concat(t.val(),",").concat(a.data("id"))):t.val(a.data("id"));var c=$(document).find("#selected_product_list_template").html().replace(/__name__/gi,a.data("name")).replace(/__id__/gi,a.data("id")).replace(/__index__/gi,r.length).replace(/__url__/gi,a.data("url")).replace(/__image__/gi,a.data("image")).replace(/__price__/gi,a.data("price")).replace(/__attributes__/gi,a.find("a span").text());a.closest(".box-search-advance").find(".list-selected-products").show(),a.closest(".box-search-advance").find(".list-selected-products").append(c)}a.closest(".card").hide()})),$(document).on("click",'[data-bb-toggle="product-search-advanced"]',(function(e){var a=$(e.currentTarget),t=a.closest(".box-search-advance").find(".card");t.show(),t.addClass("active"),0===t.find(".card-body").length&&(Shaqi.showLoading(t),$.ajax({url:a.data("bb-target"),type:"GET",success:function(e){e.error?Shaqi.showError(e.message):t.html(e.data)},error:function(e){Shaqi.handleError(e)},complete:function(){Shaqi.hideLoading(t)}}))})),$(document).on("keyup",'[data-bb-toggle="product-search-advanced"]',(function(e){var a=$(e.currentTarget),t=a.closest(".box-search-advance").find(".card");setTimeout((function(){Shaqi.hideLoading(t),$.ajax({url:"".concat(a.data("bb-target"),"?keyword=").concat(a.val()),type:"GET",success:function(e){e.error?Shaqi.showError(e.message):t.html(e.data)},error:function(e){Shaqi.handleError(e)},complete:function(){Shaqi.hideLoading(t)}})}),500)})),$(document).on("click",".box-search-advance .page-link",(function(e){e.preventDefault();var a=$(e.currentTarget).closest(".box-search-advance").find('[data-bb-toggle="product-search-advanced"]');if(!a.closest(".page-item").hasClass("disabled")&&a.data("bb-target")){var t=a.closest(".box-search-advance").find(".card");Shaqi.showLoading(t),$.ajax({url:"".concat($(e.currentTarget).prop("href"),"&keyword=").concat(a.val()),type:"GET",success:function(e){e.error?Shaqi.showError(e.message):t.html(e.data)},error:function(e){Shaqi.handleError(e)},complete:function(){Shaqi.hideLoading(t)}})}})),$(document).on("click","body",(function(e){var a=$(".box-search-advance");a.is(e.target)||0!==a.has(e.target).length||a.find(".card").hide()})),$(document).on("click",'[data-bb-toggle="product-delete-item"]',(function(e){e.preventDefault();var a=$(e.currentTarget).closest(".box-search-advance").find("input[type=hidden]"),t=a.val().split(",");$.each(t,(function(e,a){a=a.trim(),_.isEmpty(a)||(t[e]=parseInt(a))}));var r=t.indexOf($(e.currentTarget).data("bb-target"));r>-1&&delete t[r],a.val(t.join(",")),$(e.currentTarget).closest(".list-group").find(".list-group-item").length<2&&$(e.currentTarget).closest(".list-selected-products").hide(),$(e.currentTarget).closest(".list-group").find(".list-group-item[data-product-id=".concat($(e.currentTarget).data("bb-target"),"]")).remove()}))}));
//# sourceMappingURL=flash-sale.js.map