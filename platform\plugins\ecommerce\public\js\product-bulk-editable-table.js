(()=>{"use strict";var a;(a=jQuery)(document).on("change",'[data-bb-toggle="product-bulk-change"]',(function(){var t=a(this),e=t.closest("table"),c=t.data("id"),i=t.is(":checkbox")||t.is(":radio")?t.is(":checked")?"1":"0":t.val(),n=t.data("column"),o=a('[data-target-id="'.concat(c,'"]'));o.length>0&&(o.hide(),o.each((function(){var t=a(this),e=t.data("target-value").toString();i===e&&t.show()}))),$httpClient.make().withLoading(e[0]).put(t.data("url"),{value:i,column:n}).then((function(a){var t=a.data;Shaqi.showSuccess(t.message)}))}))})();
//# sourceMappingURL=product-bulk-editable-table.js.map