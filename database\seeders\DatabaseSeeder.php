<?php

namespace Database\Seeders;

use <PERSON><PERSON><PERSON>\ACL\Database\Seeders\UserSeeder;
use Shaqi\Base\Supports\BaseSeeder;
use <PERSON>haqi\Ecommerce\Database\Seeders\CurrencySeeder;
use <PERSON>haqi\Ecommerce\Database\Seeders\ProductSpecificationSeeder;
use <PERSON><PERSON><PERSON>\Ecommerce\Database\Seeders\ReviewSeeder;
use <PERSON>haqi\Ecommerce\Database\Seeders\ShippingSeeder;
use Shaqi\Ecommerce\Database\Seeders\TaxSeeder;
use Shaqi\Language\Database\Seeders\LanguageSeeder;

class DatabaseSeeder extends BaseSeeder
{
    public function run(): void
    {
        $this->prepareRun();

        $this->call([
            LanguageSeeder::class,
            UserSeeder::class,
            CurrencySeeder::class,
            SettingSeeder::class,
            BlogSeeder::class,
            PageSeeder::class,
            ThemeOptionSeeder::class,
            SimpleSliderSeeder::class,
            WidgetSeeder::class,
            AdsSeeder::class,
            ShippingSeeder::class,
            BrandSeeder::class,
            ProductCategorySeeder::class,
            ProductAttributeSeeder::class,
            ProductTagSeeder::class,
            ProductCollectionSeeder::class,
            ProductLabelSeeder::class,
            ProductOptionSeeder::class,
            TaxSeeder::class,
            FaqSeeder::class,
            ProductSeeder::class,
            ProductSpecificationSeeder::class,
            CustomerSeeder::class,
            ReviewSeeder::class,
            FlashSaleSeeder::class,
            MenuSeeder::class,
            GallerySeeder::class,
            TeamSeeder::class,
            TestimonialSeeder::class,
            AnnouncementSeeder::class,
        ]);

        $this->finished();
    }
}
