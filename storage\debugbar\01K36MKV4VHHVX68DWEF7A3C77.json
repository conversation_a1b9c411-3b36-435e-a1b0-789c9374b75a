{"__meta": {"id": "01K36MKV4VHHVX68DWEF7A3C77", "datetime": "2025-08-21 15:40:30", "utime": **********.747833, "method": "GET", "uri": "/themes/ninico/plugins/bootstrap/bootstrap.min.css.map", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755790828.137877, "end": **********.747849, "duration": 2.6099720001220703, "duration_str": "2.61s", "measures": [{"label": "Booting", "start": 1755790828.137877, "relative_start": 0, "end": **********.179505, "relative_end": **********.179505, "duration": 1.***************, "duration_str": "1.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.179526, "relative_start": 1.****************, "end": **********.747852, "relative_end": 3.0994415283203125e-06, "duration": 1.****************, "duration_str": "1.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.211883, "relative_start": 1.****************, "end": **********.747862, "relative_end": 1.3113021850585938e-05, "duration": 1.****************, "duration_str": "1.54s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: theme.ninico::views.404", "start": **********.24284, "relative_start": 1.****************, "end": **********.24284, "relative_end": **********.24284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.headers.default", "start": **********.257511, "relative_start": 1.****************, "end": **********.257511, "relative_end": **********.257511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-top", "start": **********.257915, "relative_start": 1.**************83, "end": **********.257915, "relative_end": **********.257915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.currency-switcher", "start": **********.277006, "relative_start": 1.1391289234161377, "end": **********.277006, "relative_end": **********.277006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.language-switcher", "start": **********.277442, "relative_start": 1.1395649909973145, "end": **********.277442, "relative_end": **********.277442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0226480071dd0e74b211507465ab2265", "start": **********.281432, "relative_start": 1.143554925918579, "end": **********.281432, "relative_end": **********.281432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6112de9dcda50c4e5e8edc1897f5a562", "start": **********.282099, "relative_start": 1.1442220211029053, "end": **********.282099, "relative_end": **********.282099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::08a04217c3e416224df63d8e9dd782a7", "start": **********.283006, "relative_start": 1.1451289653778076, "end": **********.283006, "relative_end": **********.283006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "start": **********.283711, "relative_start": 1.145833969116211, "end": **********.283711, "relative_end": **********.283711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::19d2357ba0f51b29e340109a50dc8d90", "start": **********.284372, "relative_start": 1.1464951038360596, "end": **********.284372, "relative_end": **********.284372, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.logo", "start": **********.284683, "relative_start": 1.146806001663208, "end": **********.284683, "relative_end": **********.284683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.471388, "relative_start": 1.3335111141204834, "end": **********.471388, "relative_end": **********.471388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.490407, "relative_start": 1.3525300025939941, "end": **********.490407, "relative_end": **********.490407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.497046, "relative_start": 1.3591690063476562, "end": **********.497046, "relative_end": **********.497046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.510616, "relative_start": 1.3727390766143799, "end": **********.510616, "relative_end": **********.510616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.528196, "relative_start": 1.3903191089630127, "end": **********.528196, "relative_end": **********.528196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.55022, "relative_start": 1.4123430252075195, "end": **********.55022, "relative_end": **********.55022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.571752, "relative_start": 1.4338750839233398, "end": **********.571752, "relative_end": **********.571752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.595344, "relative_start": 1.4574670791625977, "end": **********.595344, "relative_end": **********.595344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.60818, "relative_start": 1.4703030586242676, "end": **********.60818, "relative_end": **********.60818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.626403, "relative_start": 1.4885261058807373, "end": **********.626403, "relative_end": **********.626403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.644257, "relative_start": 1.5063800811767578, "end": **********.644257, "relative_end": **********.644257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.677043, "relative_start": 1.53916597366333, "end": **********.677043, "relative_end": **********.677043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.703676, "relative_start": 1.5657989978790283, "end": **********.703676, "relative_end": **********.703676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.720878, "relative_start": 1.583000898361206, "end": **********.720878, "relative_end": **********.720878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.755545, "relative_start": 1.6176679134368896, "end": **********.755545, "relative_end": **********.755545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.798204, "relative_start": 1.6603269577026367, "end": **********.798204, "relative_end": **********.798204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.804707, "relative_start": 1.666830062866211, "end": **********.804707, "relative_end": **********.804707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.815446, "relative_start": 1.6775689125061035, "end": **********.815446, "relative_end": **********.815446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.82845, "relative_start": 1.69057297706604, "end": **********.82845, "relative_end": **********.82845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.84499, "relative_start": 1.7071130275726318, "end": **********.84499, "relative_end": **********.84499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.862532, "relative_start": 1.7246549129486084, "end": **********.862532, "relative_end": **********.862532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.868337, "relative_start": 1.7304599285125732, "end": **********.868337, "relative_end": **********.868337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.876511, "relative_start": 1.7386341094970703, "end": **********.876511, "relative_end": **********.876511, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.902507, "relative_start": 1.7646300792694092, "end": **********.902507, "relative_end": **********.902507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.937173, "relative_start": 1.7992959022521973, "end": **********.937173, "relative_end": **********.937173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.989217, "relative_start": 1.8513400554656982, "end": **********.989217, "relative_end": **********.989217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.022003, "relative_start": 1.8841259479522705, "end": **********.022003, "relative_end": **********.022003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.033661, "relative_start": 1.8957839012145996, "end": **********.033661, "relative_end": **********.033661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.059606, "relative_start": 1.9217290878295898, "end": **********.059606, "relative_end": **********.059606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-meta", "start": **********.094292, "relative_start": 1.9564149379730225, "end": **********.094292, "relative_end": **********.094292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.navbar", "start": **********.100632, "relative_start": 1.9627549648284912, "end": **********.100632, "relative_end": **********.100632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-middle", "start": **********.102123, "relative_start": 1.9642460346221924, "end": **********.102123, "relative_end": **********.102123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.logo", "start": **********.10259, "relative_start": 1.9647130966186523, "end": **********.10259, "relative_end": **********.10259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-meta", "start": **********.106205, "relative_start": 1.9683279991149902, "end": **********.106205, "relative_end": **********.106205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.logo", "start": **********.106672, "relative_start": 1.9687950611114502, "end": **********.106672, "relative_end": **********.106672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.logo", "start": **********.10943, "relative_start": 1.971553087234497, "end": **********.10943, "relative_end": **********.10943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.mobile.menu-tab-content", "start": **********.111953, "relative_start": 1.9740760326385498, "end": **********.111953, "relative_end": **********.111953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.mobile-menu", "start": **********.120743, "relative_start": 1.9828660488128662, "end": **********.120743, "relative_end": **********.120743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.124114, "relative_start": 1.9862370491027832, "end": **********.124114, "relative_end": **********.124114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.127989, "relative_start": 1.990112066268921, "end": **********.127989, "relative_end": **********.127989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.132823, "relative_start": 1.994946002960205, "end": **********.132823, "relative_end": **********.132823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.14333, "relative_start": 2.005453109741211, "end": **********.14333, "relative_end": **********.14333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.155545, "relative_start": 2.0176680088043213, "end": **********.155545, "relative_end": **********.155545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.168937, "relative_start": 2.031059980392456, "end": **********.168937, "relative_end": **********.168937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.175115, "relative_start": 2.037238121032715, "end": **********.175115, "relative_end": **********.175115, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.182841, "relative_start": 2.044964075088501, "end": **********.182841, "relative_end": **********.182841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.187142, "relative_start": 2.049264907836914, "end": **********.187142, "relative_end": **********.187142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.201938, "relative_start": 2.064060926437378, "end": **********.201938, "relative_end": **********.201938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.213775, "relative_start": 2.0758979320526123, "end": **********.213775, "relative_end": **********.213775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.228814, "relative_start": 2.0909368991851807, "end": **********.228814, "relative_end": **********.228814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.234596, "relative_start": 2.0967190265655518, "end": **********.234596, "relative_end": **********.234596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.247254, "relative_start": 2.109376907348633, "end": **********.247254, "relative_end": **********.247254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.288105, "relative_start": 2.1502280235290527, "end": **********.288105, "relative_end": **********.288105, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.294131, "relative_start": 2.1562540531158447, "end": **********.294131, "relative_end": **********.294131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.301014, "relative_start": 2.1631369590759277, "end": **********.301014, "relative_end": **********.301014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.311972, "relative_start": 2.1740949153900146, "end": **********.311972, "relative_end": **********.311972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.321143, "relative_start": 2.1832659244537354, "end": **********.321143, "relative_end": **********.321143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.331879, "relative_start": 2.1940019130706787, "end": **********.331879, "relative_end": **********.331879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.340276, "relative_start": 2.2023990154266357, "end": **********.340276, "relative_end": **********.340276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.350005, "relative_start": 2.212127923965454, "end": **********.350005, "relative_end": **********.350005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.369111, "relative_start": 2.231234073638916, "end": **********.369111, "relative_end": **********.369111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.382732, "relative_start": 2.2448549270629883, "end": **********.382732, "relative_end": **********.382732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.410699, "relative_start": 2.2728219032287598, "end": **********.410699, "relative_end": **********.410699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.442191, "relative_start": 2.304313898086548, "end": **********.442191, "relative_end": **********.442191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.453097, "relative_start": 2.3152201175689697, "end": **********.453097, "relative_end": **********.453097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.488832, "relative_start": 2.350955009460449, "end": **********.488832, "relative_end": **********.488832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.language-switcher", "start": **********.533094, "relative_start": 2.395216941833496, "end": **********.533094, "relative_end": **********.533094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.currency-switcher", "start": **********.533856, "relative_start": 2.3959789276123047, "end": **********.533856, "relative_end": **********.533856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::views.ecommerce.includes.mini-cart", "start": **********.534486, "relative_start": 2.39660906791687, "end": **********.534486, "relative_end": **********.534486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.mobile.categories-tab-content", "start": **********.537122, "relative_start": 2.399245023727417, "end": **********.537122, "relative_end": **********.537122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.categories-dropdown-mobile", "start": **********.537869, "relative_start": 2.399991989135742, "end": **********.537869, "relative_end": **********.537869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.footer", "start": **********.541872, "relative_start": 2.4039950370788574, "end": **********.541872, "relative_end": **********.541872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/site-info/templates.frontend", "start": **********.553366, "relative_start": 2.4154889583587646, "end": **********.553366, "relative_end": **********.553366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/custom-menu/templates.frontend", "start": **********.562149, "relative_start": 2.424272060394287, "end": **********.562149, "relative_end": **********.562149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.footer-menu", "start": **********.578829, "relative_start": 2.4409520626068115, "end": **********.578829, "relative_end": **********.578829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/custom-menu/templates.frontend", "start": **********.598918, "relative_start": 2.46104097366333, "end": **********.598918, "relative_end": **********.598918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.footer-menu", "start": **********.603294, "relative_start": 2.46541690826416, "end": **********.603294, "relative_end": **********.603294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/custom-menu/templates.frontend", "start": **********.623186, "relative_start": 2.48530912399292, "end": **********.623186, "relative_end": **********.623186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.footer-menu", "start": **********.6254, "relative_start": 2.487523078918457, "end": **********.6254, "relative_end": **********.6254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::31ae887b48bc7956b164b04a31b206f3", "start": **********.628237, "relative_start": 2.4903600215911865, "end": **********.628237, "relative_end": **********.628237, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1001eded5b0608646935941ff416e315", "start": **********.631049, "relative_start": 2.4931719303131104, "end": **********.631049, "relative_end": **********.631049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::950abe7b40c10237079259e7602acfec", "start": **********.633645, "relative_start": 2.4957680702209473, "end": **********.633645, "relative_end": **********.633645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7e87b9b6cf2d20df0f49b400032abd2c", "start": **********.637663, "relative_start": 2.499785900115967, "end": **********.637663, "relative_end": **********.637663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f418e702bf973d5917d32a22532847e3", "start": **********.641958, "relative_start": 2.5040810108184814, "end": **********.641958, "relative_end": **********.641958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/newsletter/templates.frontend", "start": **********.657896, "relative_start": 2.520019054412842, "end": **********.657896, "relative_end": **********.657896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.681139, "relative_start": 2.543262004852295, "end": **********.681139, "relative_end": **********.681139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": **********.682344, "relative_start": 2.544466972351074, "end": **********.682344, "relative_end": **********.682344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.682819, "relative_start": 2.5449419021606445, "end": **********.682819, "relative_end": **********.682819, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.684074, "relative_start": 2.546196937561035, "end": **********.684074, "relative_end": **********.684074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.685099, "relative_start": 2.5472218990325928, "end": **********.685099, "relative_end": **********.685099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.685841, "relative_start": 2.547964096069336, "end": **********.685841, "relative_end": **********.685841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.686589, "relative_start": 2.5487120151519775, "end": **********.686589, "relative_end": **********.686589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.687313, "relative_start": 2.549436092376709, "end": **********.687313, "relative_end": **********.687313, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.688007, "relative_start": 2.5501301288604736, "end": **********.688007, "relative_end": **********.688007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.688789, "relative_start": 2.5509119033813477, "end": **********.688789, "relative_end": **********.688789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.68918, "relative_start": 2.551302909851074, "end": **********.68918, "relative_end": **********.68918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.689624, "relative_start": 2.5517470836639404, "end": **********.689624, "relative_end": **********.689624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": **********.690258, "relative_start": 2.5523810386657715, "end": **********.690258, "relative_end": **********.690258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.691025, "relative_start": 2.553148031234741, "end": **********.691025, "relative_end": **********.691025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.691758, "relative_start": 2.5538809299468994, "end": **********.691758, "relative_end": **********.691758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.692137, "relative_start": 2.554260015487671, "end": **********.692137, "relative_end": **********.692137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.692544, "relative_start": 2.5546669960021973, "end": **********.692544, "relative_end": **********.692544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.69305, "relative_start": 2.555172920227051, "end": **********.69305, "relative_end": **********.69305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.693603, "relative_start": 2.5557260513305664, "end": **********.693603, "relative_end": **********.693603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.694302, "relative_start": 2.556425094604492, "end": **********.694302, "relative_end": **********.694302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.694671, "relative_start": 2.5567939281463623, "end": **********.694671, "relative_end": **********.694671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.695071, "relative_start": 2.5571939945220947, "end": **********.695071, "relative_end": **********.695071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.695562, "relative_start": 2.557684898376465, "end": **********.695562, "relative_end": **********.695562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.69613, "relative_start": 2.558253049850464, "end": **********.69613, "relative_end": **********.69613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.696833, "relative_start": 2.5589559078216553, "end": **********.696833, "relative_end": **********.696833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.69721, "relative_start": 2.559333086013794, "end": **********.69721, "relative_end": **********.69721, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.697635, "relative_start": 2.559757947921753, "end": **********.697635, "relative_end": **********.697635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.698158, "relative_start": 2.5602810382843018, "end": **********.698158, "relative_end": **********.698158, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.698715, "relative_start": 2.560837984085083, "end": **********.698715, "relative_end": **********.698715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.699431, "relative_start": 2.561553955078125, "end": **********.699431, "relative_end": **********.699431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.699791, "relative_start": 2.5619139671325684, "end": **********.699791, "relative_end": **********.699791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.700194, "relative_start": 2.56231689453125, "end": **********.700194, "relative_end": **********.700194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.700698, "relative_start": 2.5628209114074707, "end": **********.700698, "relative_end": **********.700698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.701404, "relative_start": 2.5635271072387695, "end": **********.701404, "relative_end": **********.701404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.702165, "relative_start": 2.5642879009246826, "end": **********.702165, "relative_end": **********.702165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.702534, "relative_start": 2.564656972885132, "end": **********.702534, "relative_end": **********.702534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.702843, "relative_start": 2.5649659633636475, "end": **********.702843, "relative_end": **********.702843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": **********.703522, "relative_start": 2.5656449794769287, "end": **********.703522, "relative_end": **********.703522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.704161, "relative_start": 2.566283941268921, "end": **********.704161, "relative_end": **********.704161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.706641, "relative_start": 2.5687639713287354, "end": **********.706641, "relative_end": **********.706641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/cta-contact/templates.frontend", "start": **********.710788, "relative_start": 2.572911024093628, "end": **********.710788, "relative_end": **********.710788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/reviews-logos/templates.frontend", "start": **********.712899, "relative_start": 2.575021982192993, "end": **********.712899, "relative_end": **********.712899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::layouts.base", "start": **********.714664, "relative_start": 2.576786994934082, "end": **********.714664, "relative_end": **********.714664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.header", "start": **********.71968, "relative_start": 2.5818030834198, "end": **********.71968, "relative_end": **********.71968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.scroll-top", "start": **********.738121, "relative_start": 2.6002440452575684, "end": **********.738121, "relative_end": **********.738121, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.navigation-bar", "start": **********.73939, "relative_start": 2.601512908935547, "end": **********.73939, "relative_end": **********.73939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.footer", "start": **********.742563, "relative_start": 2.6046860218048096, "end": **********.742563, "relative_end": **********.742563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::fronts.toast-notification", "start": **********.743989, "relative_start": 2.606112003326416, "end": **********.743989, "relative_end": **********.743989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "53MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException", "message": "The route themes/ninico/plugins/bootstrap/bootstrap.min.css.map could not be found.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php", "line": 45, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Routing/RouteCollection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>162</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">handleMatchedRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Routing\\AbstractRouteCollection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>763</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">match</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Routing\\RouteCollection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">findRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">platform/core/js-validation/src/RemoteValidationMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Shaqi\\JsValidation\\RemoteValidationMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            return $this->getRouteForMethods($request, $others);\n", "        }\n", "\n", "        throw new NotFoundHttpException(sprintf(\n", "            'The route %s could not be found.',\n", "            $request->path()\n", "        ));\n"], "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FAbstractRouteCollection.php&line=45", "ajax": false, "filename": "AbstractRouteCollection.php", "line": "45"}}]}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "lcoal", "Debug Mode": "Enabled", "URL": "tesmods.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 144, "nb_templates": 144, "templates": [{"name": "1x theme.ninico::views.404", "param_count": null, "params": [], "start": **********.24281, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/views/404.blade.phptheme.ninico::views.404", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fviews%2F404.blade.php&line=1", "ajax": false, "filename": "404.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::views.404"}, {"name": "1x theme.ninico::partials.headers.default", "param_count": null, "params": [], "start": **********.257489, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.phptheme.ninico::partials.headers.default", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheaders%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.headers.default"}, {"name": "1x theme.ninico::partials.header-top", "param_count": null, "params": [], "start": **********.257894, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-top.blade.phptheme.ninico::partials.header-top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheader-top.blade.php&line=1", "ajax": false, "filename": "header-top.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.header-top"}, {"name": "2x theme.ninico::partials.currency-switcher", "param_count": null, "params": [], "start": **********.276985, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/currency-switcher.blade.phptheme.ninico::partials.currency-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fcurrency-switcher.blade.php&line=1", "ajax": false, "filename": "currency-switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.ninico::partials.currency-switcher"}, {"name": "2x theme.ninico::partials.language-switcher", "param_count": null, "params": [], "start": **********.277422, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/language-switcher.blade.phptheme.ninico::partials.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.ninico::partials.language-switcher"}, {"name": "1x __components::0226480071dd0e74b211507465ab2265", "param_count": null, "params": [], "start": **********.281412, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/0226480071dd0e74b211507465ab2265.blade.php__components::0226480071dd0e74b211507465ab2265", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F0226480071dd0e74b211507465ab2265.blade.php&line=1", "ajax": false, "filename": "0226480071dd0e74b211507465ab2265.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0226480071dd0e74b211507465ab2265"}, {"name": "1x __components::6112de9dcda50c4e5e8edc1897f5a562", "param_count": null, "params": [], "start": **********.282079, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/6112de9dcda50c4e5e8edc1897f5a562.blade.php__components::6112de9dcda50c4e5e8edc1897f5a562", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F6112de9dcda50c4e5e8edc1897f5a562.blade.php&line=1", "ajax": false, "filename": "6112de9dcda50c4e5e8edc1897f5a562.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6112de9dcda50c4e5e8edc1897f5a562"}, {"name": "1x __components::08a04217c3e416224df63d8e9dd782a7", "param_count": null, "params": [], "start": **********.282985, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/08a04217c3e416224df63d8e9dd782a7.blade.php__components::08a04217c3e416224df63d8e9dd782a7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F08a04217c3e416224df63d8e9dd782a7.blade.php&line=1", "ajax": false, "filename": "08a04217c3e416224df63d8e9dd782a7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::08a04217c3e416224df63d8e9dd782a7"}, {"name": "1x __components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "param_count": null, "params": [], "start": **********.283692, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php__components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php&line=1", "ajax": false, "filename": "3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3fb0a03fd317b61c1c5c6c5fdfca9fed"}, {"name": "1x __components::19d2357ba0f51b29e340109a50dc8d90", "param_count": null, "params": [], "start": **********.28435, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/19d2357ba0f51b29e340109a50dc8d90.blade.php__components::19d2357ba0f51b29e340109a50dc8d90", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F19d2357ba0f51b29e340109a50dc8d90.blade.php&line=1", "ajax": false, "filename": "19d2357ba0f51b29e340109a50dc8d90.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::19d2357ba0f51b29e340109a50dc8d90"}, {"name": "4x theme.ninico::partials.logo", "param_count": null, "params": [], "start": **********.284664, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/logo.blade.phptheme.ninico::partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 4, "name_original": "theme.ninico::partials.logo"}, {"name": "57x theme.ninico::partials.menu", "param_count": null, "params": [], "start": **********.471357, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.phptheme.ninico::partials.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 57, "name_original": "theme.ninico::partials.menu"}, {"name": "2x theme.ninico::partials.header-meta", "param_count": null, "params": [], "start": **********.094272, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-meta.blade.phptheme.ninico::partials.header-meta", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheader-meta.blade.php&line=1", "ajax": false, "filename": "header-meta.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.ninico::partials.header-meta"}, {"name": "1x theme.ninico::partials.navbar", "param_count": null, "params": [], "start": **********.10061, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/navbar.blade.phptheme.ninico::partials.navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.navbar"}, {"name": "1x theme.ninico::partials.header-middle", "param_count": null, "params": [], "start": **********.102091, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-middle.blade.phptheme.ninico::partials.header-middle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheader-middle.blade.php&line=1", "ajax": false, "filename": "header-middle.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.header-middle"}, {"name": "1x theme.ninico::partials.mobile.menu-tab-content", "param_count": null, "params": [], "start": **********.111911, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/mobile/menu-tab-content.blade.phptheme.ninico::partials.mobile.menu-tab-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fmobile%2Fmenu-tab-content.blade.php&line=1", "ajax": false, "filename": "menu-tab-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.mobile.menu-tab-content"}, {"name": "1x theme.ninico::partials.mobile-menu", "param_count": null, "params": [], "start": **********.120721, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/mobile-menu.blade.phptheme.ninico::partials.mobile-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fmobile-menu.blade.php&line=1", "ajax": false, "filename": "mobile-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.mobile-menu"}, {"name": "1x theme.ninico::views.ecommerce.includes.mini-cart", "param_count": null, "params": [], "start": **********.534445, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/views/ecommerce/includes/mini-cart.blade.phptheme.ninico::views.ecommerce.includes.mini-cart", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fviews%2Fecommerce%2Fincludes%2Fmini-cart.blade.php&line=1", "ajax": false, "filename": "mini-cart.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::views.ecommerce.includes.mini-cart"}, {"name": "1x theme.ninico::partials.mobile.categories-tab-content", "param_count": null, "params": [], "start": **********.537087, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/mobile/categories-tab-content.blade.phptheme.ninico::partials.mobile.categories-tab-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fmobile%2Fcategories-tab-content.blade.php&line=1", "ajax": false, "filename": "categories-tab-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.mobile.categories-tab-content"}, {"name": "1x theme.ninico::partials.categories-dropdown-mobile", "param_count": null, "params": [], "start": **********.537818, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/categories-dropdown-mobile.blade.phptheme.ninico::partials.categories-dropdown-mobile", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fcategories-dropdown-mobile.blade.php&line=1", "ajax": false, "filename": "categories-dropdown-mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.categories-dropdown-mobile"}, {"name": "1x theme.ninico::partials.footer", "param_count": null, "params": [], "start": **********.541839, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/footer.blade.phptheme.ninico::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.footer"}, {"name": "1x theme.ninico::/../widgets/site-info/templates.frontend", "param_count": null, "params": [], "start": **********.553328, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/site-info/templates/frontend.blade.phptheme.ninico::/../widgets/site-info/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Fsite-info%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::/../widgets/site-info/templates.frontend"}, {"name": "3x theme.ninico::/../widgets/custom-menu/templates.frontend", "param_count": null, "params": [], "start": **********.562111, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/custom-menu/templates/frontend.blade.phptheme.ninico::/../widgets/custom-menu/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Fcustom-menu%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 3, "name_original": "theme.ninico::/../widgets/custom-menu/templates.frontend"}, {"name": "3x theme.ninico::partials.footer-menu", "param_count": null, "params": [], "start": **********.578791, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/footer-menu.blade.phptheme.ninico::partials.footer-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Ffooter-menu.blade.php&line=1", "ajax": false, "filename": "footer-menu.blade.php", "line": "?"}, "render_count": 3, "name_original": "theme.ninico::partials.footer-menu"}, {"name": "1x __components::31ae887b48bc7956b164b04a31b206f3", "param_count": null, "params": [], "start": **********.628217, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/31ae887b48bc7956b164b04a31b206f3.blade.php__components::31ae887b48bc7956b164b04a31b206f3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F31ae887b48bc7956b164b04a31b206f3.blade.php&line=1", "ajax": false, "filename": "31ae887b48bc7956b164b04a31b206f3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::31ae887b48bc7956b164b04a31b206f3"}, {"name": "1x __components::1001eded5b0608646935941ff416e315", "param_count": null, "params": [], "start": **********.631029, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/1001eded5b0608646935941ff416e315.blade.php__components::1001eded5b0608646935941ff416e315", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F1001eded5b0608646935941ff416e315.blade.php&line=1", "ajax": false, "filename": "1001eded5b0608646935941ff416e315.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1001eded5b0608646935941ff416e315"}, {"name": "1x __components::950abe7b40c10237079259e7602acfec", "param_count": null, "params": [], "start": **********.633625, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/950abe7b40c10237079259e7602acfec.blade.php__components::950abe7b40c10237079259e7602acfec", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F950abe7b40c10237079259e7602acfec.blade.php&line=1", "ajax": false, "filename": "950abe7b40c10237079259e7602acfec.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::950abe7b40c10237079259e7602acfec"}, {"name": "1x __components::7e87b9b6cf2d20df0f49b400032abd2c", "param_count": null, "params": [], "start": **********.637633, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/7e87b9b6cf2d20df0f49b400032abd2c.blade.php__components::7e87b9b6cf2d20df0f49b400032abd2c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F7e87b9b6cf2d20df0f49b400032abd2c.blade.php&line=1", "ajax": false, "filename": "7e87b9b6cf2d20df0f49b400032abd2c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7e87b9b6cf2d20df0f49b400032abd2c"}, {"name": "1x __components::f418e702bf973d5917d32a22532847e3", "param_count": null, "params": [], "start": **********.641923, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/f418e702bf973d5917d32a22532847e3.blade.php__components::f418e702bf973d5917d32a22532847e3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Ff418e702bf973d5917d32a22532847e3.blade.php&line=1", "ajax": false, "filename": "f418e702bf973d5917d32a22532847e3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f418e702bf973d5917d32a22532847e3"}, {"name": "1x theme.ninico::/../widgets/newsletter/templates.frontend", "param_count": null, "params": [], "start": **********.657874, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/newsletter/templates/frontend.blade.phptheme.ninico::/../widgets/newsletter/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Fnewsletter%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::/../widgets/newsletter/templates.frontend"}, {"name": "2x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.681116, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": **********.682325, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php&line=1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "6x core/base::forms.fields.html", "param_count": null, "params": [], "start": **********.682801, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php&line=1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 6, "name_original": "core/base::forms.fields.html"}, {"name": "7x a74ad8dfacd4f985eb3977517615ce25::form.field", "param_count": null, "params": [], "start": **********.683957, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/form/field.blade.phpa74ad8dfacd4f985eb3977517615ce25::form.field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 7, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form.field"}, {"name": "7x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.685049, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 7, "name_original": "core/base::forms.partials.help-block"}, {"name": "7x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.685798, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php&line=1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 7, "name_original": "core/base::forms.partials.errors"}, {"name": "8x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.686544, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php&line=1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 8, "name_original": "core/base::forms.columns.column-span"}, {"name": "1x core/base::forms.fields.email", "param_count": null, "params": [], "start": **********.690225, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/fields/email.blade.phpcore/base::forms.fields.email", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Femail.blade.php&line=1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.email"}, {"name": "1x laravel-form-builder::button", "param_count": null, "params": [], "start": **********.703485, "type": "php", "hash": "phpD:\\laragon\\www\\tesmods\\platform\\packages\\form-builder\\src/../resources/views/button.phplaravel-form-builder::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fform-builder%2Fresources%2Fviews%2Fbutton.php&line=1", "ajax": false, "filename": "button.php", "line": "?"}, "render_count": 1, "name_original": "laravel-form-builder::button"}, {"name": "1x theme.ninico::/../widgets/cta-contact/templates.frontend", "param_count": null, "params": [], "start": **********.710767, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/cta-contact/templates/frontend.blade.phptheme.ninico::/../widgets/cta-contact/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Fcta-contact%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::/../widgets/cta-contact/templates.frontend"}, {"name": "1x theme.ninico::/../widgets/reviews-logos/templates.frontend", "param_count": null, "params": [], "start": **********.712879, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/reviews-logos/templates/frontend.blade.phptheme.ninico::/../widgets/reviews-logos/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Freviews-logos%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::/../widgets/reviews-logos/templates.frontend"}, {"name": "1x theme.ninico::layouts.base", "param_count": null, "params": [], "start": **********.714643, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/layouts/base.blade.phptheme.ninico::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::layouts.base"}, {"name": "1x packages/theme::partials.header", "param_count": null, "params": [], "start": **********.719637, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.header"}, {"name": "1x theme.ninico::partials.scroll-top", "param_count": null, "params": [], "start": **********.73806, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/scroll-top.blade.phptheme.ninico::partials.scroll-top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fscroll-top.blade.php&line=1", "ajax": false, "filename": "scroll-top.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.scroll-top"}, {"name": "1x theme.ninico::partials.navigation-bar", "param_count": null, "params": [], "start": **********.73935, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/navigation-bar.blade.phptheme.ninico::partials.navigation-bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fnavigation-bar.blade.php&line=1", "ajax": false, "filename": "navigation-bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.navigation-bar"}, {"name": "1x packages/theme::partials.footer", "param_count": null, "params": [], "start": **********.742542, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.footer"}, {"name": "1x packages/theme::fronts.toast-notification", "param_count": null, "params": [], "start": **********.74397, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/packages/theme/resources/views/fronts/toast-notification.blade.phppackages/theme::fronts.toast-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Ffronts%2Ftoast-notification.blade.php&line=1", "ajax": false, "filename": "toast-notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::fronts.toast-notification"}]}, "queries": {"count": 168, "nb_statements": 168, "nb_visible_statements": 168, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.10832000000000001, "accumulated_duration_str": "108ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 68 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 134}, {"index": 19, "namespace": null, "name": "platform/packages/theme/src/Theme.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\theme\\src\\Theme.php", "line": 346}, {"index": 20, "namespace": null, "name": "platform/packages/theme/src/Theme.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\theme\\src\\Theme.php", "line": 895}], "start": **********.250773, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 0, "width_percent": 1.486}, {"sql": "select * from `announcements` where 1 = 1 and `is_active` = 1 and (`start_date` is null or `start_date` <= '2025-08-21 15:40:29') and (`end_date` is null or `end_date` >= '2025-08-21 15:40:29') order by RAND()", "type": "query", "params": [], "bindings": [1, "2025-08-21 15:40:29", "2025-08-21 15:40:29"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/announcement/src/AnnouncementHelper.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\announcement\\src\\AnnouncementHelper.php", "line": 59}, {"index": 17, "namespace": "view", "name": "theme.ninico::partials.header-top", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-top.blade.php", "line": 5}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.259026, "duration": 0.01643, "duration_str": "16.43ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 1.486, "width_percent": 15.168}, {"sql": "select * from `menus` where `status` = 'published' and exists (select * from `language_meta` where `menus`.`id` = `language_meta`.`reference_id` and `language_meta`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\Menu' and `lang_meta_code` = 'en_US' and `lang_meta_code` = 'en_US')", "type": "query", "params": [], "bindings": ["published", "Shaqi\\Menu\\Models\\Menu", "en_US", "en_US"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 17, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}, {"index": 18, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 170}, {"index": 20, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}], "start": **********.307609, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 16.654, "width_percent": 0.499}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`menu_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 170}], "start": **********.3110719, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 17.153, "width_percent": 1.191}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` in (25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 44, 51, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 66, 93, 95, 128, 129, 131, 132, 133, 135, 136, 137, 138, 139, 141, 142, 143, 144, 145, 146, 147, 148, 149, 151, 152, 153, 154, 155, 156, 158, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 201, 202, 203, 204, 205, 206, 209, 210, 211, 212, 213, 214, 215, 216) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}], "start": **********.337428, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 18.344, "width_percent": 0.812}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (42, 44, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 66, 93, 95, 129, 131, 132, 133, 135, 136, 137, 138, 139, 141, 142, 143, 144, 145, 146, 147, 148, 149, 151, 152, 153, 154, 155, 156, 158, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 201, 202, 203, 204, 205, 206, 209, 210, 211, 212, 213, 214, 215, 216) and `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode'", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 34, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}], "start": **********.35663, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 19.156, "width_percent": 2.262}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 44, 51, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 66, 93, 95, 128, 129, 131, 132, 133, 135, 136, 137, 138, 139, 141, 142, 143, 144, 145, 146, 147, 148, 149, 151, 152, 153, 154, 155, 156, 158, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 201, 202, 203, 204, 205, 206, 209, 210, 211, 212, 213, 214, 215, 216) and `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode'", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}], "start": **********.40322, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 21.418, "width_percent": 0.609}, {"sql": "select * from `menu_locations` where `menu_locations`.`menu_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 170}], "start": **********.4360461, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 22.027, "width_percent": 0.452}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (19, 21, 213)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 180}, {"index": 26, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.458796, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 22.48, "width_percent": 0.462}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (19, 21, 213) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 30, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 180}, {"index": 32, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}], "start": **********.460724, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 22.941, "width_percent": 0.323}, {"sql": "select * from `pages` where `pages`.`id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 180}, {"index": 26, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.465554, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 23.264, "width_percent": 0.739}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (12) and `slugs`.`reference_type` = 'Shaqi\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 30, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 180}, {"index": 32, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}], "start": **********.46761, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 24.003, "width_percent": 0.388}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (24, 26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4794018, "duration": 0.00515, "duration_str": "5.15ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 24.391, "width_percent": 4.754}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (24, 26) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.4870338, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 29.145, "width_percent": 0.545}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 201 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [201], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4920518, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 29.69, "width_percent": 0.563}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 202 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [202], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4977949, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 30.253, "width_percent": 0.379}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 202 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 202], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.501965, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 30.631, "width_percent": 0.692}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (41)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.506063, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 31.324, "width_percent": 0.425}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (41) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.507834, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 31.749, "width_percent": 0.286}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 209 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 209], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.5131478, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 32.035, "width_percent": 0.36}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 203 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [203], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5156171, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 32.395, "width_percent": 0.452}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 203 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 203], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.521985, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 32.847, "width_percent": 0.462}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (97)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.525069, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 33.309, "width_percent": 0.305}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (97) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.5263891, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 33.613, "width_percent": 0.277}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 206 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 206], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.530321, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 33.89, "width_percent": 0.342}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 204 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [204], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.532876, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 34.232, "width_percent": 0.443}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 204 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 204], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.537901, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 34.675, "width_percent": 3.259}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (105)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.54461, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 37.934, "width_percent": 0.415}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (105) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.546663, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 38.349, "width_percent": 0.369}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 205 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 205], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.552465, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 38.719, "width_percent": 0.314}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5650911, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 39.032, "width_percent": 0.582}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (23) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.567512, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 39.614, "width_percent": 0.434}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 185 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [185], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.5733278, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 40.048, "width_percent": 1.532}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (376)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.587751, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 41.581, "width_percent": 1.505}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (376) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.591891, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 43.085, "width_percent": 0.462}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 165 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [165], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.59642, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 43.547, "width_percent": 0.351}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 165 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 165], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.600673, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 43.898, "width_percent": 0.535}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 186 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [186], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.609554, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 44.433, "width_percent": 0.415}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 186 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 186], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.6129448, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 44.849, "width_percent": 0.36}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (34, 41)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.616765, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 45.209, "width_percent": 0.434}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (34, 41) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.622407, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 45.643, "width_percent": 0.942}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 42 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 42], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.628474, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 46.584, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 53 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.631982, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 46.88, "width_percent": 0.258}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 187 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [187], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6345499, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 47.138, "width_percent": 0.388}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 187 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 187], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.637701, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 47.526, "width_percent": 0.277}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (47, 48, 49)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.640645, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 47.803, "width_percent": 0.286}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (47, 48, 49) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.642057, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 48.089, "width_percent": 0.231}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 54 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.647562, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 48.32, "width_percent": 0.582}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 56 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 56], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.654791, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 48.901, "width_percent": 0.452}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 57 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.658761, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 49.354, "width_percent": 0.305}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 188 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [188], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.661058, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 49.658, "width_percent": 0.36}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 188 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 188], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.664424, "duration": 0.0030499999999999998, "duration_str": "3.05ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 50.018, "width_percent": 2.816}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (60, 62, 65, 67)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6726081, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 52.834, "width_percent": 0.425}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (60, 62, 65, 67) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.67436, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 53.259, "width_percent": 0.323}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 60 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 60], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.679787, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 53.582, "width_percent": 0.554}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 62 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 62], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.6847749, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 54.136, "width_percent": 0.397}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 63 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.6885011, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 54.533, "width_percent": 0.314}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 65 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 65], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.692164, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 54.847, "width_percent": 0.305}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 189 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [189], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.694475, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 55.151, "width_percent": 0.323}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 189 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 189], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.697643, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 55.475, "width_percent": 0.258}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (70)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.700409, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 55.733, "width_percent": 0.332}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (70) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.701822, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 56.065, "width_percent": 0.286}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 58 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.705774, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 56.352, "width_percent": 0.314}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 190 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [190], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.708117, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 56.665, "width_percent": 0.286}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 190 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 190], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.711235, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 56.952, "width_percent": 0.342}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (74, 78)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.714799, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 57.293, "width_percent": 0.499}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (74, 78) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.7174978, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 57.792, "width_percent": 0.729}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 66 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 66], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.7230349, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 58.521, "width_percent": 0.351}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 64 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.729692, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 58.872, "width_percent": 0.554}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 166 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 166], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.733974, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 59.426, "width_percent": 0.351}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 44 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.736712, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 59.777, "width_percent": 0.388}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (380, 381, 383, 382, 384, 385, 386, 387)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.747415, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 60.164, "width_percent": 0.499}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (380, 381, 382, 383, 384, 385, 386, 387) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.751261, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 60.663, "width_percent": 0.342}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 170 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 170], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.757832, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 61.004, "width_percent": 0.286}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 169 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 169], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.761641, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 61.291, "width_percent": 0.369}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 168 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 168], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.7659261, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 61.66, "width_percent": 0.286}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 167 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.770467, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 61.946, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 172 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 172], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.77446, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 62.242, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 171 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 171], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.778756, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 62.537, "width_percent": 0.323}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 174 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 174], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.783067, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 62.86, "width_percent": 0.748}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 173 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 173], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.787293, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 63.608, "width_percent": 0.295}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (28, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.7945821, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 63.903, "width_percent": 0.286}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (27, 28) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.795953, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 64.189, "width_percent": 0.258}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 212 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.7989519, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 64.448, "width_percent": 0.36}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 216 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [216], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.805844, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 64.808, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 216 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 216], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.8087208, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 65.103, "width_percent": 0.212}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (145)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.811239, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 65.316, "width_percent": 0.24}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (145) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.812926, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 65.556, "width_percent": 0.406}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 213 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 213], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.8179538, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 65.962, "width_percent": 0.379}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 215 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [215], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8204498, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 66.34, "width_percent": 0.24}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 215 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 215], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.8231, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 66.581, "width_percent": 0.258}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (149)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.825636, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 66.839, "width_percent": 0.314}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (149) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.8268309, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 67.153, "width_percent": 0.24}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 211 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 211], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.8306, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 67.393, "width_percent": 0.314}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 214 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [214], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.833077, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 67.707, "width_percent": 0.332}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 214 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 214], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.838469, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 68.039, "width_percent": 0.314}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (168)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.841583, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 68.353, "width_percent": 0.277}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (168) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.8428292, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 68.63, "width_percent": 0.249}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 210 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 210], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.8472629, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 68.879, "width_percent": 0.397}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (264, 271)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.8591568, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 69.276, "width_percent": 0.388}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (264, 271) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8608272, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 69.664, "width_percent": 0.258}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.863339, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 69.922, "width_percent": 0.332}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.869045, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 70.255, "width_percent": 0.415}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.87148, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 70.67, "width_percent": 0.258}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (272, 273, 274)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.87376, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 70.929, "width_percent": 0.295}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (272, 273, 274) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.87467, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 71.224, "width_percent": 0.277}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.878541, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 71.501, "width_percent": 0.369}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.884502, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 71.87, "width_percent": 0.517}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.890855, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 72.387, "width_percent": 0.323}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.893165, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 72.71, "width_percent": 0.314}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.895668, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 73.024, "width_percent": 0.295}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (275, 276, 277, 278, 279)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.898429, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 73.32, "width_percent": 0.332}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (275, 276, 277, 278, 279) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.899873, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 73.652, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9044929, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 73.948, "width_percent": 0.369}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.907622, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 74.317, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.910788, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 74.612, "width_percent": 0.342}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9183059, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 74.954, "width_percent": 0.517}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.922472, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 75.471, "width_percent": 0.351}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.925333, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 75.822, "width_percent": 0.351}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927828, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 76.172, "width_percent": 0.277}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (280, 281, 282, 284, 283, 285, 286, 287, 288)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.930815, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 76.449, "width_percent": 0.314}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (280, 281, 282, 283, 284, 285, 286, 287, 288) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.93241, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 76.763, "width_percent": 0.369}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.939195, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 77.133, "width_percent": 0.351}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9422162, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 77.483, "width_percent": 0.249}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.948512, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 77.733, "width_percent": 0.572}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9526849, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 78.305, "width_percent": 0.397}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.955846, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 78.702, "width_percent": 0.268}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.958955, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 78.97, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9639132, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 79.265, "width_percent": 0.258}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9675891, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 79.524, "width_percent": 0.305}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9705732, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 79.828, "width_percent": 0.277}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.972892, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 80.105, "width_percent": 0.323}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9775, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 80.428, "width_percent": 0.535}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (289, 290, 291, 292, 293, 294, 295)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9825022, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 80.964, "width_percent": 0.739}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (289, 290, 291, 292, 293, 294, 295) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.984909, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 81.702, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9918878, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 81.998, "width_percent": 0.388}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9951, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 82.386, "width_percent": 0.277}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.998176, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 82.662, "width_percent": 0.249}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.001721, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 82.912, "width_percent": 0.397}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0052521, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 83.309, "width_percent": 0.314}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.00877, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 83.623, "width_percent": 0.268}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.011972, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 83.89, "width_percent": 0.286}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.01395, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 84.177, "width_percent": 0.591}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.017351, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 84.767, "width_percent": 0.369}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (296)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.019981, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 85.137, "width_percent": 0.323}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (296) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0207398, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 85.46, "width_percent": 0.222}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0240781, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 85.681, "width_percent": 0.295}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.02588, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 85.977, "width_percent": 0.286}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0281682, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 86.263, "width_percent": 0.258}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (297, 298, 299)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.030507, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 86.521, "width_percent": 0.268}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (297, 298, 299) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.031419, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 86.789, "width_percent": 0.268}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0362751, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 87.057, "width_percent": 0.295}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0393791, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 87.352, "width_percent": 0.258}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.042375, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 87.611, "width_percent": 0.249}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.04406, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 87.86, "width_percent": 0.295}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (388, 390, 391, 392, 389, 393, 394, 395)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.049244, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 88.155, "width_percent": 0.517}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (388, 389, 390, 391, 392, 393, 394, 395) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0524652, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 88.672, "width_percent": 1.403}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.062576, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 90.076, "width_percent": 0.249}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.065725, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 90.325, "width_percent": 0.332}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.070469, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 90.657, "width_percent": 0.388}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0735571, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 91.045, "width_percent": 0.268}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0764349, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 91.313, "width_percent": 0.24}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.080311, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 91.553, "width_percent": 0.591}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.085001, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 92.144, "width_percent": 0.36}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.088513, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 92.504, "width_percent": 0.314}, {"sql": "select * from `widgets` where (`theme` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5456882, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 92.818, "width_percent": 0.425}, {"sql": "select * from `pages` where `pages`.`id` in (11, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.566648, "duration": 0.00663, "duration_str": "6.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 93.242, "width_percent": 6.121}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (8, 11) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.574481, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 99.363, "width_percent": 0.637}]}, "models": {"data": {"Shaqi\\Menu\\Models\\MenuNode": {"value": 280, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenuNode.php&line=1", "ajax": false, "filename": "MenuNode.php", "line": "?"}}, "Shaqi\\Slug\\Models\\Slug": {"value": 76, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Shaqi\\Ecommerce\\Models\\ProductCategory": {"value": 73, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCategory.php&line=1", "ajax": false, "filename": "ProductCategory.php", "line": "?"}}, "Shaqi\\Widget\\Models\\Widget": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fwidget%2Fsrc%2FModels%2FWidget.php&line=1", "ajax": false, "filename": "Widget.php", "line": "?"}}, "Shaqi\\Menu\\Models\\Menu": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "Shaqi\\Menu\\Models\\MenuLocation": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenuLocation.php&line=1", "ajax": false, "filename": "MenuLocation.php", "line": "?"}}, "Shaqi\\Page\\Models\\Page": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Shaqi\\Ecommerce\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 454, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "404 Not Found", "full_url": "https://tesmods.gc/themes/ninico/plugins/bootstrap/bootstrap.min.css.map", "action_name": null, "controller_action": null, "duration": "2.63s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-290806554 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-290806554\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1725378288 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1725378288\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">tesmods.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2576 characters\">shaqi_footprints_cookie=eyJpdiI6ImxHZGRqajhxQkFDcUMxb2VsdW16d1E9PSIsInZhbHVlIjoiajFBNUdGa2xjRmJPdHdMWFpTRiswdk14REJmbFh3TGp4V0JsUjZpSEhsSENiRWhSRlgwMEl3MTByNEs4UG5qanlaUEc2UlpiOFZVY0tad0g4NnRxc25hZFMvREs4K3U2NEkwcFZBQW9NK3FSdXBpZHpCYnkvUVVRU0xWMEEzMzUiLCJtYWMiOiI2OTk3Mzk3NTRlMzM2ZTdiMDExZWJmODJhZGExOTY4MmZhNmMzNjM3MDM1MWRmMTllMjY5NTUwYjBiODZjZjg1IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImF4TmlBNFJ3RTVxNFBhWUFLZkNIQWc9PSIsInZhbHVlIjoiVmxhcXR5bU4wOXU5dlhCTjVpK0JWZ2xrT3pZYXkwSllnQ3V6QklLcDRYZVpBeFhmWmJ3WGJwRTA1ZlZmcGkydGdRSHB3ZGJwdUJ3Uk9TYWYvcWtmalNoWi9mWW5SN2hzZTMvanN5cTB5TzBCaGs5NkhWVjljTFRzWWozK0pxN0p4Wjk1ckREWVhleTVjQmJZQ2ttOTlwQ1RDbHhtN3ZOMldQSUhrMExhOHFIc2VCRE9mLy83UmhCSEdkc00rckR1Y3N4NkxML1FNeGZyWitHUldZUkR0eHYyZTdBbEtMRUxqbUtGWithaGJjcVBIV2ZnaElSYllTbE96aTQ5MWNMSkNBa2wwOHM3RFQ0RW83aUpxZmVtYjU4aDhjbmo1clFSWTRHa2VzbEJLOWE3d1NoeVIvQnhrYUM1ejZUUy9qTkY0UDdsb1Q5Z3dybjVuLzZIaXRET1NadjJNSTJsbzNYd2U2aTFMOTMxYTJQejIzTU1IRFFpVm5Nd0xLdUk5Yjh2YXE1S01sRUQrMDB6OXI4U2pZeS9zQTAyaVI5TTN6bWVyV25vWGVtRWlHWkhLb1crRnVXVDJRbjJkNUY0OXM1MUUxMVpiRE9jd0wyQkg0Z0FpcDlzakRlT1NNWnVvSTI4MVNZaC9GZmhuTGNLWXBZRnNTNG9nRnY5Q3RxeTBxWUVRVjNoMDUvTkV1ajRpN096ZW4wOHlBPT0iLCJtYWMiOiJkYmI3NTYwZDUxZWJjY2I0ZmUxMmQ2YmU2YzUxYmQ3MjYzMzJlZmQ1MjMyZTBhNGNlOTUyMTgxMDhjMzhlNjU1IiwidGFnIjoiIn0%3D; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlVHb3RYN0M5L2FRa3ovaExFNWVXQ3c9PSIsInZhbHVlIjoiMFU4V0hvaGRDVXI5K3RzdzdkMjZ1eDZ3cnhXQy9nRUpxekg1bFIxa0VoUTBTY2x6TTB4SlBGL1lkbVpOV2IyRitMcGsyMHFOS2ZMalRDQWZzM1g0MThCcWpheHNMSE5QT2g4ZUhhNFRxTnNUL0ZuKzhyNnpUbXI1V3JMSEhlT0laT2hXN0JUWWd4MEhXMXBhTzhaZkh2VkR4TXF4NXlDNnpUbzcxMjdnL1RwUzduZ05BMy9iTnZDNTlEdlBWaW5RNDkraERQRXI3RmgzNzEzclFrTkQ5S21kRmNWK1I0OVhGUGx2cXpzK0tJdz0iLCJtYWMiOiJhZDg0MDlkNzAxNWI5MjBiN2NlZmE5ZDRkNDExNDg3MjdiMTY1MWY0NDdjNjZkNmI5ZDA1ZTE0MWIwNzc4M2E1IiwidGFnIjoiIn0%3D; perf_dv6Tr4n=1; XSRF-TOKEN=eyJpdiI6Ilc4ZXhTVzg5dm9ZamE0UTlvRWROcWc9PSIsInZhbHVlIjoib3gwemxPNEh5OGVZVnE2VHhldHE1RVZnZkZ2NktVMVJHVHlWVFpLOGNhMDkxRWYvRG9pUzZSdkQ4N3lHK0h2OUR1V3lrYWhNQVh0UWVPaTdqTUs2YzhDMDVZaDNyMEZvZDFYS3NCd1M0TDhlaU5LR0xucU96Tng3U3pYUmU2VUYiLCJtYWMiOiI0ODYyNmMwNjg3ZWYzY2VjZTc2YWU3ZjRlYzY0YTgwNzRhYjI0MTlhNDY1NWRjNGJlNDNmMTIxMGVmODFiOTFlIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6Ii9VZ1NxOTJ5UVNhSUNCTHVad1VuN1E9PSIsInZhbHVlIjoiQ1RiWFV4NzV3UVBVWDBHOWZHekZNdlF1RHFOcGxVR0w0S2cwRFp4NDR5YlBGVVBnZVNRODVTMXQxRGFMNHFucVNILzlHczM2SElRa1RpeENwaGQxczJnWnZvbFJrQU12MFlmU0EvUkdhRnYvTVladHVHdEtOK1hLVTRTb2tnKy8iLCJtYWMiOiIzZjY1NjI3NzUxYTliYjQ0NTExODQyNjcwMzNjNDVlZjYxOTk3OWRkY2FiZTdlMzdjZjI5MmI2ZjY4YTMzZTVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-358210373 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shaqi_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImxHZGRqajhxQkFDcUMxb2VsdW16d1E9PSIsInZhbHVlIjoiajFBNUdGa2xjRmJPdHdMWFpTRiswdk14REJmbFh3TGp4V0JsUjZpSEhsSENiRWhSRlgwMEl3MTByNEs4UG5qanlaUEc2UlpiOFZVY0tad0g4NnRxc25hZFMvREs4K3U2NEkwcFZBQW9NK3FSdXBpZHpCYnkvUVVRU0xWMEEzMzUiLCJtYWMiOiI2OTk3Mzk3NTRlMzM2ZTdiMDExZWJmODJhZGExOTY4MmZhNmMzNjM3MDM1MWRmMTllMjY5NTUwYjBiODZjZjg1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shaqi_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"884 characters\">eyJpdiI6ImF4TmlBNFJ3RTVxNFBhWUFLZkNIQWc9PSIsInZhbHVlIjoiVmxhcXR5bU4wOXU5dlhCTjVpK0JWZ2xrT3pZYXkwSllnQ3V6QklLcDRYZVpBeFhmWmJ3WGJwRTA1ZlZmcGkydGdRSHB3ZGJwdUJ3Uk9TYWYvcWtmalNoWi9mWW5SN2hzZTMvanN5cTB5TzBCaGs5NkhWVjljTFRzWWozK0pxN0p4Wjk1ckREWVhleTVjQmJZQ2ttOTlwQ1RDbHhtN3ZOMldQSUhrMExhOHFIc2VCRE9mLy83UmhCSEdkc00rckR1Y3N4NkxML1FNeGZyWitHUldZUkR0eHYyZTdBbEtMRUxqbUtGWithaGJjcVBIV2ZnaElSYllTbE96aTQ5MWNMSkNBa2wwOHM3RFQ0RW83aUpxZmVtYjU4aDhjbmo1clFSWTRHa2VzbEJLOWE3d1NoeVIvQnhrYUM1ejZUUy9qTkY0UDdsb1Q5Z3dybjVuLzZIaXRET1NadjJNSTJsbzNYd2U2aTFMOTMxYTJQejIzTU1IRFFpVm5Nd0xLdUk5Yjh2YXE1S01sRUQrMDB6OXI4U2pZeS9zQTAyaVI5TTN6bWVyV25vWGVtRWlHWkhLb1crRnVXVDJRbjJkNUY0OXM1MUUxMVpiRE9jd0wyQkg0Z0FpcDlzakRlT1NNWnVvSTI4MVNZaC9GZmhuTGNLWXBZRnNTNG9nRnY5Q3RxeTBxWUVRVjNoMDUvTkV1ajRpN096ZW4wOHlBPT0iLCJtYWMiOiJkYmI3NTYwZDUxZWJjY2I0ZmUxMmQ2YmU2YzUxYmQ3MjYzMzJlZmQ1MjMyZTBhNGNlOTUyMTgxMDhjMzhlNjU1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6IlVHb3RYN0M5L2FRa3ovaExFNWVXQ3c9PSIsInZhbHVlIjoiMFU4V0hvaGRDVXI5K3RzdzdkMjZ1eDZ3cnhXQy9nRUpxekg1bFIxa0VoUTBTY2x6TTB4SlBGL1lkbVpOV2IyRitMcGsyMHFOS2ZMalRDQWZzM1g0MThCcWpheHNMSE5QT2g4ZUhhNFRxTnNUL0ZuKzhyNnpUbXI1V3JMSEhlT0laT2hXN0JUWWd4MEhXMXBhTzhaZkh2VkR4TXF4NXlDNnpUbzcxMjdnL1RwUzduZ05BMy9iTnZDNTlEdlBWaW5RNDkraERQRXI3RmgzNzEzclFrTkQ5S21kRmNWK1I0OVhGUGx2cXpzK0tJdz0iLCJtYWMiOiJhZDg0MDlkNzAxNWI5MjBiN2NlZmE5ZDRkNDExNDg3MjdiMTY1MWY0NDdjNjZkNmI5ZDA1ZTE0MWIwNzc4M2E1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilc4ZXhTVzg5dm9ZamE0UTlvRWROcWc9PSIsInZhbHVlIjoib3gwemxPNEh5OGVZVnE2VHhldHE1RVZnZkZ2NktVMVJHVHlWVFpLOGNhMDkxRWYvRG9pUzZSdkQ4N3lHK0h2OUR1V3lrYWhNQVh0UWVPaTdqTUs2YzhDMDVZaDNyMEZvZDFYS3NCd1M0TDhlaU5LR0xucU96Tng3U3pYUmU2VUYiLCJtYWMiOiI0ODYyNmMwNjg3ZWYzY2VjZTc2YWU3ZjRlYzY0YTgwNzRhYjI0MTlhNDY1NWRjNGJlNDNmMTIxMGVmODFiOTFlIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ii9VZ1NxOTJ5UVNhSUNCTHVad1VuN1E9PSIsInZhbHVlIjoiQ1RiWFV4NzV3UVBVWDBHOWZHekZNdlF1RHFOcGxVR0w0S2cwRFp4NDR5YlBGVVBnZVNRODVTMXQxRGFMNHFucVNILzlHczM2SElRa1RpeENwaGQxczJnWnZvbFJrQU12MFlmU0EvUkdhRnYvTVladHVHdEtOK1hLVTRTb2tnKy8iLCJtYWMiOiIzZjY1NjI3NzUxYTliYjQ0NTExODQyNjcwMzNjNDVlZjYxOTk3OWRkY2FiZTdlMzdjZjI5MmI2ZjY4YTMzZTVkIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-358210373\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 21 Aug 2025 15:40:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1256535174 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1256535174\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "404 Not Found", "full_url": "https://tesmods.gc/themes/ninico/plugins/bootstrap/bootstrap.min.css.map"}, "badge": "404 Not Found"}}