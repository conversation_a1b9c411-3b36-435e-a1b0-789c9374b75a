<div class="table-responsive mb-3">

    <table class="table product-table display responsive nowrap">
        <thead>
            <tr>
                <th class="text-left w-15"></th>
                <th class="text-left w-30">Part Name</th>
                <th class="text-left w-15">Part Number</th>
                <th class="text-left w-15">Price</th>
                <th class="text-center w-30">Buy</th>
            </tr>
        </thead>
        <tbody>
            <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                    <td>
                        <img src="<?php echo e(RvMedia::getImageUrl($product->image, 'small', false, RvMedia::getDefaultImage())); ?>" alt="<?php echo e($product->name); ?>" style="max-width: 100px;height: 100px;object-fit: cover;">
                    </td>
                    <td>
                        <a href="<?php echo e($product->url); ?>"><?php echo e($product->name); ?></a>
                    </td>
                    <td>
                        <?php if($part_number = $product->getMetaData('part_number', true)): ?>
                        <?php echo e($part_number); ?>

                        <?php endif; ?>
                    </td>
                    <td>
                        <?php echo $__env->make(EcommerceHelper::viewPath('includes.product-price'), [
                            'product' => $product,
                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </td>
                    <td class="text-center">
                        <?php if($product->variations()->exists() || $product->options()->exists()): ?>
                            
                            <div class="d-flex align-items-center justify-content-end gap-2">
                                <a data-id="<?php echo e($product->slug); ?>" href="#" data-url="<?php echo e(route('public.ajax.quick-shop', $product->slug)); ?>" class="btn button-quick-shop">
                                    <i class="fal fa-shopping-cart"></i>
                                    <?php echo e(__('Select options')); ?>

                                </a>
                            </div>
                        <?php else: ?>
                            
                            <form
                                class="cart-form"
                                method="POST"
                                action="<?php echo e(route('public.cart.add-to-cart')); ?>"
                            >
                                <?php echo csrf_field(); ?>
                                <input
                                    class="hidden-product-id"
                                    name="id"
                                    type="hidden"
                                    value="<?php echo e($product->is_variation || !$product->defaultVariation->product_id ? $product->id : $product->defaultVariation->product_id); ?>"
                                />

                                <div class="tpproduct-details__count d-flex align-items-center justify-content-end flex-wrap gap-2">
                                    <?php if(EcommerceHelper::isCartEnabled()): ?>
                                        <div class="tpproduct-details__quantity">
                                            <span class="cart-minus"><i class="far fa-minus"></i></span>
                                            <input
                                                class="tp-cart-input"
                                                name="qty"
                                                type="text"
                                                value="1"
                                            >
                                            <span class="cart-plus"><i class="far fa-plus"></i></span>
                                        </div>
                                        <div class="d-flex gap-2 tpproduct-details__cart">
                                            <button
                                                class="btn add-to-cart"
                                                name="add_to_cart"
                                                type="submit"
                                                value="1"
                                                <?php if($product->isOutOfStock()): echo 'disabled'; endif; ?>
                                            >
                                                <i class="fal fa-shopping-cart"></i>
                                                <?php echo e(__('Add To Cart')); ?>

                                            </button>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </form>
                        <?php endif; ?>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
    </table>
</div>


<?php /**PATH D:\laragon\www\tesmods\platform\themes/ninico/views/ecommerce/includes/product-table.blade.php ENDPATH**/ ?>