{"version": 3, "file": "vendor/core/plugins/payment/css/payment-setting.css", "mappings": "AACI,2BACI,aAGJ,qCACI,gBAGI,sDACI,gBACA,UAIR,qDAGI,4FAFA,kBACA,qBACA,CAEA,4EACI,ydAIA,wBADA,4BADA,YADA,UAGA,CAIR,sDACI,WAIR,2CACI,gBAKY,kEACI,gBACA,UAGI,kJAEI,kBACA,yCAFA,mBAEA,CAS5B,8CACI,2BACA,iCACA,oCACA,6CACA,mCACA,0DACA,+CACA,wCACA,oDACA,+CAEA,oDAGI,wCACA,8CAHA,gCACA,oBAEA,C", "sources": ["webpack:///./platform/plugins/payment/resources/sass/payment-setting.scss"], "sourcesContent": [".payment-method-item {\n    .well {\n        padding: 15px;\n    }\n\n    .border-pay-row {\n        background: #fff;\n\n        .border-right {\n            ul {\n                list-style: none;\n                padding: 0;\n            }\n        }\n\n        .border-pay-col {\n            text-align: center;\n            vertical-align: middle;\n            border-right: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color) !important;\n\n            i.fa.fa-theme-payments {\n                background-image: url(\"data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20class%3D%22icon%20icon-tabler%20icon-tabler-wallet%22%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%222%22%20stroke%3D%22currentColor%22%20fill%3D%22none%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%3E%0A%20%20%3Cpath%20stroke%3D%22none%22%20d%3D%22M0%200h24v24H0z%22%20fill%3D%22none%22%2F%3E%0A%20%20%3Cpath%20d%3D%22M17%208v-3a1%201%200%200%200%20-1%20-1h-10a2%202%200%200%200%200%204h12a1%201%200%200%201%201%201v3m0%204v3a1%201%200%200%201%20-1%201h-12a2%202%200%200%201%20-2%20-2v-12%22%20%2F%3E%0A%20%20%3Cpath%20d%3D%22M20%2012v4h-4a2%202%200%200%201%200%20-4h4%22%20%2F%3E%0A%3C%2Fsvg%3E\");\n                width: 32px;\n                height: 32px;\n                background-repeat: no-repeat;\n                background-position: center;\n            }\n        }\n\n        img.filter-black {\n            width: 5rem;\n        }\n    }\n\n    .payment-content-item {\n        background: #fff;\n\n        form {\n            > .row {\n                > .col-sm-6 {\n                    > ul {\n                        list-style: none;\n                        padding: 0;\n\n                        > li {\n                            > label, > p {\n                                margin-bottom: 0.5rem;\n                                font-size: 0.875rem;\n                                font-weight: var(--bb-font-weight-medium);\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n\n    .btn.toggle-payment-item {\n        --bb-btn-icon-size: 1.25rem;\n        --bb-btn-bg: var(--bb-bg-surface);\n        --bb-btn-color: var(--bb-body-color);\n        --bb-btn-border-color: var(--bb-border-color);\n        --bb-btn-hover-bg: var(--bb-btn-bg);\n        --bb-btn-hover-border-color: var(--bb-border-active-color);\n        --bb-btn-box-shadow: var(--bb-box-shadow-input);\n        --bb-btn-active-color: var(--bb-primary);\n        --bb-btn-active-bg: rgba(var(--bb-primary-rgb), 0.04);\n        --bb-btn-active-border-color: var(--bb-primary);\n\n        &:hover {\n            color: var(--bb-btn-hover-color);\n            text-decoration: none;\n            background-color: var(--bb-btn-hover-bg);\n            border-color: var(--bb-btn-hover-border-color);\n        }\n    }\n}\n"], "names": [], "sourceRoot": ""}