(()=>{var e={38:e=>{"use strict";class t{constructor(e,t={}){if(this.type="warning",this.text=e,t.node&&t.node.source){let e=t.node.rangeBy(t);this.line=e.start.line,this.column=e.start.column,this.endLine=e.end.line,this.endColumn=e.end.column}for(let e in t)this[e]=t[e]}toString(){return this.node?this.node.error(this.text,{index:this.index,plugin:this.plugin,word:this.word}).message:this.plugin?this.plugin+": "+this.text:this.text}}e.exports=t,t.default=t},145:(e,t,n)=>{"use strict";let r,i,o=n(7793);class s extends o{constructor(e){super({type:"document",...e}),this.nodes||(this.nodes=[])}toResult(e={}){return new r(new i,this,e).stringify()}}s.registerLazyResult=e=>{r=e},s.registerProcessor=e=>{i=e},e.exports=s,s.default=s},197:()=>{},221:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return i(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.Parser=void 0;var s=o(n(357)),a=n(2349),l=new Set(["input","option","optgroup","select","button","datalist","textarea"]),c=new Set(["p"]),u=new Set(["thead","tbody"]),h=new Set(["dd","dt"]),f=new Set(["rt","rp"]),d=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",c],["h1",c],["h2",c],["h3",c],["h4",c],["h5",c],["h6",c],["select",l],["input",l],["output",l],["button",l],["datalist",l],["textarea",l],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",h],["dt",h],["address",c],["article",c],["aside",c],["blockquote",c],["details",c],["div",c],["dl",c],["fieldset",c],["figcaption",c],["figure",c],["footer",c],["form",c],["header",c],["hr",c],["main",c],["nav",c],["ol",c],["pre",c],["section",c],["table",c],["ul",c],["rt",f],["rp",f],["tbody",u],["tfoot",u]]),p=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),m=new Set(["math","svg"]),g=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),y=/\s|\//,v=function(){function e(e,t){var n,r,i,o,a;void 0===t&&(t={}),this.options=t,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.foreignContext=[],this.buffers=[],this.bufferOffset=0,this.writeIndex=0,this.ended=!1,this.cbs=null!=e?e:{},this.lowerCaseTagNames=null!==(n=t.lowerCaseTags)&&void 0!==n?n:!t.xmlMode,this.lowerCaseAttributeNames=null!==(r=t.lowerCaseAttributeNames)&&void 0!==r?r:!t.xmlMode,this.tokenizer=new(null!==(i=t.Tokenizer)&&void 0!==i?i:s.default)(this.options,this),null===(a=(o=this.cbs).onparserinit)||void 0===a||a.call(o,this)}return e.prototype.ontext=function(e,t){var n,r,i=this.getSlice(e,t);this.endIndex=t-1,null===(r=(n=this.cbs).ontext)||void 0===r||r.call(n,i),this.startIndex=t},e.prototype.ontextentity=function(e){var t,n,r=this.tokenizer.getSectionStart();this.endIndex=r-1,null===(n=(t=this.cbs).ontext)||void 0===n||n.call(t,(0,a.fromCodePoint)(e)),this.startIndex=r},e.prototype.isVoidElement=function(e){return!this.options.xmlMode&&p.has(e)},e.prototype.onopentagname=function(e,t){this.endIndex=t;var n=this.getSlice(e,t);this.lowerCaseTagNames&&(n=n.toLowerCase()),this.emitOpenTag(n)},e.prototype.emitOpenTag=function(e){var t,n,r,i;this.openTagStart=this.startIndex,this.tagname=e;var o=!this.options.xmlMode&&d.get(e);if(o)for(;this.stack.length>0&&o.has(this.stack[this.stack.length-1]);){var s=this.stack.pop();null===(n=(t=this.cbs).onclosetag)||void 0===n||n.call(t,s,!0)}this.isVoidElement(e)||(this.stack.push(e),m.has(e)?this.foreignContext.push(!0):g.has(e)&&this.foreignContext.push(!1)),null===(i=(r=this.cbs).onopentagname)||void 0===i||i.call(r,e),this.cbs.onopentag&&(this.attribs={})},e.prototype.endOpenTag=function(e){var t,n;this.startIndex=this.openTagStart,this.attribs&&(null===(n=(t=this.cbs).onopentag)||void 0===n||n.call(t,this.tagname,this.attribs,e),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""},e.prototype.onopentagend=function(e){this.endIndex=e,this.endOpenTag(!1),this.startIndex=e+1},e.prototype.onclosetag=function(e,t){var n,r,i,o,s,a;this.endIndex=t;var l=this.getSlice(e,t);if(this.lowerCaseTagNames&&(l=l.toLowerCase()),(m.has(l)||g.has(l))&&this.foreignContext.pop(),this.isVoidElement(l))this.options.xmlMode||"br"!==l||(null===(r=(n=this.cbs).onopentagname)||void 0===r||r.call(n,"br"),null===(o=(i=this.cbs).onopentag)||void 0===o||o.call(i,"br",{},!0),null===(a=(s=this.cbs).onclosetag)||void 0===a||a.call(s,"br",!1));else{var c=this.stack.lastIndexOf(l);if(-1!==c)if(this.cbs.onclosetag)for(var u=this.stack.length-c;u--;)this.cbs.onclosetag(this.stack.pop(),0!==u);else this.stack.length=c;else this.options.xmlMode||"p"!==l||(this.emitOpenTag("p"),this.closeCurrentTag(!0))}this.startIndex=t+1},e.prototype.onselfclosingtag=function(e){this.endIndex=e,this.options.xmlMode||this.options.recognizeSelfClosing||this.foreignContext[this.foreignContext.length-1]?(this.closeCurrentTag(!1),this.startIndex=e+1):this.onopentagend(e)},e.prototype.closeCurrentTag=function(e){var t,n,r=this.tagname;this.endOpenTag(e),this.stack[this.stack.length-1]===r&&(null===(n=(t=this.cbs).onclosetag)||void 0===n||n.call(t,r,!e),this.stack.pop())},e.prototype.onattribname=function(e,t){this.startIndex=e;var n=this.getSlice(e,t);this.attribname=this.lowerCaseAttributeNames?n.toLowerCase():n},e.prototype.onattribdata=function(e,t){this.attribvalue+=this.getSlice(e,t)},e.prototype.onattribentity=function(e){this.attribvalue+=(0,a.fromCodePoint)(e)},e.prototype.onattribend=function(e,t){var n,r;this.endIndex=t,null===(r=(n=this.cbs).onattribute)||void 0===r||r.call(n,this.attribname,this.attribvalue,e===s.QuoteType.Double?'"':e===s.QuoteType.Single?"'":e===s.QuoteType.NoValue?void 0:null),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribvalue=""},e.prototype.getInstructionName=function(e){var t=e.search(y),n=t<0?e:e.substr(0,t);return this.lowerCaseTagNames&&(n=n.toLowerCase()),n},e.prototype.ondeclaration=function(e,t){this.endIndex=t;var n=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){var r=this.getInstructionName(n);this.cbs.onprocessinginstruction("!".concat(r),"!".concat(n))}this.startIndex=t+1},e.prototype.onprocessinginstruction=function(e,t){this.endIndex=t;var n=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){var r=this.getInstructionName(n);this.cbs.onprocessinginstruction("?".concat(r),"?".concat(n))}this.startIndex=t+1},e.prototype.oncomment=function(e,t,n){var r,i,o,s;this.endIndex=t,null===(i=(r=this.cbs).oncomment)||void 0===i||i.call(r,this.getSlice(e,t-n)),null===(s=(o=this.cbs).oncommentend)||void 0===s||s.call(o),this.startIndex=t+1},e.prototype.oncdata=function(e,t,n){var r,i,o,s,a,l,c,u,h,f;this.endIndex=t;var d=this.getSlice(e,t-n);this.options.xmlMode||this.options.recognizeCDATA?(null===(i=(r=this.cbs).oncdatastart)||void 0===i||i.call(r),null===(s=(o=this.cbs).ontext)||void 0===s||s.call(o,d),null===(l=(a=this.cbs).oncdataend)||void 0===l||l.call(a)):(null===(u=(c=this.cbs).oncomment)||void 0===u||u.call(c,"[CDATA[".concat(d,"]]")),null===(f=(h=this.cbs).oncommentend)||void 0===f||f.call(h)),this.startIndex=t+1},e.prototype.onend=function(){var e,t;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(var n=this.stack.length;n>0;this.cbs.onclosetag(this.stack[--n],!0));}null===(t=(e=this.cbs).onend)||void 0===t||t.call(e)},e.prototype.reset=function(){var e,t,n,r;null===(t=(e=this.cbs).onreset)||void 0===t||t.call(e),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack.length=0,this.startIndex=0,this.endIndex=0,null===(r=(n=this.cbs).onparserinit)||void 0===r||r.call(n,this),this.buffers.length=0,this.bufferOffset=0,this.writeIndex=0,this.ended=!1},e.prototype.parseComplete=function(e){this.reset(),this.end(e)},e.prototype.getSlice=function(e,t){for(;e-this.bufferOffset>=this.buffers[0].length;)this.shiftBuffer();for(var n=this.buffers[0].slice(e-this.bufferOffset,t-this.bufferOffset);t-this.bufferOffset>this.buffers[0].length;)this.shiftBuffer(),n+=this.buffers[0].slice(0,t-this.bufferOffset);return n},e.prototype.shiftBuffer=function(){this.bufferOffset+=this.buffers[0].length,this.writeIndex--,this.buffers.shift()},e.prototype.write=function(e){var t,n;this.ended?null===(n=(t=this.cbs).onerror)||void 0===n||n.call(t,new Error(".write() after done!")):(this.buffers.push(e),this.tokenizer.running&&(this.tokenizer.write(e),this.writeIndex++))},e.prototype.end=function(e){var t,n;this.ended?null===(n=(t=this.cbs).onerror)||void 0===n||n.call(t,new Error(".end() after done!")):(e&&this.write(e),this.ended=!0,this.tokenizer.end())},e.prototype.pause=function(){this.tokenizer.pause()},e.prototype.resume=function(){for(this.tokenizer.resume();this.tokenizer.running&&this.writeIndex<this.buffers.length;)this.tokenizer.write(this.buffers[this.writeIndex++]);this.ended&&this.tokenizer.end()},e.prototype.parseChunk=function(e){this.write(e)},e.prototype.done=function(e){this.end(e)},e}();t.Parser=v},251:(e,t)=>{
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
t.read=function(e,t,n,r,i){var o,s,a=8*i-r-1,l=(1<<a)-1,c=l>>1,u=-7,h=n?i-1:0,f=n?-1:1,d=e[t+h];for(h+=f,o=d&(1<<-u)-1,d>>=-u,u+=a;u>0;o=256*o+e[t+h],h+=f,u-=8);for(s=o&(1<<-u)-1,o>>=-u,u+=r;u>0;s=256*s+e[t+h],h+=f,u-=8);if(0===o)o=1-c;else{if(o===l)return s?NaN:1/0*(d?-1:1);s+=Math.pow(2,r),o-=c}return(d?-1:1)*s*Math.pow(2,o-r)},t.write=function(e,t,n,r,i,o){var s,a,l,c=8*o-i-1,u=(1<<c)-1,h=u>>1,f=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:o-1,p=r?1:-1,m=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=u):(s=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-s))<1&&(s--,l*=2),(t+=s+h>=1?f/l:f*Math.pow(2,1-h))*l>=2&&(s++,l/=2),s+h>=u?(a=0,s=u):s+h>=1?(a=(t*l-1)*Math.pow(2,i),s+=h):(a=t*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;e[n+d]=255&a,d+=p,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;e[n+d]=255&s,d+=p,s/=256,c-=8);e[n+d-p]|=128*m}},357:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.QuoteType=void 0;var r,i,o,s=n(2349);function a(e){return e===r.Space||e===r.NewLine||e===r.Tab||e===r.FormFeed||e===r.CarriageReturn}function l(e){return e===r.Slash||e===r.Gt||a(e)}function c(e){return e>=r.Zero&&e<=r.Nine}!function(e){e[e.Tab=9]="Tab",e[e.NewLine=10]="NewLine",e[e.FormFeed=12]="FormFeed",e[e.CarriageReturn=13]="CarriageReturn",e[e.Space=32]="Space",e[e.ExclamationMark=33]="ExclamationMark",e[e.Number=35]="Number",e[e.Amp=38]="Amp",e[e.SingleQuote=39]="SingleQuote",e[e.DoubleQuote=34]="DoubleQuote",e[e.Dash=45]="Dash",e[e.Slash=47]="Slash",e[e.Zero=48]="Zero",e[e.Nine=57]="Nine",e[e.Semi=59]="Semi",e[e.Lt=60]="Lt",e[e.Eq=61]="Eq",e[e.Gt=62]="Gt",e[e.Questionmark=63]="Questionmark",e[e.UpperA=65]="UpperA",e[e.LowerA=97]="LowerA",e[e.UpperF=70]="UpperF",e[e.LowerF=102]="LowerF",e[e.UpperZ=90]="UpperZ",e[e.LowerZ=122]="LowerZ",e[e.LowerX=120]="LowerX",e[e.OpeningSquareBracket=91]="OpeningSquareBracket"}(r||(r={})),function(e){e[e.Text=1]="Text",e[e.BeforeTagName=2]="BeforeTagName",e[e.InTagName=3]="InTagName",e[e.InSelfClosingTag=4]="InSelfClosingTag",e[e.BeforeClosingTagName=5]="BeforeClosingTagName",e[e.InClosingTagName=6]="InClosingTagName",e[e.AfterClosingTagName=7]="AfterClosingTagName",e[e.BeforeAttributeName=8]="BeforeAttributeName",e[e.InAttributeName=9]="InAttributeName",e[e.AfterAttributeName=10]="AfterAttributeName",e[e.BeforeAttributeValue=11]="BeforeAttributeValue",e[e.InAttributeValueDq=12]="InAttributeValueDq",e[e.InAttributeValueSq=13]="InAttributeValueSq",e[e.InAttributeValueNq=14]="InAttributeValueNq",e[e.BeforeDeclaration=15]="BeforeDeclaration",e[e.InDeclaration=16]="InDeclaration",e[e.InProcessingInstruction=17]="InProcessingInstruction",e[e.BeforeComment=18]="BeforeComment",e[e.CDATASequence=19]="CDATASequence",e[e.InSpecialComment=20]="InSpecialComment",e[e.InCommentLike=21]="InCommentLike",e[e.BeforeSpecialS=22]="BeforeSpecialS",e[e.SpecialStartSequence=23]="SpecialStartSequence",e[e.InSpecialTag=24]="InSpecialTag",e[e.BeforeEntity=25]="BeforeEntity",e[e.BeforeNumericEntity=26]="BeforeNumericEntity",e[e.InNamedEntity=27]="InNamedEntity",e[e.InNumericEntity=28]="InNumericEntity",e[e.InHexEntity=29]="InHexEntity"}(i||(i={})),function(e){e[e.NoValue=0]="NoValue",e[e.Unquoted=1]="Unquoted",e[e.Single=2]="Single",e[e.Double=3]="Double"}(o=t.QuoteType||(t.QuoteType={}));var u={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101])},h=function(){function e(e,t){var n=e.xmlMode,r=void 0!==n&&n,o=e.decodeEntities,a=void 0===o||o;this.cbs=t,this.state=i.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=i.Text,this.isSpecial=!1,this.running=!0,this.offset=0,this.currentSequence=void 0,this.sequenceIndex=0,this.trieIndex=0,this.trieCurrent=0,this.entityResult=0,this.entityExcess=0,this.xmlMode=r,this.decodeEntities=a,this.entityTrie=r?s.xmlDecodeTree:s.htmlDecodeTree}return e.prototype.reset=function(){this.state=i.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=i.Text,this.currentSequence=void 0,this.running=!0,this.offset=0},e.prototype.write=function(e){this.offset+=this.buffer.length,this.buffer=e,this.parse()},e.prototype.end=function(){this.running&&this.finish()},e.prototype.pause=function(){this.running=!1},e.prototype.resume=function(){this.running=!0,this.index<this.buffer.length+this.offset&&this.parse()},e.prototype.getIndex=function(){return this.index},e.prototype.getSectionStart=function(){return this.sectionStart},e.prototype.stateText=function(e){e===r.Lt||!this.decodeEntities&&this.fastForwardTo(r.Lt)?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=i.BeforeTagName,this.sectionStart=this.index):this.decodeEntities&&e===r.Amp&&(this.state=i.BeforeEntity)},e.prototype.stateSpecialStartSequence=function(e){var t=this.sequenceIndex===this.currentSequence.length;if(t?l(e):(32|e)===this.currentSequence[this.sequenceIndex]){if(!t)return void this.sequenceIndex++}else this.isSpecial=!1;this.sequenceIndex=0,this.state=i.InTagName,this.stateInTagName(e)},e.prototype.stateInSpecialTag=function(e){if(this.sequenceIndex===this.currentSequence.length){if(e===r.Gt||a(e)){var t=this.index-this.currentSequence.length;if(this.sectionStart<t){var n=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=n}return this.isSpecial=!1,this.sectionStart=t+2,void this.stateInClosingTagName(e)}this.sequenceIndex=0}(32|e)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence===u.TitleEnd?this.decodeEntities&&e===r.Amp&&(this.state=i.BeforeEntity):this.fastForwardTo(r.Lt)&&(this.sequenceIndex=1):this.sequenceIndex=Number(e===r.Lt)},e.prototype.stateCDATASequence=function(e){e===u.Cdata[this.sequenceIndex]?++this.sequenceIndex===u.Cdata.length&&(this.state=i.InCommentLike,this.currentSequence=u.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=i.InDeclaration,this.stateInDeclaration(e))},e.prototype.fastForwardTo=function(e){for(;++this.index<this.buffer.length+this.offset;)if(this.buffer.charCodeAt(this.index-this.offset)===e)return!0;return this.index=this.buffer.length+this.offset-1,!1},e.prototype.stateInCommentLike=function(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===u.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index,2):this.cbs.oncomment(this.sectionStart,this.index,2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=i.Text):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)},e.prototype.isTagStartChar=function(e){return this.xmlMode?!l(e):function(e){return e>=r.LowerA&&e<=r.LowerZ||e>=r.UpperA&&e<=r.UpperZ}(e)},e.prototype.startSpecial=function(e,t){this.isSpecial=!0,this.currentSequence=e,this.sequenceIndex=t,this.state=i.SpecialStartSequence},e.prototype.stateBeforeTagName=function(e){if(e===r.ExclamationMark)this.state=i.BeforeDeclaration,this.sectionStart=this.index+1;else if(e===r.Questionmark)this.state=i.InProcessingInstruction,this.sectionStart=this.index+1;else if(this.isTagStartChar(e)){var t=32|e;this.sectionStart=this.index,this.xmlMode||t!==u.TitleEnd[2]?this.state=this.xmlMode||t!==u.ScriptEnd[2]?i.InTagName:i.BeforeSpecialS:this.startSpecial(u.TitleEnd,3)}else e===r.Slash?this.state=i.BeforeClosingTagName:(this.state=i.Text,this.stateText(e))},e.prototype.stateInTagName=function(e){l(e)&&(this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=i.BeforeAttributeName,this.stateBeforeAttributeName(e))},e.prototype.stateBeforeClosingTagName=function(e){a(e)||(e===r.Gt?this.state=i.Text:(this.state=this.isTagStartChar(e)?i.InClosingTagName:i.InSpecialComment,this.sectionStart=this.index))},e.prototype.stateInClosingTagName=function(e){(e===r.Gt||a(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=i.AfterClosingTagName,this.stateAfterClosingTagName(e))},e.prototype.stateAfterClosingTagName=function(e){(e===r.Gt||this.fastForwardTo(r.Gt))&&(this.state=i.Text,this.baseState=i.Text,this.sectionStart=this.index+1)},e.prototype.stateBeforeAttributeName=function(e){e===r.Gt?(this.cbs.onopentagend(this.index),this.isSpecial?(this.state=i.InSpecialTag,this.sequenceIndex=0):this.state=i.Text,this.baseState=this.state,this.sectionStart=this.index+1):e===r.Slash?this.state=i.InSelfClosingTag:a(e)||(this.state=i.InAttributeName,this.sectionStart=this.index)},e.prototype.stateInSelfClosingTag=function(e){e===r.Gt?(this.cbs.onselfclosingtag(this.index),this.state=i.Text,this.baseState=i.Text,this.sectionStart=this.index+1,this.isSpecial=!1):a(e)||(this.state=i.BeforeAttributeName,this.stateBeforeAttributeName(e))},e.prototype.stateInAttributeName=function(e){(e===r.Eq||l(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.sectionStart=-1,this.state=i.AfterAttributeName,this.stateAfterAttributeName(e))},e.prototype.stateAfterAttributeName=function(e){e===r.Eq?this.state=i.BeforeAttributeValue:e===r.Slash||e===r.Gt?(this.cbs.onattribend(o.NoValue,this.index),this.state=i.BeforeAttributeName,this.stateBeforeAttributeName(e)):a(e)||(this.cbs.onattribend(o.NoValue,this.index),this.state=i.InAttributeName,this.sectionStart=this.index)},e.prototype.stateBeforeAttributeValue=function(e){e===r.DoubleQuote?(this.state=i.InAttributeValueDq,this.sectionStart=this.index+1):e===r.SingleQuote?(this.state=i.InAttributeValueSq,this.sectionStart=this.index+1):a(e)||(this.sectionStart=this.index,this.state=i.InAttributeValueNq,this.stateInAttributeValueNoQuotes(e))},e.prototype.handleInAttributeValue=function(e,t){e===t||!this.decodeEntities&&this.fastForwardTo(t)?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(t===r.DoubleQuote?o.Double:o.Single,this.index),this.state=i.BeforeAttributeName):this.decodeEntities&&e===r.Amp&&(this.baseState=this.state,this.state=i.BeforeEntity)},e.prototype.stateInAttributeValueDoubleQuotes=function(e){this.handleInAttributeValue(e,r.DoubleQuote)},e.prototype.stateInAttributeValueSingleQuotes=function(e){this.handleInAttributeValue(e,r.SingleQuote)},e.prototype.stateInAttributeValueNoQuotes=function(e){a(e)||e===r.Gt?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(o.Unquoted,this.index),this.state=i.BeforeAttributeName,this.stateBeforeAttributeName(e)):this.decodeEntities&&e===r.Amp&&(this.baseState=this.state,this.state=i.BeforeEntity)},e.prototype.stateBeforeDeclaration=function(e){e===r.OpeningSquareBracket?(this.state=i.CDATASequence,this.sequenceIndex=0):this.state=e===r.Dash?i.BeforeComment:i.InDeclaration},e.prototype.stateInDeclaration=function(e){(e===r.Gt||this.fastForwardTo(r.Gt))&&(this.cbs.ondeclaration(this.sectionStart,this.index),this.state=i.Text,this.sectionStart=this.index+1)},e.prototype.stateInProcessingInstruction=function(e){(e===r.Gt||this.fastForwardTo(r.Gt))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=i.Text,this.sectionStart=this.index+1)},e.prototype.stateBeforeComment=function(e){e===r.Dash?(this.state=i.InCommentLike,this.currentSequence=u.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=i.InDeclaration},e.prototype.stateInSpecialComment=function(e){(e===r.Gt||this.fastForwardTo(r.Gt))&&(this.cbs.oncomment(this.sectionStart,this.index,0),this.state=i.Text,this.sectionStart=this.index+1)},e.prototype.stateBeforeSpecialS=function(e){var t=32|e;t===u.ScriptEnd[3]?this.startSpecial(u.ScriptEnd,4):t===u.StyleEnd[3]?this.startSpecial(u.StyleEnd,4):(this.state=i.InTagName,this.stateInTagName(e))},e.prototype.stateBeforeEntity=function(e){this.entityExcess=1,this.entityResult=0,e===r.Number?this.state=i.BeforeNumericEntity:e===r.Amp||(this.trieIndex=0,this.trieCurrent=this.entityTrie[0],this.state=i.InNamedEntity,this.stateInNamedEntity(e))},e.prototype.stateInNamedEntity=function(e){if(this.entityExcess+=1,this.trieIndex=(0,s.determineBranch)(this.entityTrie,this.trieCurrent,this.trieIndex+1,e),this.trieIndex<0)return this.emitNamedEntity(),void this.index--;this.trieCurrent=this.entityTrie[this.trieIndex];var t=this.trieCurrent&s.BinTrieFlags.VALUE_LENGTH;if(t){var n=(t>>14)-1;if(this.allowLegacyEntity()||e===r.Semi){var i=this.index-this.entityExcess+1;i>this.sectionStart&&this.emitPartial(this.sectionStart,i),this.entityResult=this.trieIndex,this.trieIndex+=n,this.entityExcess=0,this.sectionStart=this.index+1,0===n&&this.emitNamedEntity()}else this.trieIndex+=n}},e.prototype.emitNamedEntity=function(){if(this.state=this.baseState,0!==this.entityResult)switch((this.entityTrie[this.entityResult]&s.BinTrieFlags.VALUE_LENGTH)>>14){case 1:this.emitCodePoint(this.entityTrie[this.entityResult]&~s.BinTrieFlags.VALUE_LENGTH);break;case 2:this.emitCodePoint(this.entityTrie[this.entityResult+1]);break;case 3:this.emitCodePoint(this.entityTrie[this.entityResult+1]),this.emitCodePoint(this.entityTrie[this.entityResult+2])}},e.prototype.stateBeforeNumericEntity=function(e){(32|e)===r.LowerX?(this.entityExcess++,this.state=i.InHexEntity):(this.state=i.InNumericEntity,this.stateInNumericEntity(e))},e.prototype.emitNumericEntity=function(e){var t=this.index-this.entityExcess-1;t+2+Number(this.state===i.InHexEntity)!==this.index&&(t>this.sectionStart&&this.emitPartial(this.sectionStart,t),this.sectionStart=this.index+Number(e),this.emitCodePoint((0,s.replaceCodePoint)(this.entityResult))),this.state=this.baseState},e.prototype.stateInNumericEntity=function(e){e===r.Semi?this.emitNumericEntity(!0):c(e)?(this.entityResult=10*this.entityResult+(e-r.Zero),this.entityExcess++):(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--)},e.prototype.stateInHexEntity=function(e){e===r.Semi?this.emitNumericEntity(!0):c(e)?(this.entityResult=16*this.entityResult+(e-r.Zero),this.entityExcess++):!function(e){return e>=r.UpperA&&e<=r.UpperF||e>=r.LowerA&&e<=r.LowerF}(e)?(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--):(this.entityResult=16*this.entityResult+((32|e)-r.LowerA+10),this.entityExcess++)},e.prototype.allowLegacyEntity=function(){return!this.xmlMode&&(this.baseState===i.Text||this.baseState===i.InSpecialTag)},e.prototype.cleanup=function(){this.running&&this.sectionStart!==this.index&&(this.state===i.Text||this.state===i.InSpecialTag&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):this.state!==i.InAttributeValueDq&&this.state!==i.InAttributeValueSq&&this.state!==i.InAttributeValueNq||(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))},e.prototype.shouldContinue=function(){return this.index<this.buffer.length+this.offset&&this.running},e.prototype.parse=function(){for(;this.shouldContinue();){var e=this.buffer.charCodeAt(this.index-this.offset);switch(this.state){case i.Text:this.stateText(e);break;case i.SpecialStartSequence:this.stateSpecialStartSequence(e);break;case i.InSpecialTag:this.stateInSpecialTag(e);break;case i.CDATASequence:this.stateCDATASequence(e);break;case i.InAttributeValueDq:this.stateInAttributeValueDoubleQuotes(e);break;case i.InAttributeName:this.stateInAttributeName(e);break;case i.InCommentLike:this.stateInCommentLike(e);break;case i.InSpecialComment:this.stateInSpecialComment(e);break;case i.BeforeAttributeName:this.stateBeforeAttributeName(e);break;case i.InTagName:this.stateInTagName(e);break;case i.InClosingTagName:this.stateInClosingTagName(e);break;case i.BeforeTagName:this.stateBeforeTagName(e);break;case i.AfterAttributeName:this.stateAfterAttributeName(e);break;case i.InAttributeValueSq:this.stateInAttributeValueSingleQuotes(e);break;case i.BeforeAttributeValue:this.stateBeforeAttributeValue(e);break;case i.BeforeClosingTagName:this.stateBeforeClosingTagName(e);break;case i.AfterClosingTagName:this.stateAfterClosingTagName(e);break;case i.BeforeSpecialS:this.stateBeforeSpecialS(e);break;case i.InAttributeValueNq:this.stateInAttributeValueNoQuotes(e);break;case i.InSelfClosingTag:this.stateInSelfClosingTag(e);break;case i.InDeclaration:this.stateInDeclaration(e);break;case i.BeforeDeclaration:this.stateBeforeDeclaration(e);break;case i.BeforeComment:this.stateBeforeComment(e);break;case i.InProcessingInstruction:this.stateInProcessingInstruction(e);break;case i.InNamedEntity:this.stateInNamedEntity(e);break;case i.BeforeEntity:this.stateBeforeEntity(e);break;case i.InHexEntity:this.stateInHexEntity(e);break;case i.InNumericEntity:this.stateInNumericEntity(e);break;default:this.stateBeforeNumericEntity(e)}this.index++}this.cleanup()},e.prototype.finish=function(){this.state===i.InNamedEntity&&this.emitNamedEntity(),this.sectionStart<this.index&&this.handleTrailingData(),this.cbs.onend()},e.prototype.handleTrailingData=function(){var e=this.buffer.length+this.offset;this.state===i.InCommentLike?this.currentSequence===u.CdataEnd?this.cbs.oncdata(this.sectionStart,e,0):this.cbs.oncomment(this.sectionStart,e,0):this.state===i.InNumericEntity&&this.allowLegacyEntity()||this.state===i.InHexEntity&&this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state===i.InTagName||this.state===i.BeforeAttributeName||this.state===i.BeforeAttributeValue||this.state===i.AfterAttributeName||this.state===i.InAttributeName||this.state===i.InAttributeValueSq||this.state===i.InAttributeValueDq||this.state===i.InAttributeValueNq||this.state===i.InClosingTagName||this.cbs.ontext(this.sectionStart,e)},e.prototype.emitPartial=function(e,t){this.baseState!==i.Text&&this.baseState!==i.InSpecialTag?this.cbs.onattribdata(e,t):this.cbs.ontext(e,t)},e.prototype.emitCodePoint=function(e){this.baseState!==i.Text&&this.baseState!==i.InSpecialTag?this.cbs.onattribentity(e):this.cbs.ontextentity(e)},e}();t.default=h},396:(e,t,n)=>{"use strict";let r=n(7793);class i extends r{constructor(e){super(e),this.type="atrule"}append(...e){return this.proxyOf.nodes||(this.nodes=[]),super.append(...e)}prepend(...e){return this.proxyOf.nodes||(this.nodes=[]),super.prepend(...e)}}e.exports=i,i.default=i,r.registerAtRule(i)},430:function(e,t,n){"use strict";var r,i=this&&this.__extends||(r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},r(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),o=this&&this.__assign||function(){return o=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},o.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.cloneNode=t.hasChildren=t.isDocument=t.isDirective=t.isComment=t.isText=t.isCDATA=t.isTag=t.Element=t.Document=t.CDATA=t.NodeWithChildren=t.ProcessingInstruction=t.Comment=t.Text=t.DataNode=t.Node=void 0;var s=n(5413),a=function(){function e(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}return Object.defineProperty(e.prototype,"parentNode",{get:function(){return this.parent},set:function(e){this.parent=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"previousSibling",{get:function(){return this.prev},set:function(e){this.prev=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"nextSibling",{get:function(){return this.next},set:function(e){this.next=e},enumerable:!1,configurable:!0}),e.prototype.cloneNode=function(e){return void 0===e&&(e=!1),_(this,e)},e}();t.Node=a;var l=function(e){function t(t){var n=e.call(this)||this;return n.data=t,n}return i(t,e),Object.defineProperty(t.prototype,"nodeValue",{get:function(){return this.data},set:function(e){this.data=e},enumerable:!1,configurable:!0}),t}(a);t.DataNode=l;var c=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=s.ElementType.Text,t}return i(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 3},enumerable:!1,configurable:!0}),t}(l);t.Text=c;var u=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=s.ElementType.Comment,t}return i(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 8},enumerable:!1,configurable:!0}),t}(l);t.Comment=u;var h=function(e){function t(t,n){var r=e.call(this,n)||this;return r.name=t,r.type=s.ElementType.Directive,r}return i(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),t}(l);t.ProcessingInstruction=h;var f=function(e){function t(t){var n=e.call(this)||this;return n.children=t,n}return i(t,e),Object.defineProperty(t.prototype,"firstChild",{get:function(){var e;return null!==(e=this.children[0])&&void 0!==e?e:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lastChild",{get:function(){return this.children.length>0?this.children[this.children.length-1]:null},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"childNodes",{get:function(){return this.children},set:function(e){this.children=e},enumerable:!1,configurable:!0}),t}(a);t.NodeWithChildren=f;var d=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=s.ElementType.CDATA,t}return i(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 4},enumerable:!1,configurable:!0}),t}(f);t.CDATA=d;var p=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.type=s.ElementType.Root,t}return i(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 9},enumerable:!1,configurable:!0}),t}(f);t.Document=p;var m=function(e){function t(t,n,r,i){void 0===r&&(r=[]),void 0===i&&(i="script"===t?s.ElementType.Script:"style"===t?s.ElementType.Style:s.ElementType.Tag);var o=e.call(this,r)||this;return o.name=t,o.attribs=n,o.type=i,o}return i(t,e),Object.defineProperty(t.prototype,"nodeType",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tagName",{get:function(){return this.name},set:function(e){this.name=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"attributes",{get:function(){var e=this;return Object.keys(this.attribs).map((function(t){var n,r;return{name:t,value:e.attribs[t],namespace:null===(n=e["x-attribsNamespace"])||void 0===n?void 0:n[t],prefix:null===(r=e["x-attribsPrefix"])||void 0===r?void 0:r[t]}}))},enumerable:!1,configurable:!0}),t}(f);function g(e){return(0,s.isTag)(e)}function y(e){return e.type===s.ElementType.CDATA}function v(e){return e.type===s.ElementType.Text}function b(e){return e.type===s.ElementType.Comment}function w(e){return e.type===s.ElementType.Directive}function x(e){return e.type===s.ElementType.Root}function _(e,t){var n;if(void 0===t&&(t=!1),v(e))n=new c(e.data);else if(b(e))n=new u(e.data);else if(g(e)){var r=t?T(e.children):[],i=new m(e.name,o({},e.attribs),r);r.forEach((function(e){return e.parent=i})),null!=e.namespace&&(i.namespace=e.namespace),e["x-attribsNamespace"]&&(i["x-attribsNamespace"]=o({},e["x-attribsNamespace"])),e["x-attribsPrefix"]&&(i["x-attribsPrefix"]=o({},e["x-attribsPrefix"])),n=i}else if(y(e)){r=t?T(e.children):[];var s=new d(r);r.forEach((function(e){return e.parent=s})),n=s}else if(x(e)){r=t?T(e.children):[];var a=new p(r);r.forEach((function(e){return e.parent=a})),e["x-mode"]&&(a["x-mode"]=e["x-mode"]),n=a}else{if(!w(e))throw new Error("Not implemented yet: ".concat(e.type));var l=new h(e.name,e.data);null!=e["x-name"]&&(l["x-name"]=e["x-name"],l["x-publicId"]=e["x-publicId"],l["x-systemId"]=e["x-systemId"]),n=l}return n.startIndex=e.startIndex,n.endIndex=e.endIndex,null!=e.sourceCodeLocation&&(n.sourceCodeLocation=e.sourceCodeLocation),n}function T(e){for(var t=e.map((function(e){return _(e,!0)})),n=1;n<t.length;n++)t[n].prev=t[n-1],t[n-1].next=t[n];return t}t.Element=m,t.isTag=g,t.isCDATA=y,t.isText=v,t.isComment=b,t.isDirective=w,t.isDocument=x,t.hasChildren=function(e){return Object.prototype.hasOwnProperty.call(e,"children")},t.cloneNode=_},454:(e,t,n)=>{"use strict";n.r(t),n.d(t,{Alert:()=>kt,Button:()=>Nt,Carousel:()=>hn,Collapse:()=>An,Dropdown:()=>Qn,Modal:()=>Dr,Offcanvas:()=>Xr,Popover:()=>wi,ScrollSpy:()=>Di,Tab:()=>eo,Toast:()=>go,Tooltip:()=>mi});var r={};n.r(r),n.d(r,{afterMain:()=>T,afterRead:()=>w,afterWrite:()=>S,applyStyles:()=>P,arrow:()=>K,auto:()=>l,basePlacements:()=>c,beforeMain:()=>x,beforeRead:()=>v,beforeWrite:()=>E,bottom:()=>o,clippingParents:()=>f,computeStyles:()=>re,createPopper:()=>Pe,createPopperBase:()=>Le,createPopperLite:()=>Ie,detectOverflow:()=>be,end:()=>h,eventListeners:()=>oe,flip:()=>we,hide:()=>Te,left:()=>a,main:()=>_,modifierPhases:()=>C,offset:()=>Ee,placements:()=>y,popper:()=>p,popperGenerator:()=>De,popperOffsets:()=>Ae,preventOverflow:()=>Se,read:()=>b,reference:()=>m,right:()=>s,start:()=>u,top:()=>i,variationPlacements:()=>g,viewport:()=>d,write:()=>A});var i="top",o="bottom",s="right",a="left",l="auto",c=[i,o,s,a],u="start",h="end",f="clippingParents",d="viewport",p="popper",m="reference",g=c.reduce((function(e,t){return e.concat([t+"-"+u,t+"-"+h])}),[]),y=[].concat(c,[l]).reduce((function(e,t){return e.concat([t,t+"-"+u,t+"-"+h])}),[]),v="beforeRead",b="read",w="afterRead",x="beforeMain",_="main",T="afterMain",E="beforeWrite",A="write",S="afterWrite",C=[v,b,w,x,_,T,E,A,S];function k(e){return e?(e.nodeName||"").toLowerCase():null}function O(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function N(e){return e instanceof O(e).Element||e instanceof Element}function D(e){return e instanceof O(e).HTMLElement||e instanceof HTMLElement}function L(e){return"undefined"!=typeof ShadowRoot&&(e instanceof O(e).ShadowRoot||e instanceof ShadowRoot)}const P={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},i=t.elements[e];D(i)&&k(i)&&(Object.assign(i.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?i.removeAttribute(e):i.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],i=t.attributes[e]||{},o=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});D(r)&&k(r)&&(Object.assign(r.style,o),Object.keys(i).forEach((function(e){r.removeAttribute(e)})))}))}},requires:["computeStyles"]};function I(e){return e.split("-")[0]}var j=Math.max,M=Math.min,q=Math.round;function R(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function B(){return!/^((?!chrome|android).)*safari/i.test(R())}function $(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),i=1,o=1;t&&D(e)&&(i=e.offsetWidth>0&&q(r.width)/e.offsetWidth||1,o=e.offsetHeight>0&&q(r.height)/e.offsetHeight||1);var s=(N(e)?O(e):window).visualViewport,a=!B()&&n,l=(r.left+(a&&s?s.offsetLeft:0))/i,c=(r.top+(a&&s?s.offsetTop:0))/o,u=r.width/i,h=r.height/o;return{width:u,height:h,top:c,right:l+u,bottom:c+h,left:l,x:l,y:c}}function H(e){var t=$(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function U(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&L(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function F(e){return O(e).getComputedStyle(e)}function V(e){return["table","td","th"].indexOf(k(e))>=0}function z(e){return((N(e)?e.ownerDocument:e.document)||window.document).documentElement}function W(e){return"html"===k(e)?e:e.assignedSlot||e.parentNode||(L(e)?e.host:null)||z(e)}function G(e){return D(e)&&"fixed"!==F(e).position?e.offsetParent:null}function Y(e){for(var t=O(e),n=G(e);n&&V(n)&&"static"===F(n).position;)n=G(n);return n&&("html"===k(n)||"body"===k(n)&&"static"===F(n).position)?t:n||function(e){var t=/firefox/i.test(R());if(/Trident/i.test(R())&&D(e)&&"fixed"===F(e).position)return null;var n=W(e);for(L(n)&&(n=n.host);D(n)&&["html","body"].indexOf(k(n))<0;){var r=F(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}function X(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Q(e,t,n){return j(e,M(t,n))}function J(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Z(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}const K={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,l=e.options,u=n.elements.arrow,h=n.modifiersData.popperOffsets,f=I(n.placement),d=X(f),p=[a,s].indexOf(f)>=0?"height":"width";if(u&&h){var m=function(e,t){return J("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Z(e,c))}(l.padding,n),g=H(u),y="y"===d?i:a,v="y"===d?o:s,b=n.rects.reference[p]+n.rects.reference[d]-h[d]-n.rects.popper[p],w=h[d]-n.rects.reference[d],x=Y(u),_=x?"y"===d?x.clientHeight||0:x.clientWidth||0:0,T=b/2-w/2,E=m[y],A=_-g[p]-m[v],S=_/2-g[p]/2+T,C=Q(E,S,A),k=d;n.modifiersData[r]=((t={})[k]=C,t.centerOffset=C-S,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&U(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function ee(e){return e.split("-")[1]}var te={top:"auto",right:"auto",bottom:"auto",left:"auto"};function ne(e){var t,n=e.popper,r=e.popperRect,l=e.placement,c=e.variation,u=e.offsets,f=e.position,d=e.gpuAcceleration,p=e.adaptive,m=e.roundOffsets,g=e.isFixed,y=u.x,v=void 0===y?0:y,b=u.y,w=void 0===b?0:b,x="function"==typeof m?m({x:v,y:w}):{x:v,y:w};v=x.x,w=x.y;var _=u.hasOwnProperty("x"),T=u.hasOwnProperty("y"),E=a,A=i,S=window;if(p){var C=Y(n),k="clientHeight",N="clientWidth";if(C===O(n)&&"static"!==F(C=z(n)).position&&"absolute"===f&&(k="scrollHeight",N="scrollWidth"),l===i||(l===a||l===s)&&c===h)A=o,w-=(g&&C===S&&S.visualViewport?S.visualViewport.height:C[k])-r.height,w*=d?1:-1;if(l===a||(l===i||l===o)&&c===h)E=s,v-=(g&&C===S&&S.visualViewport?S.visualViewport.width:C[N])-r.width,v*=d?1:-1}var D,L=Object.assign({position:f},p&&te),P=!0===m?function(e,t){var n=e.x,r=e.y,i=t.devicePixelRatio||1;return{x:q(n*i)/i||0,y:q(r*i)/i||0}}({x:v,y:w},O(n)):{x:v,y:w};return v=P.x,w=P.y,d?Object.assign({},L,((D={})[A]=T?"0":"",D[E]=_?"0":"",D.transform=(S.devicePixelRatio||1)<=1?"translate("+v+"px, "+w+"px)":"translate3d("+v+"px, "+w+"px, 0)",D)):Object.assign({},L,((t={})[A]=T?w+"px":"",t[E]=_?v+"px":"",t.transform="",t))}const re={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,i=void 0===r||r,o=n.adaptive,s=void 0===o||o,a=n.roundOffsets,l=void 0===a||a,c={placement:I(t.placement),variation:ee(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:i,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,ne(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:s,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,ne(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}};var ie={passive:!0};const oe={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,i=r.scroll,o=void 0===i||i,s=r.resize,a=void 0===s||s,l=O(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return o&&c.forEach((function(e){e.addEventListener("scroll",n.update,ie)})),a&&l.addEventListener("resize",n.update,ie),function(){o&&c.forEach((function(e){e.removeEventListener("scroll",n.update,ie)})),a&&l.removeEventListener("resize",n.update,ie)}},data:{}};var se={left:"right",right:"left",bottom:"top",top:"bottom"};function ae(e){return e.replace(/left|right|bottom|top/g,(function(e){return se[e]}))}var le={start:"end",end:"start"};function ce(e){return e.replace(/start|end/g,(function(e){return le[e]}))}function ue(e){var t=O(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function he(e){return $(z(e)).left+ue(e).scrollLeft}function fe(e){var t=F(e),n=t.overflow,r=t.overflowX,i=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+i+r)}function de(e){return["html","body","#document"].indexOf(k(e))>=0?e.ownerDocument.body:D(e)&&fe(e)?e:de(W(e))}function pe(e,t){var n;void 0===t&&(t=[]);var r=de(e),i=r===(null==(n=e.ownerDocument)?void 0:n.body),o=O(r),s=i?[o].concat(o.visualViewport||[],fe(r)?r:[]):r,a=t.concat(s);return i?a:a.concat(pe(W(s)))}function me(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ge(e,t,n){return t===d?me(function(e,t){var n=O(e),r=z(e),i=n.visualViewport,o=r.clientWidth,s=r.clientHeight,a=0,l=0;if(i){o=i.width,s=i.height;var c=B();(c||!c&&"fixed"===t)&&(a=i.offsetLeft,l=i.offsetTop)}return{width:o,height:s,x:a+he(e),y:l}}(e,n)):N(t)?function(e,t){var n=$(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):me(function(e){var t,n=z(e),r=ue(e),i=null==(t=e.ownerDocument)?void 0:t.body,o=j(n.scrollWidth,n.clientWidth,i?i.scrollWidth:0,i?i.clientWidth:0),s=j(n.scrollHeight,n.clientHeight,i?i.scrollHeight:0,i?i.clientHeight:0),a=-r.scrollLeft+he(e),l=-r.scrollTop;return"rtl"===F(i||n).direction&&(a+=j(n.clientWidth,i?i.clientWidth:0)-o),{width:o,height:s,x:a,y:l}}(z(e)))}function ye(e,t,n,r){var i="clippingParents"===t?function(e){var t=pe(W(e)),n=["absolute","fixed"].indexOf(F(e).position)>=0&&D(e)?Y(e):e;return N(n)?t.filter((function(e){return N(e)&&U(e,n)&&"body"!==k(e)})):[]}(e):[].concat(t),o=[].concat(i,[n]),s=o[0],a=o.reduce((function(t,n){var i=ge(e,n,r);return t.top=j(i.top,t.top),t.right=M(i.right,t.right),t.bottom=M(i.bottom,t.bottom),t.left=j(i.left,t.left),t}),ge(e,s,r));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function ve(e){var t,n=e.reference,r=e.element,l=e.placement,c=l?I(l):null,f=l?ee(l):null,d=n.x+n.width/2-r.width/2,p=n.y+n.height/2-r.height/2;switch(c){case i:t={x:d,y:n.y-r.height};break;case o:t={x:d,y:n.y+n.height};break;case s:t={x:n.x+n.width,y:p};break;case a:t={x:n.x-r.width,y:p};break;default:t={x:n.x,y:n.y}}var m=c?X(c):null;if(null!=m){var g="y"===m?"height":"width";switch(f){case u:t[m]=t[m]-(n[g]/2-r[g]/2);break;case h:t[m]=t[m]+(n[g]/2-r[g]/2)}}return t}function be(e,t){void 0===t&&(t={});var n=t,r=n.placement,a=void 0===r?e.placement:r,l=n.strategy,u=void 0===l?e.strategy:l,h=n.boundary,g=void 0===h?f:h,y=n.rootBoundary,v=void 0===y?d:y,b=n.elementContext,w=void 0===b?p:b,x=n.altBoundary,_=void 0!==x&&x,T=n.padding,E=void 0===T?0:T,A=J("number"!=typeof E?E:Z(E,c)),S=w===p?m:p,C=e.rects.popper,k=e.elements[_?S:w],O=ye(N(k)?k:k.contextElement||z(e.elements.popper),g,v,u),D=$(e.elements.reference),L=ve({reference:D,element:C,strategy:"absolute",placement:a}),P=me(Object.assign({},C,L)),I=w===p?P:D,j={top:O.top-I.top+A.top,bottom:I.bottom-O.bottom+A.bottom,left:O.left-I.left+A.left,right:I.right-O.right+A.right},M=e.modifiersData.offset;if(w===p&&M){var q=M[a];Object.keys(j).forEach((function(e){var t=[s,o].indexOf(e)>=0?1:-1,n=[i,o].indexOf(e)>=0?"y":"x";j[e]+=q[n]*t}))}return j}const we={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var h=n.mainAxis,f=void 0===h||h,d=n.altAxis,p=void 0===d||d,m=n.fallbackPlacements,v=n.padding,b=n.boundary,w=n.rootBoundary,x=n.altBoundary,_=n.flipVariations,T=void 0===_||_,E=n.allowedAutoPlacements,A=t.options.placement,S=I(A),C=m||(S===A||!T?[ae(A)]:function(e){if(I(e)===l)return[];var t=ae(e);return[ce(e),t,ce(t)]}(A)),k=[A].concat(C).reduce((function(e,n){return e.concat(I(n)===l?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,i=n.boundary,o=n.rootBoundary,s=n.padding,a=n.flipVariations,l=n.allowedAutoPlacements,u=void 0===l?y:l,h=ee(r),f=h?a?g:g.filter((function(e){return ee(e)===h})):c,d=f.filter((function(e){return u.indexOf(e)>=0}));0===d.length&&(d=f);var p=d.reduce((function(t,n){return t[n]=be(e,{placement:n,boundary:i,rootBoundary:o,padding:s})[I(n)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}(t,{placement:n,boundary:b,rootBoundary:w,padding:v,flipVariations:T,allowedAutoPlacements:E}):n)}),[]),O=t.rects.reference,N=t.rects.popper,D=new Map,L=!0,P=k[0],j=0;j<k.length;j++){var M=k[j],q=I(M),R=ee(M)===u,B=[i,o].indexOf(q)>=0,$=B?"width":"height",H=be(t,{placement:M,boundary:b,rootBoundary:w,altBoundary:x,padding:v}),U=B?R?s:a:R?o:i;O[$]>N[$]&&(U=ae(U));var F=ae(U),V=[];if(f&&V.push(H[q]<=0),p&&V.push(H[U]<=0,H[F]<=0),V.every((function(e){return e}))){P=M,L=!1;break}D.set(M,V)}if(L)for(var z=function(e){var t=k.find((function(t){var n=D.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return P=t,"break"},W=T?3:1;W>0;W--){if("break"===z(W))break}t.placement!==P&&(t.modifiersData[r]._skip=!0,t.placement=P,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function xe(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function _e(e){return[i,s,o,a].some((function(t){return e[t]>=0}))}const Te={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,i=t.rects.popper,o=t.modifiersData.preventOverflow,s=be(t,{elementContext:"reference"}),a=be(t,{altBoundary:!0}),l=xe(s,r),c=xe(a,i,o),u=_e(l),h=_e(c);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:h},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":h})}};const Ee={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,l=void 0===o?[0,0]:o,c=y.reduce((function(e,n){return e[n]=function(e,t,n){var r=I(e),o=[a,i].indexOf(r)>=0?-1:1,l="function"==typeof n?n(Object.assign({},t,{placement:e})):n,c=l[0],u=l[1];return c=c||0,u=(u||0)*o,[a,s].indexOf(r)>=0?{x:u,y:c}:{x:c,y:u}}(n,t.rects,l),e}),{}),u=c[t.placement],h=u.x,f=u.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=h,t.modifiersData.popperOffsets.y+=f),t.modifiersData[r]=c}};const Ae={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=ve({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}};const Se={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,l=n.mainAxis,c=void 0===l||l,h=n.altAxis,f=void 0!==h&&h,d=n.boundary,p=n.rootBoundary,m=n.altBoundary,g=n.padding,y=n.tether,v=void 0===y||y,b=n.tetherOffset,w=void 0===b?0:b,x=be(t,{boundary:d,rootBoundary:p,padding:g,altBoundary:m}),_=I(t.placement),T=ee(t.placement),E=!T,A=X(_),S="x"===A?"y":"x",C=t.modifiersData.popperOffsets,k=t.rects.reference,O=t.rects.popper,N="function"==typeof w?w(Object.assign({},t.rects,{placement:t.placement})):w,D="number"==typeof N?{mainAxis:N,altAxis:N}:Object.assign({mainAxis:0,altAxis:0},N),L=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,P={x:0,y:0};if(C){if(c){var q,R="y"===A?i:a,B="y"===A?o:s,$="y"===A?"height":"width",U=C[A],F=U+x[R],V=U-x[B],z=v?-O[$]/2:0,W=T===u?k[$]:O[$],G=T===u?-O[$]:-k[$],J=t.elements.arrow,Z=v&&J?H(J):{width:0,height:0},K=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},te=K[R],ne=K[B],re=Q(0,k[$],Z[$]),ie=E?k[$]/2-z-re-te-D.mainAxis:W-re-te-D.mainAxis,oe=E?-k[$]/2+z+re+ne+D.mainAxis:G+re+ne+D.mainAxis,se=t.elements.arrow&&Y(t.elements.arrow),ae=se?"y"===A?se.clientTop||0:se.clientLeft||0:0,le=null!=(q=null==L?void 0:L[A])?q:0,ce=U+oe-le,ue=Q(v?M(F,U+ie-le-ae):F,U,v?j(V,ce):V);C[A]=ue,P[A]=ue-U}if(f){var he,fe="x"===A?i:a,de="x"===A?o:s,pe=C[S],me="y"===S?"height":"width",ge=pe+x[fe],ye=pe-x[de],ve=-1!==[i,a].indexOf(_),we=null!=(he=null==L?void 0:L[S])?he:0,xe=ve?ge:pe-k[me]-O[me]-we+D.altAxis,_e=ve?pe+k[me]+O[me]-we-D.altAxis:ye,Te=v&&ve?function(e,t,n){var r=Q(e,t,n);return r>n?n:r}(xe,pe,_e):Q(v?xe:ge,pe,v?_e:ye);C[S]=Te,P[S]=Te-pe}t.modifiersData[r]=P}},requiresIfExists:["offset"]};function Ce(e,t,n){void 0===n&&(n=!1);var r,i,o=D(t),s=D(t)&&function(e){var t=e.getBoundingClientRect(),n=q(t.width)/e.offsetWidth||1,r=q(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),a=z(t),l=$(e,s,n),c={scrollLeft:0,scrollTop:0},u={x:0,y:0};return(o||!o&&!n)&&(("body"!==k(t)||fe(a))&&(c=(r=t)!==O(r)&&D(r)?{scrollLeft:(i=r).scrollLeft,scrollTop:i.scrollTop}:ue(r)),D(t)?((u=$(t,!0)).x+=t.clientLeft,u.y+=t.clientTop):a&&(u.x=he(a))),{x:l.left+c.scrollLeft-u.x,y:l.top+c.scrollTop-u.y,width:l.width,height:l.height}}function ke(e){var t=new Map,n=new Set,r=[];function i(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&i(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||i(e)})),r}var Oe={placement:"bottom",modifiers:[],strategy:"absolute"};function Ne(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function De(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,i=t.defaultOptions,o=void 0===i?Oe:i;return function(e,t,n){void 0===n&&(n=o);var i,s,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},Oe,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},l=[],c=!1,u={state:a,setOptions:function(n){var i="function"==typeof n?n(a.options):n;h(),a.options=Object.assign({},o,a.options,i),a.scrollParents={reference:N(e)?pe(e):e.contextElement?pe(e.contextElement):[],popper:pe(t)};var s,c,f=function(e){var t=ke(e);return C.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((s=[].concat(r,a.options.modifiers),c=s.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(c).map((function(e){return c[e]}))));return a.orderedModifiers=f.filter((function(e){return e.enabled})),a.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,i=e.effect;if("function"==typeof i){var o=i({state:a,name:t,instance:u,options:r}),s=function(){};l.push(o||s)}})),u.update()},forceUpdate:function(){if(!c){var e=a.elements,t=e.reference,n=e.popper;if(Ne(t,n)){a.rects={reference:Ce(t,Y(n),"fixed"===a.options.strategy),popper:H(n)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(e){return a.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<a.orderedModifiers.length;r++)if(!0!==a.reset){var i=a.orderedModifiers[r],o=i.fn,s=i.options,l=void 0===s?{}:s,h=i.name;"function"==typeof o&&(a=o({state:a,options:l,name:h,instance:u})||a)}else a.reset=!1,r=-1}}},update:(i=function(){return new Promise((function(e){u.forceUpdate(),e(a)}))},function(){return s||(s=new Promise((function(e){Promise.resolve().then((function(){s=void 0,e(i())}))}))),s}),destroy:function(){h(),c=!0}};if(!Ne(e,t))return u;function h(){l.forEach((function(e){return e()})),l=[]}return u.setOptions(n).then((function(e){!c&&n.onFirstUpdate&&n.onFirstUpdate(e)})),u}}var Le=De(),Pe=De({defaultModifiers:[oe,Ae,re,P,Ee,we,Se,K,Te]}),Ie=De({defaultModifiers:[oe,Ae,re,P]});
/*!
  * Bootstrap v5.3.3 (https://getbootstrap.com/)
  * Copyright 2011-2024 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */
const je=new Map,Me={set(e,t,n){je.has(e)||je.set(e,new Map);const r=je.get(e);r.has(t)||0===r.size?r.set(t,n):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(r.keys())[0]}.`)},get:(e,t)=>je.has(e)&&je.get(e).get(t)||null,remove(e,t){if(!je.has(e))return;const n=je.get(e);n.delete(t),0===n.size&&je.delete(e)}},qe="transitionend",Re=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,((e,t)=>`#${CSS.escape(t)}`))),e),Be=e=>{e.dispatchEvent(new Event(qe))},$e=e=>!(!e||"object"!=typeof e)&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),He=e=>$e(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(Re(e)):null,Ue=e=>{if(!$e(e)||0===e.getClientRects().length)return!1;const t="visible"===getComputedStyle(e).getPropertyValue("visibility"),n=e.closest("details:not([open])");if(!n)return t;if(n!==e){const t=e.closest("summary");if(t&&t.parentNode!==n)return!1;if(null===t)return!1}return t},Fe=e=>!e||e.nodeType!==Node.ELEMENT_NODE||(!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled"))),Ve=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?Ve(e.parentNode):null},ze=()=>{},We=e=>{e.offsetHeight},Ge=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,Ye=[],Xe=()=>"rtl"===document.documentElement.dir,Qe=e=>{var t;t=()=>{const t=Ge();if(t){const n=e.NAME,r=t.fn[n];t.fn[n]=e.jQueryInterface,t.fn[n].Constructor=e,t.fn[n].noConflict=()=>(t.fn[n]=r,e.jQueryInterface)}},"loading"===document.readyState?(Ye.length||document.addEventListener("DOMContentLoaded",(()=>{for(const e of Ye)e()})),Ye.push(t)):t()},Je=(e,t=[],n=e)=>"function"==typeof e?e(...t):n,Ze=(e,t,n=!0)=>{if(!n)return void Je(e);const r=(e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:n}=window.getComputedStyle(e);const r=Number.parseFloat(t),i=Number.parseFloat(n);return r||i?(t=t.split(",")[0],n=n.split(",")[0],1e3*(Number.parseFloat(t)+Number.parseFloat(n))):0})(t)+5;let i=!1;const o=({target:n})=>{n===t&&(i=!0,t.removeEventListener(qe,o),Je(e))};t.addEventListener(qe,o),setTimeout((()=>{i||Be(t)}),r)},Ke=(e,t,n,r)=>{const i=e.length;let o=e.indexOf(t);return-1===o?!n&&r?e[i-1]:e[0]:(o+=n?1:-1,r&&(o=(o+i)%i),e[Math.max(0,Math.min(o,i-1))])},et=/[^.]*(?=\..*)\.|.*/,tt=/\..*/,nt=/::\d+$/,rt={};let it=1;const ot={mouseenter:"mouseover",mouseleave:"mouseout"},st=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function at(e,t){return t&&`${t}::${it++}`||e.uidEvent||it++}function lt(e){const t=at(e);return e.uidEvent=t,rt[t]=rt[t]||{},rt[t]}function ct(e,t,n=null){return Object.values(e).find((e=>e.callable===t&&e.delegationSelector===n))}function ut(e,t,n){const r="string"==typeof t,i=r?n:t||n;let o=pt(e);return st.has(o)||(o=e),[r,i,o]}function ht(e,t,n,r,i){if("string"!=typeof t||!e)return;let[o,s,a]=ut(t,n,r);if(t in ot){const e=e=>function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)};s=e(s)}const l=lt(e),c=l[a]||(l[a]={}),u=ct(c,s,o?n:null);if(u)return void(u.oneOff=u.oneOff&&i);const h=at(s,t.replace(et,"")),f=o?function(e,t,n){return function r(i){const o=e.querySelectorAll(t);for(let{target:s}=i;s&&s!==this;s=s.parentNode)for(const a of o)if(a===s)return gt(i,{delegateTarget:s}),r.oneOff&&mt.off(e,i.type,t,n),n.apply(s,[i])}}(e,n,s):function(e,t){return function n(r){return gt(r,{delegateTarget:e}),n.oneOff&&mt.off(e,r.type,t),t.apply(e,[r])}}(e,s);f.delegationSelector=o?n:null,f.callable=s,f.oneOff=i,f.uidEvent=h,c[h]=f,e.addEventListener(a,f,o)}function ft(e,t,n,r,i){const o=ct(t[n],r,i);o&&(e.removeEventListener(n,o,Boolean(i)),delete t[n][o.uidEvent])}function dt(e,t,n,r){const i=t[n]||{};for(const[o,s]of Object.entries(i))o.includes(r)&&ft(e,t,n,s.callable,s.delegationSelector)}function pt(e){return e=e.replace(tt,""),ot[e]||e}const mt={on(e,t,n,r){ht(e,t,n,r,!1)},one(e,t,n,r){ht(e,t,n,r,!0)},off(e,t,n,r){if("string"!=typeof t||!e)return;const[i,o,s]=ut(t,n,r),a=s!==t,l=lt(e),c=l[s]||{},u=t.startsWith(".");if(void 0===o){if(u)for(const n of Object.keys(l))dt(e,l,n,t.slice(1));for(const[n,r]of Object.entries(c)){const i=n.replace(nt,"");a&&!t.includes(i)||ft(e,l,s,r.callable,r.delegationSelector)}}else{if(!Object.keys(c).length)return;ft(e,l,s,o,i?n:null)}},trigger(e,t,n){if("string"!=typeof t||!e)return null;const r=Ge();let i=null,o=!0,s=!0,a=!1;t!==pt(t)&&r&&(i=r.Event(t,n),r(e).trigger(i),o=!i.isPropagationStopped(),s=!i.isImmediatePropagationStopped(),a=i.isDefaultPrevented());const l=gt(new Event(t,{bubbles:o,cancelable:!0}),n);return a&&l.preventDefault(),s&&e.dispatchEvent(l),l.defaultPrevented&&i&&i.preventDefault(),l}};function gt(e,t={}){for(const[n,r]of Object.entries(t))try{e[n]=r}catch(t){Object.defineProperty(e,n,{configurable:!0,get:()=>r})}return e}function yt(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function vt(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}const bt={setDataAttribute(e,t,n){e.setAttribute(`data-bs-${vt(t)}`,n)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${vt(t)}`)},getDataAttributes(e){if(!e)return{};const t={},n=Object.keys(e.dataset).filter((e=>e.startsWith("bs")&&!e.startsWith("bsConfig")));for(const r of n){let n=r.replace(/^bs/,"");n=n.charAt(0).toLowerCase()+n.slice(1,n.length),t[n]=yt(e.dataset[r])}return t},getDataAttribute:(e,t)=>yt(e.getAttribute(`data-bs-${vt(t)}`))};class wt{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){const n=$e(t)?bt.getDataAttribute(t,"config"):{};return{...this.constructor.Default,..."object"==typeof n?n:{},...$e(t)?bt.getDataAttributes(t):{},..."object"==typeof e?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(const[r,i]of Object.entries(t)){const t=e[r],o=$e(t)?"element":null==(n=t)?`${n}`:Object.prototype.toString.call(n).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(i).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${r}" provided type "${o}" but expected type "${i}".`)}var n}}class xt extends wt{constructor(e,t){super(),(e=He(e))&&(this._element=e,this._config=this._getConfig(t),Me.set(this._element,this.constructor.DATA_KEY,this))}dispose(){Me.remove(this._element,this.constructor.DATA_KEY),mt.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,n=!0){Ze(e,t,n)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return Me.get(He(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return"5.3.3"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}const _t=e=>{let t=e.getAttribute("data-bs-target");if(!t||"#"===t){let n=e.getAttribute("href");if(!n||!n.includes("#")&&!n.startsWith("."))return null;n.includes("#")&&!n.startsWith("#")&&(n=`#${n.split("#")[1]}`),t=n&&"#"!==n?n.trim():null}return t?t.split(",").map((e=>Re(e))).join(","):null},Tt={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter((e=>e.matches(t))),parents(e,t){const n=[];let r=e.parentNode.closest(t);for(;r;)n.push(r),r=r.parentNode.closest(t);return n},prev(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return[n];n=n.previousElementSibling}return[]},next(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return[n];n=n.nextElementSibling}return[]},focusableChildren(e){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((e=>`${e}:not([tabindex^="-"])`)).join(",");return this.find(t,e).filter((e=>!Fe(e)&&Ue(e)))},getSelectorFromElement(e){const t=_t(e);return t&&Tt.findOne(t)?t:null},getElementFromSelector(e){const t=_t(e);return t?Tt.findOne(t):null},getMultipleElementsFromSelector(e){const t=_t(e);return t?Tt.find(t):[]}},Et=(e,t="hide")=>{const n=`click.dismiss${e.EVENT_KEY}`,r=e.NAME;mt.on(document,n,`[data-bs-dismiss="${r}"]`,(function(n){if(["A","AREA"].includes(this.tagName)&&n.preventDefault(),Fe(this))return;const i=Tt.getElementFromSelector(this)||this.closest(`.${r}`);e.getOrCreateInstance(i)[t]()}))},At=".bs.alert",St=`close${At}`,Ct=`closed${At}`;class kt extends xt{static get NAME(){return"alert"}close(){if(mt.trigger(this._element,St).defaultPrevented)return;this._element.classList.remove("show");const e=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,e)}_destroyElement(){this._element.remove(),mt.trigger(this._element,Ct),this.dispose()}static jQueryInterface(e){return this.each((function(){const t=kt.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}Et(kt,"close"),Qe(kt);const Ot='[data-bs-toggle="button"]';class Nt extends xt{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each((function(){const t=Nt.getOrCreateInstance(this);"toggle"===e&&t[e]()}))}}mt.on(document,"click.bs.button.data-api",Ot,(e=>{e.preventDefault();const t=e.target.closest(Ot);Nt.getOrCreateInstance(t).toggle()})),Qe(Nt);const Dt=".bs.swipe",Lt=`touchstart${Dt}`,Pt=`touchmove${Dt}`,It=`touchend${Dt}`,jt=`pointerdown${Dt}`,Mt=`pointerup${Dt}`,qt={endCallback:null,leftCallback:null,rightCallback:null},Rt={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class Bt extends wt{constructor(e,t){super(),this._element=e,e&&Bt.isSupported()&&(this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return qt}static get DefaultType(){return Rt}static get NAME(){return"swipe"}dispose(){mt.off(this._element,Dt)}_start(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),Je(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){const e=Math.abs(this._deltaX);if(e<=40)return;const t=e/this._deltaX;this._deltaX=0,t&&Je(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(mt.on(this._element,jt,(e=>this._start(e))),mt.on(this._element,Mt,(e=>this._end(e))),this._element.classList.add("pointer-event")):(mt.on(this._element,Lt,(e=>this._start(e))),mt.on(this._element,Pt,(e=>this._move(e))),mt.on(this._element,It,(e=>this._end(e))))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&("pen"===e.pointerType||"touch"===e.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const $t=".bs.carousel",Ht=".data-api",Ut="ArrowLeft",Ft="ArrowRight",Vt="next",zt="prev",Wt="left",Gt="right",Yt=`slide${$t}`,Xt=`slid${$t}`,Qt=`keydown${$t}`,Jt=`mouseenter${$t}`,Zt=`mouseleave${$t}`,Kt=`dragstart${$t}`,en=`load${$t}${Ht}`,tn=`click${$t}${Ht}`,nn="carousel",rn="active",on=".active",sn=".carousel-item",an=on+sn,ln={[Ut]:Gt,[Ft]:Wt},cn={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},un={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class hn extends xt{constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=Tt.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===nn&&this.cycle()}static get Default(){return cn}static get DefaultType(){return un}static get NAME(){return"carousel"}next(){this._slide(Vt)}nextWhenVisible(){!document.hidden&&Ue(this._element)&&this.next()}prev(){this._slide(zt)}pause(){this._isSliding&&Be(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?mt.one(this._element,Xt,(()=>this.cycle())):this.cycle())}to(e){const t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding)return void mt.one(this._element,Xt,(()=>this.to(e)));const n=this._getItemIndex(this._getActive());if(n===e)return;const r=e>n?Vt:zt;this._slide(r,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&mt.on(this._element,Qt,(e=>this._keydown(e))),"hover"===this._config.pause&&(mt.on(this._element,Jt,(()=>this.pause())),mt.on(this._element,Zt,(()=>this._maybeEnableCycle()))),this._config.touch&&Bt.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const e of Tt.find(".carousel-item img",this._element))mt.on(e,Kt,(e=>e.preventDefault()));const e={leftCallback:()=>this._slide(this._directionToOrder(Wt)),rightCallback:()=>this._slide(this._directionToOrder(Gt)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new Bt(this._element,e)}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;const t=ln[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;const t=Tt.findOne(on,this._indicatorsElement);t.classList.remove(rn),t.removeAttribute("aria-current");const n=Tt.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);n&&(n.classList.add(rn),n.setAttribute("aria-current","true"))}_updateInterval(){const e=this._activeElement||this._getActive();if(!e)return;const t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e,t=null){if(this._isSliding)return;const n=this._getActive(),r=e===Vt,i=t||Ke(this._getItems(),n,r,this._config.wrap);if(i===n)return;const o=this._getItemIndex(i),s=t=>mt.trigger(this._element,t,{relatedTarget:i,direction:this._orderToDirection(e),from:this._getItemIndex(n),to:o});if(s(Yt).defaultPrevented)return;if(!n||!i)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=i;const l=r?"carousel-item-start":"carousel-item-end",c=r?"carousel-item-next":"carousel-item-prev";i.classList.add(c),We(i),n.classList.add(l),i.classList.add(l);this._queueCallback((()=>{i.classList.remove(l,c),i.classList.add(rn),n.classList.remove(rn,c,l),this._isSliding=!1,s(Xt)}),n,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return Tt.findOne(an,this._element)}_getItems(){return Tt.find(sn,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return Xe()?e===Wt?zt:Vt:e===Wt?Vt:zt}_orderToDirection(e){return Xe()?e===zt?Wt:Gt:e===zt?Gt:Wt}static jQueryInterface(e){return this.each((function(){const t=hn.getOrCreateInstance(this,e);if("number"!=typeof e){if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}else t.to(e)}))}}mt.on(document,tn,"[data-bs-slide], [data-bs-slide-to]",(function(e){const t=Tt.getElementFromSelector(this);if(!t||!t.classList.contains(nn))return;e.preventDefault();const n=hn.getOrCreateInstance(t),r=this.getAttribute("data-bs-slide-to");return r?(n.to(r),void n._maybeEnableCycle()):"next"===bt.getDataAttribute(this,"slide")?(n.next(),void n._maybeEnableCycle()):(n.prev(),void n._maybeEnableCycle())})),mt.on(window,en,(()=>{const e=Tt.find('[data-bs-ride="carousel"]');for(const t of e)hn.getOrCreateInstance(t)})),Qe(hn);const fn=".bs.collapse",dn=`show${fn}`,pn=`shown${fn}`,mn=`hide${fn}`,gn=`hidden${fn}`,yn=`click${fn}.data-api`,vn="show",bn="collapse",wn="collapsing",xn=`:scope .${bn} .${bn}`,_n='[data-bs-toggle="collapse"]',Tn={parent:null,toggle:!0},En={parent:"(null|element)",toggle:"boolean"};class An extends xt{constructor(e,t){super(e,t),this._isTransitioning=!1,this._triggerArray=[];const n=Tt.find(_n);for(const e of n){const t=Tt.getSelectorFromElement(e),n=Tt.find(t).filter((e=>e===this._element));null!==t&&n.length&&this._triggerArray.push(e)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Tn}static get DefaultType(){return En}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((e=>e!==this._element)).map((e=>An.getOrCreateInstance(e,{toggle:!1})))),e.length&&e[0]._isTransitioning)return;if(mt.trigger(this._element,dn).defaultPrevented)return;for(const t of e)t.hide();const t=this._getDimension();this._element.classList.remove(bn),this._element.classList.add(wn),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const n=`scroll${t[0].toUpperCase()+t.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(wn),this._element.classList.add(bn,vn),this._element.style[t]="",mt.trigger(this._element,pn)}),this._element,!0),this._element.style[t]=`${this._element[n]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(mt.trigger(this._element,mn).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,We(this._element),this._element.classList.add(wn),this._element.classList.remove(bn,vn);for(const e of this._triggerArray){const t=Tt.getElementFromSelector(e);t&&!this._isShown(t)&&this._addAriaAndCollapsedClass([e],!1)}this._isTransitioning=!0;this._element.style[e]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(wn),this._element.classList.add(bn),mt.trigger(this._element,gn)}),this._element,!0)}_isShown(e=this._element){return e.classList.contains(vn)}_configAfterMerge(e){return e.toggle=Boolean(e.toggle),e.parent=He(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const e=this._getFirstLevelChildren(_n);for(const t of e){const e=Tt.getElementFromSelector(t);e&&this._addAriaAndCollapsedClass([t],this._isShown(e))}}_getFirstLevelChildren(e){const t=Tt.find(xn,this._config.parent);return Tt.find(e,this._config.parent).filter((e=>!t.includes(e)))}_addAriaAndCollapsedClass(e,t){if(e.length)for(const n of e)n.classList.toggle("collapsed",!t),n.setAttribute("aria-expanded",t)}static jQueryInterface(e){const t={};return"string"==typeof e&&/show|hide/.test(e)&&(t.toggle=!1),this.each((function(){const n=An.getOrCreateInstance(this,t);if("string"==typeof e){if(void 0===n[e])throw new TypeError(`No method named "${e}"`);n[e]()}}))}}mt.on(document,yn,_n,(function(e){("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault();for(const e of Tt.getMultipleElementsFromSelector(this))An.getOrCreateInstance(e,{toggle:!1}).toggle()})),Qe(An);const Sn="dropdown",Cn=".bs.dropdown",kn=".data-api",On="ArrowUp",Nn="ArrowDown",Dn=`hide${Cn}`,Ln=`hidden${Cn}`,Pn=`show${Cn}`,In=`shown${Cn}`,jn=`click${Cn}${kn}`,Mn=`keydown${Cn}${kn}`,qn=`keyup${Cn}${kn}`,Rn="show",Bn='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',$n=`${Bn}.${Rn}`,Hn=".dropdown-menu",Un=Xe()?"top-end":"top-start",Fn=Xe()?"top-start":"top-end",Vn=Xe()?"bottom-end":"bottom-start",zn=Xe()?"bottom-start":"bottom-end",Wn=Xe()?"left-start":"right-start",Gn=Xe()?"right-start":"left-start",Yn={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Xn={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Qn extends xt{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=Tt.next(this._element,Hn)[0]||Tt.prev(this._element,Hn)[0]||Tt.findOne(Hn,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Yn}static get DefaultType(){return Xn}static get NAME(){return Sn}toggle(){return this._isShown()?this.hide():this.show()}show(){if(Fe(this._element)||this._isShown())return;const e={relatedTarget:this._element};if(!mt.trigger(this._element,Pn,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const e of[].concat(...document.body.children))mt.on(e,"mouseover",ze);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Rn),this._element.classList.add(Rn),mt.trigger(this._element,In,e)}}hide(){if(Fe(this._element)||!this._isShown())return;const e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!mt.trigger(this._element,Dn,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))mt.off(e,"mouseover",ze);this._popper&&this._popper.destroy(),this._menu.classList.remove(Rn),this._element.classList.remove(Rn),this._element.setAttribute("aria-expanded","false"),bt.removeDataAttribute(this._menu,"popper"),mt.trigger(this._element,Ln,e)}}_getConfig(e){if("object"==typeof(e=super._getConfig(e)).reference&&!$e(e.reference)&&"function"!=typeof e.reference.getBoundingClientRect)throw new TypeError(`${Sn.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){let e=this._element;"parent"===this._config.reference?e=this._parent:$e(this._config.reference)?e=He(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);const t=this._getPopperConfig();this._popper=Pe(e,this._menu,t)}_isShown(){return this._menu.classList.contains(Rn)}_getPlacement(){const e=this._parent;if(e.classList.contains("dropend"))return Wn;if(e.classList.contains("dropstart"))return Gn;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";const t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return e.classList.contains("dropup")?t?Fn:Un:t?zn:Vn}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map((e=>Number.parseInt(e,10))):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){const e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(bt.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...Je(this._config.popperConfig,[e])}}_selectMenuItem({key:e,target:t}){const n=Tt.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((e=>Ue(e)));n.length&&Ke(n,t,e===Nn,!n.includes(t)).focus()}static jQueryInterface(e){return this.each((function(){const t=Qn.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}static clearMenus(e){if(2===e.button||"keyup"===e.type&&"Tab"!==e.key)return;const t=Tt.find($n);for(const n of t){const t=Qn.getInstance(n);if(!t||!1===t._config.autoClose)continue;const r=e.composedPath(),i=r.includes(t._menu);if(r.includes(t._element)||"inside"===t._config.autoClose&&!i||"outside"===t._config.autoClose&&i)continue;if(t._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;const o={relatedTarget:t._element};"click"===e.type&&(o.clickEvent=e),t._completeHide(o)}}static dataApiKeydownHandler(e){const t=/input|textarea/i.test(e.target.tagName),n="Escape"===e.key,r=[On,Nn].includes(e.key);if(!r&&!n)return;if(t&&!n)return;e.preventDefault();const i=this.matches(Bn)?this:Tt.prev(this,Bn)[0]||Tt.next(this,Bn)[0]||Tt.findOne(Bn,e.delegateTarget.parentNode),o=Qn.getOrCreateInstance(i);if(r)return e.stopPropagation(),o.show(),void o._selectMenuItem(e);o._isShown()&&(e.stopPropagation(),o.hide(),i.focus())}}mt.on(document,Mn,Bn,Qn.dataApiKeydownHandler),mt.on(document,Mn,Hn,Qn.dataApiKeydownHandler),mt.on(document,jn,Qn.clearMenus),mt.on(document,qn,Qn.clearMenus),mt.on(document,jn,Bn,(function(e){e.preventDefault(),Qn.getOrCreateInstance(this).toggle()})),Qe(Qn);const Jn="backdrop",Zn="show",Kn=`mousedown.bs.${Jn}`,er={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},tr={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class nr extends wt{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return er}static get DefaultType(){return tr}static get NAME(){return Jn}show(e){if(!this._config.isVisible)return void Je(e);this._append();const t=this._getElement();this._config.isAnimated&&We(t),t.classList.add(Zn),this._emulateAnimation((()=>{Je(e)}))}hide(e){this._config.isVisible?(this._getElement().classList.remove(Zn),this._emulateAnimation((()=>{this.dispose(),Je(e)}))):Je(e)}dispose(){this._isAppended&&(mt.off(this._element,Kn),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=He(e.rootElement),e}_append(){if(this._isAppended)return;const e=this._getElement();this._config.rootElement.append(e),mt.on(e,Kn,(()=>{Je(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(e){Ze(e,this._getElement(),this._config.isAnimated)}}const rr=".bs.focustrap",ir=`focusin${rr}`,or=`keydown.tab${rr}`,sr="backward",ar={autofocus:!0,trapElement:null},lr={autofocus:"boolean",trapElement:"element"};class cr extends wt{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return ar}static get DefaultType(){return lr}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),mt.off(document,rr),mt.on(document,ir,(e=>this._handleFocusin(e))),mt.on(document,or,(e=>this._handleKeydown(e))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,mt.off(document,rr))}_handleFocusin(e){const{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;const n=Tt.focusableChildren(t);0===n.length?t.focus():this._lastTabNavDirection===sr?n[n.length-1].focus():n[0].focus()}_handleKeydown(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?sr:"forward")}}const ur=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",hr=".sticky-top",fr="padding-right",dr="margin-right";class pr{constructor(){this._element=document.body}getWidth(){const e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,fr,(t=>t+e)),this._setElementAttributes(ur,fr,(t=>t+e)),this._setElementAttributes(hr,dr,(t=>t-e))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,fr),this._resetElementAttributes(ur,fr),this._resetElementAttributes(hr,dr)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,n){const r=this.getWidth();this._applyManipulationCallback(e,(e=>{if(e!==this._element&&window.innerWidth>e.clientWidth+r)return;this._saveInitialAttribute(e,t);const i=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,`${n(Number.parseFloat(i))}px`)}))}_saveInitialAttribute(e,t){const n=e.style.getPropertyValue(t);n&&bt.setDataAttribute(e,t,n)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,(e=>{const n=bt.getDataAttribute(e,t);null!==n?(bt.removeDataAttribute(e,t),e.style.setProperty(t,n)):e.style.removeProperty(t)}))}_applyManipulationCallback(e,t){if($e(e))t(e);else for(const n of Tt.find(e,this._element))t(n)}}const mr=".bs.modal",gr=`hide${mr}`,yr=`hidePrevented${mr}`,vr=`hidden${mr}`,br=`show${mr}`,wr=`shown${mr}`,xr=`resize${mr}`,_r=`click.dismiss${mr}`,Tr=`mousedown.dismiss${mr}`,Er=`keydown.dismiss${mr}`,Ar=`click${mr}.data-api`,Sr="modal-open",Cr="show",kr="modal-static",Or={backdrop:!0,focus:!0,keyboard:!0},Nr={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Dr extends xt{constructor(e,t){super(e,t),this._dialog=Tt.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new pr,this._addEventListeners()}static get Default(){return Or}static get DefaultType(){return Nr}static get NAME(){return"modal"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){if(this._isShown||this._isTransitioning)return;mt.trigger(this._element,br,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(Sr),this._adjustDialog(),this._backdrop.show((()=>this._showElement(e))))}hide(){if(!this._isShown||this._isTransitioning)return;mt.trigger(this._element,gr).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(Cr),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated()))}dispose(){mt.off(window,mr),mt.off(this._dialog,mr),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new nr({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new cr({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const t=Tt.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),We(this._element),this._element.classList.add(Cr);this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,mt.trigger(this._element,wr,{relatedTarget:e})}),this._dialog,this._isAnimated())}_addEventListeners(){mt.on(this._element,Er,(e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),mt.on(window,xr,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),mt.on(this._element,Tr,(e=>{mt.one(this._element,_r,(t=>{this._element===e.target&&this._element===t.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(Sr),this._resetAdjustments(),this._scrollBar.reset(),mt.trigger(this._element,vr)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(mt.trigger(this._element,yr).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;"hidden"===t||this._element.classList.contains(kr)||(e||(this._element.style.overflowY="hidden"),this._element.classList.add(kr),this._queueCallback((()=>{this._element.classList.remove(kr),this._queueCallback((()=>{this._element.style.overflowY=t}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),n=t>0;if(n&&!e){const e=Xe()?"paddingLeft":"paddingRight";this._element.style[e]=`${t}px`}if(!n&&e){const e=Xe()?"paddingRight":"paddingLeft";this._element.style[e]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each((function(){const n=Dr.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===n[e])throw new TypeError(`No method named "${e}"`);n[e](t)}}))}}mt.on(document,Ar,'[data-bs-toggle="modal"]',(function(e){const t=Tt.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),mt.one(t,br,(e=>{e.defaultPrevented||mt.one(t,vr,(()=>{Ue(this)&&this.focus()}))}));const n=Tt.findOne(".modal.show");n&&Dr.getInstance(n).hide();Dr.getOrCreateInstance(t).toggle(this)})),Et(Dr),Qe(Dr);const Lr=".bs.offcanvas",Pr=".data-api",Ir=`load${Lr}${Pr}`,jr="show",Mr="showing",qr="hiding",Rr=".offcanvas.show",Br=`show${Lr}`,$r=`shown${Lr}`,Hr=`hide${Lr}`,Ur=`hidePrevented${Lr}`,Fr=`hidden${Lr}`,Vr=`resize${Lr}`,zr=`click${Lr}${Pr}`,Wr=`keydown.dismiss${Lr}`,Gr={backdrop:!0,keyboard:!0,scroll:!1},Yr={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Xr extends xt{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Gr}static get DefaultType(){return Yr}static get NAME(){return"offcanvas"}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){if(this._isShown)return;if(mt.trigger(this._element,Br,{relatedTarget:e}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||(new pr).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Mr);this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(jr),this._element.classList.remove(Mr),mt.trigger(this._element,$r,{relatedTarget:e})}),this._element,!0)}hide(){if(!this._isShown)return;if(mt.trigger(this._element,Hr).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(qr),this._backdrop.hide();this._queueCallback((()=>{this._element.classList.remove(jr,qr),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new pr).reset(),mt.trigger(this._element,Fr)}),this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const e=Boolean(this._config.backdrop);return new nr({className:"offcanvas-backdrop",isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{"static"!==this._config.backdrop?this.hide():mt.trigger(this._element,Ur)}:null})}_initializeFocusTrap(){return new cr({trapElement:this._element})}_addEventListeners(){mt.on(this._element,Wr,(e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():mt.trigger(this._element,Ur))}))}static jQueryInterface(e){return this.each((function(){const t=Xr.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}mt.on(document,zr,'[data-bs-toggle="offcanvas"]',(function(e){const t=Tt.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),Fe(this))return;mt.one(t,Fr,(()=>{Ue(this)&&this.focus()}));const n=Tt.findOne(Rr);n&&n!==t&&Xr.getInstance(n).hide();Xr.getOrCreateInstance(t).toggle(this)})),mt.on(window,Ir,(()=>{for(const e of Tt.find(Rr))Xr.getOrCreateInstance(e).show()})),mt.on(window,Vr,(()=>{for(const e of Tt.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(e).position&&Xr.getOrCreateInstance(e).hide()})),Et(Xr),Qe(Xr);const Qr={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Jr=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Zr=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Kr=(e,t)=>{const n=e.nodeName.toLowerCase();return t.includes(n)?!Jr.has(n)||Boolean(Zr.test(e.nodeValue)):t.filter((e=>e instanceof RegExp)).some((e=>e.test(n)))};const ei={allowList:Qr,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},ti={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},ni={entry:"(string|element|function|null)",selector:"(string|element)"};class ri extends wt{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return ei}static get DefaultType(){return ti}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map((e=>this._resolvePossibleFunction(e))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[t,n]of Object.entries(this._config.content))this._setContent(e,n,t);const t=e.children[0],n=this._resolvePossibleFunction(this._config.extraClass);return n&&t.classList.add(...n.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[t,n]of Object.entries(e))super._typeCheckConfig({selector:t,entry:n},ni)}_setContent(e,t,n){const r=Tt.findOne(n,e);r&&((t=this._resolvePossibleFunction(t))?$e(t)?this._putElementInTemplate(He(t),r):this._config.html?r.innerHTML=this._maybeSanitize(t):r.textContent=t:r.remove())}_maybeSanitize(e){return this._config.sanitize?function(e,t,n){if(!e.length)return e;if(n&&"function"==typeof n)return n(e);const r=(new window.DOMParser).parseFromString(e,"text/html"),i=[].concat(...r.body.querySelectorAll("*"));for(const e of i){const n=e.nodeName.toLowerCase();if(!Object.keys(t).includes(n)){e.remove();continue}const r=[].concat(...e.attributes),i=[].concat(t["*"]||[],t[n]||[]);for(const t of r)Kr(t,i)||e.removeAttribute(t.nodeName)}return r.body.innerHTML}(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return Je(e,[this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}const ii=new Set(["sanitize","allowList","sanitizeFn"]),oi="fade",si="show",ai=".tooltip-inner",li=".modal",ci="hide.bs.modal",ui="hover",hi="focus",fi={AUTO:"auto",TOP:"top",RIGHT:Xe()?"left":"right",BOTTOM:"bottom",LEFT:Xe()?"right":"left"},di={allowList:Qr,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},pi={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class mi extends xt{constructor(e,t){super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return di}static get DefaultType(){return pi}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),mt.off(this._element.closest(li),ci,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const e=mt.trigger(this._element,this.constructor.eventName("show")),t=(Ve(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();const n=this._getTipElement();this._element.setAttribute("aria-describedby",n.getAttribute("id"));const{container:r}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(r.append(n),mt.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(n),n.classList.add(si),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))mt.on(e,"mouseover",ze);this._queueCallback((()=>{mt.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(mt.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented)return;if(this._getTipElement().classList.remove(si),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))mt.off(e,"mouseover",ze);this._activeTrigger.click=!1,this._activeTrigger[hi]=!1,this._activeTrigger[ui]=!1,this._isHovered=null;this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),mt.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove(oi,si),t.classList.add(`bs-${this.constructor.NAME}-auto`);const n=(e=>{do{e+=Math.floor(1e6*Math.random())}while(document.getElementById(e));return e})(this.constructor.NAME).toString();return t.setAttribute("id",n),this._isAnimated()&&t.classList.add(oi),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new ri({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[ai]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(oi)}_isShown(){return this.tip&&this.tip.classList.contains(si)}_createPopper(e){const t=Je(this._config.placement,[this,e,this._element]),n=fi[t.toUpperCase()];return Pe(this._element,e,this._getPopperConfig(n))}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map((e=>Number.parseInt(e,10))):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return Je(e,[this._element])}_getPopperConfig(e){const t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...Je(this._config.popperConfig,[t])}}_setListeners(){const e=this._config.trigger.split(" ");for(const t of e)if("click"===t)mt.on(this._element,this.constructor.eventName("click"),this._config.selector,(e=>{this._initializeOnDelegatedTarget(e).toggle()}));else if("manual"!==t){const e=t===ui?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),n=t===ui?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");mt.on(this._element,e,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?hi:ui]=!0,t._enter()})),mt.on(this._element,n,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?hi:ui]=t._element.contains(e.relatedTarget),t._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},mt.on(this._element.closest(li),ci,this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const t=bt.getDataAttributes(this._element);for(const e of Object.keys(t))ii.has(e)&&delete t[e];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:He(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const[t,n]of Object.entries(this._config))this.constructor.Default[t]!==n&&(e[t]=n);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each((function(){const t=mi.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}}Qe(mi);const gi=".popover-header",yi=".popover-body",vi={...mi.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},bi={...mi.DefaultType,content:"(null|string|element|function)"};class wi extends mi{static get Default(){return vi}static get DefaultType(){return bi}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[gi]:this._getTitle(),[yi]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each((function(){const t=wi.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}}Qe(wi);const xi=".bs.scrollspy",_i=`activate${xi}`,Ti=`click${xi}`,Ei=`load${xi}.data-api`,Ai="active",Si="[href]",Ci=".nav-link",ki=`${Ci}, .nav-item > ${Ci}, .list-group-item`,Oi={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},Ni={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Di extends xt{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Oi}static get DefaultType(){return Ni}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const e of this._observableSections.values())this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=He(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map((e=>Number.parseFloat(e)))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(mt.off(this._config.target,Ti),mt.on(this._config.target,Ti,Si,(e=>{const t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();const n=this._rootElement||window,r=t.offsetTop-this._element.offsetTop;if(n.scrollTo)return void n.scrollTo({top:r,behavior:"smooth"});n.scrollTop=r}})))}_getNewObserver(){const e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((e=>this._observerCallback(e)),e)}_observerCallback(e){const t=e=>this._targetLinks.get(`#${e.target.id}`),n=e=>{this._previousScrollData.visibleEntryTop=e.target.offsetTop,this._process(t(e))},r=(this._rootElement||document.documentElement).scrollTop,i=r>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=r;for(const o of e){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(o));continue}const e=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(i&&e){if(n(o),!r)return}else i||e||n(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const e=Tt.find(Si,this._config.target);for(const t of e){if(!t.hash||Fe(t))continue;const e=Tt.findOne(decodeURI(t.hash),this._element);Ue(e)&&(this._targetLinks.set(decodeURI(t.hash),t),this._observableSections.set(t.hash,e))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add(Ai),this._activateParents(e),mt.trigger(this._element,_i,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item"))Tt.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add(Ai);else for(const t of Tt.parents(e,".nav, .list-group"))for(const e of Tt.prev(t,ki))e.classList.add(Ai)}_clearActiveClass(e){e.classList.remove(Ai);const t=Tt.find(`${Si}.${Ai}`,e);for(const e of t)e.classList.remove(Ai)}static jQueryInterface(e){return this.each((function(){const t=Di.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}))}}mt.on(window,Ei,(()=>{for(const e of Tt.find('[data-bs-spy="scroll"]'))Di.getOrCreateInstance(e)})),Qe(Di);const Li=".bs.tab",Pi=`hide${Li}`,Ii=`hidden${Li}`,ji=`show${Li}`,Mi=`shown${Li}`,qi=`click${Li}`,Ri=`keydown${Li}`,Bi=`load${Li}`,$i="ArrowLeft",Hi="ArrowRight",Ui="ArrowUp",Fi="ArrowDown",Vi="Home",zi="End",Wi="active",Gi="fade",Yi="show",Xi=".dropdown-toggle",Qi=`:not(${Xi})`,Ji='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Zi=`${`.nav-link${Qi}, .list-group-item${Qi}, [role="tab"]${Qi}`}, ${Ji}`,Ki=`.${Wi}[data-bs-toggle="tab"], .${Wi}[data-bs-toggle="pill"], .${Wi}[data-bs-toggle="list"]`;class eo extends xt{constructor(e){super(e),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),mt.on(this._element,Ri,(e=>this._keydown(e))))}static get NAME(){return"tab"}show(){const e=this._element;if(this._elemIsActive(e))return;const t=this._getActiveElem(),n=t?mt.trigger(t,Pi,{relatedTarget:e}):null;mt.trigger(e,ji,{relatedTarget:t}).defaultPrevented||n&&n.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){if(!e)return;e.classList.add(Wi),this._activate(Tt.getElementFromSelector(e));this._queueCallback((()=>{"tab"===e.getAttribute("role")?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),mt.trigger(e,Mi,{relatedTarget:t})):e.classList.add(Yi)}),e,e.classList.contains(Gi))}_deactivate(e,t){if(!e)return;e.classList.remove(Wi),e.blur(),this._deactivate(Tt.getElementFromSelector(e));this._queueCallback((()=>{"tab"===e.getAttribute("role")?(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),mt.trigger(e,Ii,{relatedTarget:t})):e.classList.remove(Yi)}),e,e.classList.contains(Gi))}_keydown(e){if(![$i,Hi,Ui,Fi,Vi,zi].includes(e.key))return;e.stopPropagation(),e.preventDefault();const t=this._getChildren().filter((e=>!Fe(e)));let n;if([Vi,zi].includes(e.key))n=t[e.key===Vi?0:t.length-1];else{const r=[Hi,Fi].includes(e.key);n=Ke(t,e.target,r,!0)}n&&(n.focus({preventScroll:!0}),eo.getOrCreateInstance(n).show())}_getChildren(){return Tt.find(Zi,this._parent)}_getActiveElem(){return this._getChildren().find((e=>this._elemIsActive(e)))||null}_setInitialAttributes(e,t){this._setAttributeIfNotExists(e,"role","tablist");for(const e of t)this._setInitialAttributesOnChild(e)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);const t=this._elemIsActive(e),n=this._getOuterElement(e);e.setAttribute("aria-selected",t),n!==e&&this._setAttributeIfNotExists(n,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){const t=Tt.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){const n=this._getOuterElement(e);if(!n.classList.contains("dropdown"))return;const r=(e,r)=>{const i=Tt.findOne(e,n);i&&i.classList.toggle(r,t)};r(Xi,Wi),r(".dropdown-menu",Yi),n.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,n){e.hasAttribute(t)||e.setAttribute(t,n)}_elemIsActive(e){return e.classList.contains(Wi)}_getInnerElement(e){return e.matches(Zi)?e:Tt.findOne(Zi,e)}_getOuterElement(e){return e.closest(".nav-item, .list-group-item")||e}static jQueryInterface(e){return this.each((function(){const t=eo.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}))}}mt.on(document,qi,Ji,(function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),Fe(this)||eo.getOrCreateInstance(this).show()})),mt.on(window,Bi,(()=>{for(const e of Tt.find(Ki))eo.getOrCreateInstance(e)})),Qe(eo);const to=".bs.toast",no=`mouseover${to}`,ro=`mouseout${to}`,io=`focusin${to}`,oo=`focusout${to}`,so=`hide${to}`,ao=`hidden${to}`,lo=`show${to}`,co=`shown${to}`,uo="hide",ho="show",fo="showing",po={animation:"boolean",autohide:"boolean",delay:"number"},mo={animation:!0,autohide:!0,delay:5e3};class go extends xt{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return mo}static get DefaultType(){return po}static get NAME(){return"toast"}show(){if(mt.trigger(this._element,lo).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");this._element.classList.remove(uo),We(this._element),this._element.classList.add(ho,fo),this._queueCallback((()=>{this._element.classList.remove(fo),mt.trigger(this._element,co),this._maybeScheduleHide()}),this._element,this._config.animation)}hide(){if(!this.isShown())return;if(mt.trigger(this._element,so).defaultPrevented)return;this._element.classList.add(fo),this._queueCallback((()=>{this._element.classList.add(uo),this._element.classList.remove(fo,ho),mt.trigger(this._element,ao)}),this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(ho),super.dispose()}isShown(){return this._element.classList.contains(ho)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();const n=e.relatedTarget;this._element===n||this._element.contains(n)||this._maybeScheduleHide()}_setListeners(){mt.on(this._element,no,(e=>this._onInteraction(e,!0))),mt.on(this._element,ro,(e=>this._onInteraction(e,!1))),mt.on(this._element,io,(e=>this._onInteraction(e,!0))),mt.on(this._element,oo,(e=>this._onInteraction(e,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each((function(){const t=go.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}Et(go),Qe(go)},568:(e,t)=>{"use strict";function n(e){if(e.prev&&(e.prev.next=e.next),e.next&&(e.next.prev=e.prev),e.parent){var t=e.parent.children,n=t.lastIndexOf(e);n>=0&&t.splice(n,1)}e.next=null,e.prev=null,e.parent=null}Object.defineProperty(t,"__esModule",{value:!0}),t.removeElement=n,t.replaceElement=function(e,t){var n=t.prev=e.prev;n&&(n.next=t);var r=t.next=e.next;r&&(r.prev=t);var i=t.parent=e.parent;if(i){var o=i.children;o[o.lastIndexOf(e)]=t,e.parent=null}},t.appendChild=function(e,t){if(n(t),t.next=null,t.parent=e,e.children.push(t)>1){var r=e.children[e.children.length-2];r.next=t,t.prev=r}else t.prev=null},t.append=function(e,t){n(t);var r=e.parent,i=e.next;if(t.next=i,t.prev=e,e.next=t,t.parent=r,i){if(i.prev=t,r){var o=r.children;o.splice(o.lastIndexOf(i),0,t)}}else r&&r.children.push(t)},t.prependChild=function(e,t){if(n(t),t.parent=e,t.prev=null,1!==e.children.unshift(t)){var r=e.children[1];r.prev=t,t.next=r}else t.next=null},t.prepend=function(e,t){n(t);var r=e.parent;if(r){var i=r.children;i.splice(i.indexOf(e),0,t)}e.prev&&(e.prev.next=t);t.parent=r,t.prev=e.prev,t.next=e,e.prev=t}},855:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.replaceCodePoint=t.fromCodePoint=void 0;var r=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]);function i(e){var t;return e>=55296&&e<=57343||e>1114111?65533:null!==(t=r.get(e))&&void 0!==t?t:e}t.fromCodePoint=null!==(n=String.fromCodePoint)&&void 0!==n?n:function(e){var t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+=String.fromCharCode(e)},t.replaceCodePoint=i,t.default=function(e){return(0,t.fromCodePoint)(i(e))}},1019:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.attributeNames=t.elementNames=void 0,t.elementNames=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map((function(e){return[e.toLowerCase(),e]}))),t.attributeNames=new Map(["definitionURL","attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map((function(e){return[e.toLowerCase(),e]})))},1106:(e,t,n)=>{"use strict";let{nanoid:r}=n(5042),{isAbsolute:i,resolve:o}=n(197),{SourceMapConsumer:s,SourceMapGenerator:a}=n(1866),{fileURLToPath:l,pathToFileURL:c}=n(2739),u=n(3614),h=n(3878),f=n(9746),d=Symbol("fromOffsetCache"),p=Boolean(s&&a),m=Boolean(o&&i);class g{get from(){return this.file||this.id}constructor(e,t={}){if(null==e||"object"==typeof e&&!e.toString)throw new Error(`PostCSS received ${e} instead of CSS string`);if(this.css=e.toString(),"\ufeff"===this.css[0]||"￾"===this.css[0]?(this.hasBOM=!0,this.css=this.css.slice(1)):this.hasBOM=!1,this.document=this.css,t.document&&(this.document=t.document.toString()),t.from&&(!m||/^\w+:\/\//.test(t.from)||i(t.from)?this.file=t.from:this.file=o(t.from)),m&&p){let e=new h(this.css,t);if(e.text){this.map=e;let t=e.consumer().file;!this.file&&t&&(this.file=this.mapResolve(t))}}this.file||(this.id="<input css "+r(6)+">"),this.map&&(this.map.file=this.from)}error(e,t,n,r={}){let i,o,s;if(t&&"object"==typeof t){let e=t,r=n;if("number"==typeof e.offset){let r=this.fromOffset(e.offset);t=r.line,n=r.col}else t=e.line,n=e.column;if("number"==typeof r.offset){let e=this.fromOffset(r.offset);o=e.line,i=e.col}else o=r.line,i=r.column}else if(!n){let e=this.fromOffset(t);t=e.line,n=e.col}let a=this.origin(t,n,o,i);return s=a?new u(e,void 0===a.endLine?a.line:{column:a.column,line:a.line},void 0===a.endLine?a.column:{column:a.endColumn,line:a.endLine},a.source,a.file,r.plugin):new u(e,void 0===o?t:{column:n,line:t},void 0===o?n:{column:i,line:o},this.css,this.file,r.plugin),s.input={column:n,endColumn:i,endLine:o,line:t,source:this.css},this.file&&(c&&(s.input.url=c(this.file).toString()),s.input.file=this.file),s}fromOffset(e){let t,n;if(this[d])n=this[d];else{let e=this.css.split("\n");n=new Array(e.length);let t=0;for(let r=0,i=e.length;r<i;r++)n[r]=t,t+=e[r].length+1;this[d]=n}t=n[n.length-1];let r=0;if(e>=t)r=n.length-1;else{let t,i=n.length-2;for(;r<i;)if(t=r+(i-r>>1),e<n[t])i=t-1;else{if(!(e>=n[t+1])){r=t;break}r=t+1}}return{col:e-n[r]+1,line:r+1}}mapResolve(e){return/^\w+:\/\//.test(e)?e:o(this.map.consumer().sourceRoot||this.map.root||".",e)}origin(e,t,n,r){if(!this.map)return!1;let o,s,a=this.map.consumer(),u=a.originalPositionFor({column:t,line:e});if(!u.source)return!1;"number"==typeof n&&(o=a.originalPositionFor({column:r,line:n})),s=i(u.source)?c(u.source):new URL(u.source,this.map.consumer().sourceRoot||c(this.map.mapFile));let h={column:u.column,endColumn:o&&o.column,endLine:o&&o.line,line:u.line,url:s.toString()};if("file:"===s.protocol){if(!l)throw new Error("file: protocol is not available in this PostCSS build");h.file=l(s)}let f=a.sourceContentFor(u.source);return f&&(h.source=f),h}toJSON(){let e={};for(let t of["hasBOM","css","file","id"])null!=this[t]&&(e[t]=this[t]);return this.map&&(e.map={...this.map},e.map.consumerCache&&(e.map.consumerCache=void 0)),e}}e.exports=g,g.default=g,f&&f.registerInput&&f.registerInput(g)},1161:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.filter=function(e,t,n,r){void 0===n&&(n=!0);void 0===r&&(r=1/0);return i(e,Array.isArray(t)?t:[t],n,r)},t.find=i,t.findOneChild=function(e,t){return t.find(e)},t.findOne=function e(t,n,i){void 0===i&&(i=!0);for(var o=Array.isArray(n)?n:[n],s=0;s<o.length;s++){var a=o[s];if((0,r.isTag)(a)&&t(a))return a;if(i&&(0,r.hasChildren)(a)&&a.children.length>0){var l=e(t,a.children,!0);if(l)return l}}return null},t.existsOne=function e(t,n){return(Array.isArray(n)?n:[n]).some((function(n){return(0,r.isTag)(n)&&t(n)||(0,r.hasChildren)(n)&&e(t,n.children)}))},t.findAll=function(e,t){for(var n=[],i=[Array.isArray(t)?t:[t]],o=[0];;)if(o[0]>=i[0].length){if(1===i.length)return n;i.shift(),o.shift()}else{var s=i[0][o[0]++];(0,r.isTag)(s)&&e(s)&&n.push(s),(0,r.hasChildren)(s)&&s.children.length>0&&(o.unshift(0),i.unshift(s.children))}};var r=n(4128);function i(e,t,n,i){for(var o=[],s=[Array.isArray(t)?t:[t]],a=[0];;)if(a[0]>=s[0].length){if(1===a.length)return o;s.shift(),a.shift()}else{var l=s[0][a[0]++];if(e(l)&&(o.push(l),--i<=0))return o;n&&(0,r.hasChildren)(l)&&l.children.length>0&&(a.unshift(0),s.unshift(l.children))}}},1534:(e,t,n)=>{"use strict";let r=n(7793),i=n(1752);class o extends r{get selectors(){return i.comma(this.selector)}set selectors(e){let t=this.selector?this.selector.match(/,\s*/):null,n=t?t[0]:","+this.raw("between","beforeOpen");this.selector=e.join(n)}constructor(e){super(e),this.type="rule",this.nodes||(this.nodes=[])}}e.exports=o,o.default=o,r.registerRule(o)},1560:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.escapeText=t.escapeAttribute=t.escapeUTF8=t.escape=t.encodeXML=t.getCodePoint=t.xmlReplacer=void 0,t.xmlReplacer=/["&'<>$\x80-\uFFFF]/g;var n=new Map([[34,"&quot;"],[38,"&amp;"],[39,"&apos;"],[60,"&lt;"],[62,"&gt;"]]);function r(e){for(var r,i="",o=0;null!==(r=t.xmlReplacer.exec(e));){var s=r.index,a=e.charCodeAt(s),l=n.get(a);void 0!==l?(i+=e.substring(o,s)+l,o=s+1):(i+="".concat(e.substring(o,s),"&#x").concat((0,t.getCodePoint)(e,s).toString(16),";"),o=t.xmlReplacer.lastIndex+=Number(55296==(64512&a)))}return i+e.substr(o)}function i(e,t){return function(n){for(var r,i=0,o="";r=e.exec(n);)i!==r.index&&(o+=n.substring(i,r.index)),o+=t.get(r[0].charCodeAt(0)),i=r.index+1;return o+n.substring(i)}}t.getCodePoint=null!=String.prototype.codePointAt?function(e,t){return e.codePointAt(t)}:function(e,t){return 55296==(64512&e.charCodeAt(t))?1024*(e.charCodeAt(t)-55296)+e.charCodeAt(t+1)-56320+65536:e.charCodeAt(t)},t.encodeXML=r,t.escape=r,t.escapeUTF8=i(/[&<>'"]/g,n),t.escapeAttribute=i(/["&\u00A0]/g,new Map([[34,"&quot;"],[38,"&amp;"],[160,"&nbsp;"]])),t.escapeText=i(/[&<>\u00A0]/g,new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[160,"&nbsp;"]]))},1752:e=>{"use strict";let t={comma:e=>t.split(e,[","],!0),space:e=>t.split(e,[" ","\n","\t"]),split(e,t,n){let r=[],i="",o=!1,s=0,a=!1,l="",c=!1;for(let n of e)c?c=!1:"\\"===n?c=!0:a?n===l&&(a=!1):'"'===n||"'"===n?(a=!0,l=n):"("===n?s+=1:")"===n?s>0&&(s-=1):0===s&&t.includes(n)&&(o=!0),o?(""!==i&&r.push(i.trim()),i="",o=!1):i+=n;return(n||""!==i)&&r.push(i.trim()),r}};e.exports=t,t.default=t},1866:()=>{},1941:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.hasChildren=t.isDocument=t.isComment=t.isText=t.isCDATA=t.isTag=void 0,i(n(9124),t),i(n(2851),t),i(n(568),t),i(n(1161),t),i(n(1974),t),i(n(5936),t),i(n(2772),t);var o=n(4128);Object.defineProperty(t,"isTag",{enumerable:!0,get:function(){return o.isTag}}),Object.defineProperty(t,"isCDATA",{enumerable:!0,get:function(){return o.isCDATA}}),Object.defineProperty(t,"isText",{enumerable:!0,get:function(){return o.isText}}),Object.defineProperty(t,"isComment",{enumerable:!0,get:function(){return o.isComment}}),Object.defineProperty(t,"isDocument",{enumerable:!0,get:function(){return o.isDocument}}),Object.defineProperty(t,"hasChildren",{enumerable:!0,get:function(){return o.hasChildren}})},1974:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.testElement=function(e,t){var n=l(e);return!n||n(t)},t.getElements=function(e,t,n,r){void 0===r&&(r=1/0);var o=l(e);return o?(0,i.filter)(o,t,n,r):[]},t.getElementById=function(e,t,n){void 0===n&&(n=!0);Array.isArray(t)||(t=[t]);return(0,i.findOne)(s("id",e),t,n)},t.getElementsByTagName=function(e,t,n,r){void 0===n&&(n=!0);void 0===r&&(r=1/0);return(0,i.filter)(o.tag_name(e),t,n,r)},t.getElementsByClassName=function(e,t,n,r){void 0===n&&(n=!0);void 0===r&&(r=1/0);return(0,i.filter)(s("class",e),t,n,r)},t.getElementsByTagType=function(e,t,n,r){void 0===n&&(n=!0);void 0===r&&(r=1/0);return(0,i.filter)(o.tag_type(e),t,n,r)};var r=n(4128),i=n(1161),o={tag_name:function(e){return"function"==typeof e?function(t){return(0,r.isTag)(t)&&e(t.name)}:"*"===e?r.isTag:function(t){return(0,r.isTag)(t)&&t.name===e}},tag_type:function(e){return"function"==typeof e?function(t){return e(t.type)}:function(t){return t.type===e}},tag_contains:function(e){return"function"==typeof e?function(t){return(0,r.isText)(t)&&e(t.data)}:function(t){return(0,r.isText)(t)&&t.data===e}}};function s(e,t){return"function"==typeof t?function(n){return(0,r.isTag)(n)&&t(n.attribs[e])}:function(n){return(0,r.isTag)(n)&&n.attribs[e]===t}}function a(e,t){return function(n){return e(n)||t(n)}}function l(e){var t=Object.keys(e).map((function(t){var n=e[t];return Object.prototype.hasOwnProperty.call(o,t)?o[t](n):s(t,n)}));return 0===t.length?null:t.reduce(a)}},2125:(e,t)=>{"use strict";function n(e){for(var t=1;t<e.length;t++)e[t][0]+=e[t-1][0]+1;return e}Object.defineProperty(t,"__esModule",{value:!0}),t.default=new Map(n([[9,"&Tab;"],[0,"&NewLine;"],[22,"&excl;"],[0,"&quot;"],[0,"&num;"],[0,"&dollar;"],[0,"&percnt;"],[0,"&amp;"],[0,"&apos;"],[0,"&lpar;"],[0,"&rpar;"],[0,"&ast;"],[0,"&plus;"],[0,"&comma;"],[1,"&period;"],[0,"&sol;"],[10,"&colon;"],[0,"&semi;"],[0,{v:"&lt;",n:8402,o:"&nvlt;"}],[0,{v:"&equals;",n:8421,o:"&bne;"}],[0,{v:"&gt;",n:8402,o:"&nvgt;"}],[0,"&quest;"],[0,"&commat;"],[26,"&lbrack;"],[0,"&bsol;"],[0,"&rbrack;"],[0,"&Hat;"],[0,"&lowbar;"],[0,"&DiacriticalGrave;"],[5,{n:106,o:"&fjlig;"}],[20,"&lbrace;"],[0,"&verbar;"],[0,"&rbrace;"],[34,"&nbsp;"],[0,"&iexcl;"],[0,"&cent;"],[0,"&pound;"],[0,"&curren;"],[0,"&yen;"],[0,"&brvbar;"],[0,"&sect;"],[0,"&die;"],[0,"&copy;"],[0,"&ordf;"],[0,"&laquo;"],[0,"&not;"],[0,"&shy;"],[0,"&circledR;"],[0,"&macr;"],[0,"&deg;"],[0,"&PlusMinus;"],[0,"&sup2;"],[0,"&sup3;"],[0,"&acute;"],[0,"&micro;"],[0,"&para;"],[0,"&centerdot;"],[0,"&cedil;"],[0,"&sup1;"],[0,"&ordm;"],[0,"&raquo;"],[0,"&frac14;"],[0,"&frac12;"],[0,"&frac34;"],[0,"&iquest;"],[0,"&Agrave;"],[0,"&Aacute;"],[0,"&Acirc;"],[0,"&Atilde;"],[0,"&Auml;"],[0,"&angst;"],[0,"&AElig;"],[0,"&Ccedil;"],[0,"&Egrave;"],[0,"&Eacute;"],[0,"&Ecirc;"],[0,"&Euml;"],[0,"&Igrave;"],[0,"&Iacute;"],[0,"&Icirc;"],[0,"&Iuml;"],[0,"&ETH;"],[0,"&Ntilde;"],[0,"&Ograve;"],[0,"&Oacute;"],[0,"&Ocirc;"],[0,"&Otilde;"],[0,"&Ouml;"],[0,"&times;"],[0,"&Oslash;"],[0,"&Ugrave;"],[0,"&Uacute;"],[0,"&Ucirc;"],[0,"&Uuml;"],[0,"&Yacute;"],[0,"&THORN;"],[0,"&szlig;"],[0,"&agrave;"],[0,"&aacute;"],[0,"&acirc;"],[0,"&atilde;"],[0,"&auml;"],[0,"&aring;"],[0,"&aelig;"],[0,"&ccedil;"],[0,"&egrave;"],[0,"&eacute;"],[0,"&ecirc;"],[0,"&euml;"],[0,"&igrave;"],[0,"&iacute;"],[0,"&icirc;"],[0,"&iuml;"],[0,"&eth;"],[0,"&ntilde;"],[0,"&ograve;"],[0,"&oacute;"],[0,"&ocirc;"],[0,"&otilde;"],[0,"&ouml;"],[0,"&div;"],[0,"&oslash;"],[0,"&ugrave;"],[0,"&uacute;"],[0,"&ucirc;"],[0,"&uuml;"],[0,"&yacute;"],[0,"&thorn;"],[0,"&yuml;"],[0,"&Amacr;"],[0,"&amacr;"],[0,"&Abreve;"],[0,"&abreve;"],[0,"&Aogon;"],[0,"&aogon;"],[0,"&Cacute;"],[0,"&cacute;"],[0,"&Ccirc;"],[0,"&ccirc;"],[0,"&Cdot;"],[0,"&cdot;"],[0,"&Ccaron;"],[0,"&ccaron;"],[0,"&Dcaron;"],[0,"&dcaron;"],[0,"&Dstrok;"],[0,"&dstrok;"],[0,"&Emacr;"],[0,"&emacr;"],[2,"&Edot;"],[0,"&edot;"],[0,"&Eogon;"],[0,"&eogon;"],[0,"&Ecaron;"],[0,"&ecaron;"],[0,"&Gcirc;"],[0,"&gcirc;"],[0,"&Gbreve;"],[0,"&gbreve;"],[0,"&Gdot;"],[0,"&gdot;"],[0,"&Gcedil;"],[1,"&Hcirc;"],[0,"&hcirc;"],[0,"&Hstrok;"],[0,"&hstrok;"],[0,"&Itilde;"],[0,"&itilde;"],[0,"&Imacr;"],[0,"&imacr;"],[2,"&Iogon;"],[0,"&iogon;"],[0,"&Idot;"],[0,"&imath;"],[0,"&IJlig;"],[0,"&ijlig;"],[0,"&Jcirc;"],[0,"&jcirc;"],[0,"&Kcedil;"],[0,"&kcedil;"],[0,"&kgreen;"],[0,"&Lacute;"],[0,"&lacute;"],[0,"&Lcedil;"],[0,"&lcedil;"],[0,"&Lcaron;"],[0,"&lcaron;"],[0,"&Lmidot;"],[0,"&lmidot;"],[0,"&Lstrok;"],[0,"&lstrok;"],[0,"&Nacute;"],[0,"&nacute;"],[0,"&Ncedil;"],[0,"&ncedil;"],[0,"&Ncaron;"],[0,"&ncaron;"],[0,"&napos;"],[0,"&ENG;"],[0,"&eng;"],[0,"&Omacr;"],[0,"&omacr;"],[2,"&Odblac;"],[0,"&odblac;"],[0,"&OElig;"],[0,"&oelig;"],[0,"&Racute;"],[0,"&racute;"],[0,"&Rcedil;"],[0,"&rcedil;"],[0,"&Rcaron;"],[0,"&rcaron;"],[0,"&Sacute;"],[0,"&sacute;"],[0,"&Scirc;"],[0,"&scirc;"],[0,"&Scedil;"],[0,"&scedil;"],[0,"&Scaron;"],[0,"&scaron;"],[0,"&Tcedil;"],[0,"&tcedil;"],[0,"&Tcaron;"],[0,"&tcaron;"],[0,"&Tstrok;"],[0,"&tstrok;"],[0,"&Utilde;"],[0,"&utilde;"],[0,"&Umacr;"],[0,"&umacr;"],[0,"&Ubreve;"],[0,"&ubreve;"],[0,"&Uring;"],[0,"&uring;"],[0,"&Udblac;"],[0,"&udblac;"],[0,"&Uogon;"],[0,"&uogon;"],[0,"&Wcirc;"],[0,"&wcirc;"],[0,"&Ycirc;"],[0,"&ycirc;"],[0,"&Yuml;"],[0,"&Zacute;"],[0,"&zacute;"],[0,"&Zdot;"],[0,"&zdot;"],[0,"&Zcaron;"],[0,"&zcaron;"],[19,"&fnof;"],[34,"&imped;"],[63,"&gacute;"],[65,"&jmath;"],[142,"&circ;"],[0,"&caron;"],[16,"&breve;"],[0,"&DiacriticalDot;"],[0,"&ring;"],[0,"&ogon;"],[0,"&DiacriticalTilde;"],[0,"&dblac;"],[51,"&DownBreve;"],[127,"&Alpha;"],[0,"&Beta;"],[0,"&Gamma;"],[0,"&Delta;"],[0,"&Epsilon;"],[0,"&Zeta;"],[0,"&Eta;"],[0,"&Theta;"],[0,"&Iota;"],[0,"&Kappa;"],[0,"&Lambda;"],[0,"&Mu;"],[0,"&Nu;"],[0,"&Xi;"],[0,"&Omicron;"],[0,"&Pi;"],[0,"&Rho;"],[1,"&Sigma;"],[0,"&Tau;"],[0,"&Upsilon;"],[0,"&Phi;"],[0,"&Chi;"],[0,"&Psi;"],[0,"&ohm;"],[7,"&alpha;"],[0,"&beta;"],[0,"&gamma;"],[0,"&delta;"],[0,"&epsi;"],[0,"&zeta;"],[0,"&eta;"],[0,"&theta;"],[0,"&iota;"],[0,"&kappa;"],[0,"&lambda;"],[0,"&mu;"],[0,"&nu;"],[0,"&xi;"],[0,"&omicron;"],[0,"&pi;"],[0,"&rho;"],[0,"&sigmaf;"],[0,"&sigma;"],[0,"&tau;"],[0,"&upsi;"],[0,"&phi;"],[0,"&chi;"],[0,"&psi;"],[0,"&omega;"],[7,"&thetasym;"],[0,"&Upsi;"],[2,"&phiv;"],[0,"&piv;"],[5,"&Gammad;"],[0,"&digamma;"],[18,"&kappav;"],[0,"&rhov;"],[3,"&epsiv;"],[0,"&backepsilon;"],[10,"&IOcy;"],[0,"&DJcy;"],[0,"&GJcy;"],[0,"&Jukcy;"],[0,"&DScy;"],[0,"&Iukcy;"],[0,"&YIcy;"],[0,"&Jsercy;"],[0,"&LJcy;"],[0,"&NJcy;"],[0,"&TSHcy;"],[0,"&KJcy;"],[1,"&Ubrcy;"],[0,"&DZcy;"],[0,"&Acy;"],[0,"&Bcy;"],[0,"&Vcy;"],[0,"&Gcy;"],[0,"&Dcy;"],[0,"&IEcy;"],[0,"&ZHcy;"],[0,"&Zcy;"],[0,"&Icy;"],[0,"&Jcy;"],[0,"&Kcy;"],[0,"&Lcy;"],[0,"&Mcy;"],[0,"&Ncy;"],[0,"&Ocy;"],[0,"&Pcy;"],[0,"&Rcy;"],[0,"&Scy;"],[0,"&Tcy;"],[0,"&Ucy;"],[0,"&Fcy;"],[0,"&KHcy;"],[0,"&TScy;"],[0,"&CHcy;"],[0,"&SHcy;"],[0,"&SHCHcy;"],[0,"&HARDcy;"],[0,"&Ycy;"],[0,"&SOFTcy;"],[0,"&Ecy;"],[0,"&YUcy;"],[0,"&YAcy;"],[0,"&acy;"],[0,"&bcy;"],[0,"&vcy;"],[0,"&gcy;"],[0,"&dcy;"],[0,"&iecy;"],[0,"&zhcy;"],[0,"&zcy;"],[0,"&icy;"],[0,"&jcy;"],[0,"&kcy;"],[0,"&lcy;"],[0,"&mcy;"],[0,"&ncy;"],[0,"&ocy;"],[0,"&pcy;"],[0,"&rcy;"],[0,"&scy;"],[0,"&tcy;"],[0,"&ucy;"],[0,"&fcy;"],[0,"&khcy;"],[0,"&tscy;"],[0,"&chcy;"],[0,"&shcy;"],[0,"&shchcy;"],[0,"&hardcy;"],[0,"&ycy;"],[0,"&softcy;"],[0,"&ecy;"],[0,"&yucy;"],[0,"&yacy;"],[1,"&iocy;"],[0,"&djcy;"],[0,"&gjcy;"],[0,"&jukcy;"],[0,"&dscy;"],[0,"&iukcy;"],[0,"&yicy;"],[0,"&jsercy;"],[0,"&ljcy;"],[0,"&njcy;"],[0,"&tshcy;"],[0,"&kjcy;"],[1,"&ubrcy;"],[0,"&dzcy;"],[7074,"&ensp;"],[0,"&emsp;"],[0,"&emsp13;"],[0,"&emsp14;"],[1,"&numsp;"],[0,"&puncsp;"],[0,"&ThinSpace;"],[0,"&hairsp;"],[0,"&NegativeMediumSpace;"],[0,"&zwnj;"],[0,"&zwj;"],[0,"&lrm;"],[0,"&rlm;"],[0,"&dash;"],[2,"&ndash;"],[0,"&mdash;"],[0,"&horbar;"],[0,"&Verbar;"],[1,"&lsquo;"],[0,"&CloseCurlyQuote;"],[0,"&lsquor;"],[1,"&ldquo;"],[0,"&CloseCurlyDoubleQuote;"],[0,"&bdquo;"],[1,"&dagger;"],[0,"&Dagger;"],[0,"&bull;"],[2,"&nldr;"],[0,"&hellip;"],[9,"&permil;"],[0,"&pertenk;"],[0,"&prime;"],[0,"&Prime;"],[0,"&tprime;"],[0,"&backprime;"],[3,"&lsaquo;"],[0,"&rsaquo;"],[3,"&oline;"],[2,"&caret;"],[1,"&hybull;"],[0,"&frasl;"],[10,"&bsemi;"],[7,"&qprime;"],[7,{v:"&MediumSpace;",n:8202,o:"&ThickSpace;"}],[0,"&NoBreak;"],[0,"&af;"],[0,"&InvisibleTimes;"],[0,"&ic;"],[72,"&euro;"],[46,"&tdot;"],[0,"&DotDot;"],[37,"&complexes;"],[2,"&incare;"],[4,"&gscr;"],[0,"&hamilt;"],[0,"&Hfr;"],[0,"&Hopf;"],[0,"&planckh;"],[0,"&hbar;"],[0,"&imagline;"],[0,"&Ifr;"],[0,"&lagran;"],[0,"&ell;"],[1,"&naturals;"],[0,"&numero;"],[0,"&copysr;"],[0,"&weierp;"],[0,"&Popf;"],[0,"&Qopf;"],[0,"&realine;"],[0,"&real;"],[0,"&reals;"],[0,"&rx;"],[3,"&trade;"],[1,"&integers;"],[2,"&mho;"],[0,"&zeetrf;"],[0,"&iiota;"],[2,"&bernou;"],[0,"&Cayleys;"],[1,"&escr;"],[0,"&Escr;"],[0,"&Fouriertrf;"],[1,"&Mellintrf;"],[0,"&order;"],[0,"&alefsym;"],[0,"&beth;"],[0,"&gimel;"],[0,"&daleth;"],[12,"&CapitalDifferentialD;"],[0,"&dd;"],[0,"&ee;"],[0,"&ii;"],[10,"&frac13;"],[0,"&frac23;"],[0,"&frac15;"],[0,"&frac25;"],[0,"&frac35;"],[0,"&frac45;"],[0,"&frac16;"],[0,"&frac56;"],[0,"&frac18;"],[0,"&frac38;"],[0,"&frac58;"],[0,"&frac78;"],[49,"&larr;"],[0,"&ShortUpArrow;"],[0,"&rarr;"],[0,"&darr;"],[0,"&harr;"],[0,"&updownarrow;"],[0,"&nwarr;"],[0,"&nearr;"],[0,"&LowerRightArrow;"],[0,"&LowerLeftArrow;"],[0,"&nlarr;"],[0,"&nrarr;"],[1,{v:"&rarrw;",n:824,o:"&nrarrw;"}],[0,"&Larr;"],[0,"&Uarr;"],[0,"&Rarr;"],[0,"&Darr;"],[0,"&larrtl;"],[0,"&rarrtl;"],[0,"&LeftTeeArrow;"],[0,"&mapstoup;"],[0,"&map;"],[0,"&DownTeeArrow;"],[1,"&hookleftarrow;"],[0,"&hookrightarrow;"],[0,"&larrlp;"],[0,"&looparrowright;"],[0,"&harrw;"],[0,"&nharr;"],[1,"&lsh;"],[0,"&rsh;"],[0,"&ldsh;"],[0,"&rdsh;"],[1,"&crarr;"],[0,"&cularr;"],[0,"&curarr;"],[2,"&circlearrowleft;"],[0,"&circlearrowright;"],[0,"&leftharpoonup;"],[0,"&DownLeftVector;"],[0,"&RightUpVector;"],[0,"&LeftUpVector;"],[0,"&rharu;"],[0,"&DownRightVector;"],[0,"&dharr;"],[0,"&dharl;"],[0,"&RightArrowLeftArrow;"],[0,"&udarr;"],[0,"&LeftArrowRightArrow;"],[0,"&leftleftarrows;"],[0,"&upuparrows;"],[0,"&rightrightarrows;"],[0,"&ddarr;"],[0,"&leftrightharpoons;"],[0,"&Equilibrium;"],[0,"&nlArr;"],[0,"&nhArr;"],[0,"&nrArr;"],[0,"&DoubleLeftArrow;"],[0,"&DoubleUpArrow;"],[0,"&DoubleRightArrow;"],[0,"&dArr;"],[0,"&DoubleLeftRightArrow;"],[0,"&DoubleUpDownArrow;"],[0,"&nwArr;"],[0,"&neArr;"],[0,"&seArr;"],[0,"&swArr;"],[0,"&lAarr;"],[0,"&rAarr;"],[1,"&zigrarr;"],[6,"&larrb;"],[0,"&rarrb;"],[15,"&DownArrowUpArrow;"],[7,"&loarr;"],[0,"&roarr;"],[0,"&hoarr;"],[0,"&forall;"],[0,"&comp;"],[0,{v:"&part;",n:824,o:"&npart;"}],[0,"&exist;"],[0,"&nexist;"],[0,"&empty;"],[1,"&Del;"],[0,"&Element;"],[0,"&NotElement;"],[1,"&ni;"],[0,"&notni;"],[2,"&prod;"],[0,"&coprod;"],[0,"&sum;"],[0,"&minus;"],[0,"&MinusPlus;"],[0,"&dotplus;"],[1,"&Backslash;"],[0,"&lowast;"],[0,"&compfn;"],[1,"&radic;"],[2,"&prop;"],[0,"&infin;"],[0,"&angrt;"],[0,{v:"&ang;",n:8402,o:"&nang;"}],[0,"&angmsd;"],[0,"&angsph;"],[0,"&mid;"],[0,"&nmid;"],[0,"&DoubleVerticalBar;"],[0,"&NotDoubleVerticalBar;"],[0,"&and;"],[0,"&or;"],[0,{v:"&cap;",n:65024,o:"&caps;"}],[0,{v:"&cup;",n:65024,o:"&cups;"}],[0,"&int;"],[0,"&Int;"],[0,"&iiint;"],[0,"&conint;"],[0,"&Conint;"],[0,"&Cconint;"],[0,"&cwint;"],[0,"&ClockwiseContourIntegral;"],[0,"&awconint;"],[0,"&there4;"],[0,"&becaus;"],[0,"&ratio;"],[0,"&Colon;"],[0,"&dotminus;"],[1,"&mDDot;"],[0,"&homtht;"],[0,{v:"&sim;",n:8402,o:"&nvsim;"}],[0,{v:"&backsim;",n:817,o:"&race;"}],[0,{v:"&ac;",n:819,o:"&acE;"}],[0,"&acd;"],[0,"&VerticalTilde;"],[0,"&NotTilde;"],[0,{v:"&eqsim;",n:824,o:"&nesim;"}],[0,"&sime;"],[0,"&NotTildeEqual;"],[0,"&cong;"],[0,"&simne;"],[0,"&ncong;"],[0,"&ap;"],[0,"&nap;"],[0,"&ape;"],[0,{v:"&apid;",n:824,o:"&napid;"}],[0,"&backcong;"],[0,{v:"&asympeq;",n:8402,o:"&nvap;"}],[0,{v:"&bump;",n:824,o:"&nbump;"}],[0,{v:"&bumpe;",n:824,o:"&nbumpe;"}],[0,{v:"&doteq;",n:824,o:"&nedot;"}],[0,"&doteqdot;"],[0,"&efDot;"],[0,"&erDot;"],[0,"&Assign;"],[0,"&ecolon;"],[0,"&ecir;"],[0,"&circeq;"],[1,"&wedgeq;"],[0,"&veeeq;"],[1,"&triangleq;"],[2,"&equest;"],[0,"&ne;"],[0,{v:"&Congruent;",n:8421,o:"&bnequiv;"}],[0,"&nequiv;"],[1,{v:"&le;",n:8402,o:"&nvle;"}],[0,{v:"&ge;",n:8402,o:"&nvge;"}],[0,{v:"&lE;",n:824,o:"&nlE;"}],[0,{v:"&gE;",n:824,o:"&ngE;"}],[0,{v:"&lnE;",n:65024,o:"&lvertneqq;"}],[0,{v:"&gnE;",n:65024,o:"&gvertneqq;"}],[0,{v:"&ll;",n:new Map(n([[824,"&nLtv;"],[7577,"&nLt;"]]))}],[0,{v:"&gg;",n:new Map(n([[824,"&nGtv;"],[7577,"&nGt;"]]))}],[0,"&between;"],[0,"&NotCupCap;"],[0,"&nless;"],[0,"&ngt;"],[0,"&nle;"],[0,"&nge;"],[0,"&lesssim;"],[0,"&GreaterTilde;"],[0,"&nlsim;"],[0,"&ngsim;"],[0,"&LessGreater;"],[0,"&gl;"],[0,"&NotLessGreater;"],[0,"&NotGreaterLess;"],[0,"&pr;"],[0,"&sc;"],[0,"&prcue;"],[0,"&sccue;"],[0,"&PrecedesTilde;"],[0,{v:"&scsim;",n:824,o:"&NotSucceedsTilde;"}],[0,"&NotPrecedes;"],[0,"&NotSucceeds;"],[0,{v:"&sub;",n:8402,o:"&NotSubset;"}],[0,{v:"&sup;",n:8402,o:"&NotSuperset;"}],[0,"&nsub;"],[0,"&nsup;"],[0,"&sube;"],[0,"&supe;"],[0,"&NotSubsetEqual;"],[0,"&NotSupersetEqual;"],[0,{v:"&subne;",n:65024,o:"&varsubsetneq;"}],[0,{v:"&supne;",n:65024,o:"&varsupsetneq;"}],[1,"&cupdot;"],[0,"&UnionPlus;"],[0,{v:"&sqsub;",n:824,o:"&NotSquareSubset;"}],[0,{v:"&sqsup;",n:824,o:"&NotSquareSuperset;"}],[0,"&sqsube;"],[0,"&sqsupe;"],[0,{v:"&sqcap;",n:65024,o:"&sqcaps;"}],[0,{v:"&sqcup;",n:65024,o:"&sqcups;"}],[0,"&CirclePlus;"],[0,"&CircleMinus;"],[0,"&CircleTimes;"],[0,"&osol;"],[0,"&CircleDot;"],[0,"&circledcirc;"],[0,"&circledast;"],[1,"&circleddash;"],[0,"&boxplus;"],[0,"&boxminus;"],[0,"&boxtimes;"],[0,"&dotsquare;"],[0,"&RightTee;"],[0,"&dashv;"],[0,"&DownTee;"],[0,"&bot;"],[1,"&models;"],[0,"&DoubleRightTee;"],[0,"&Vdash;"],[0,"&Vvdash;"],[0,"&VDash;"],[0,"&nvdash;"],[0,"&nvDash;"],[0,"&nVdash;"],[0,"&nVDash;"],[0,"&prurel;"],[1,"&LeftTriangle;"],[0,"&RightTriangle;"],[0,{v:"&LeftTriangleEqual;",n:8402,o:"&nvltrie;"}],[0,{v:"&RightTriangleEqual;",n:8402,o:"&nvrtrie;"}],[0,"&origof;"],[0,"&imof;"],[0,"&multimap;"],[0,"&hercon;"],[0,"&intcal;"],[0,"&veebar;"],[1,"&barvee;"],[0,"&angrtvb;"],[0,"&lrtri;"],[0,"&bigwedge;"],[0,"&bigvee;"],[0,"&bigcap;"],[0,"&bigcup;"],[0,"&diam;"],[0,"&sdot;"],[0,"&sstarf;"],[0,"&divideontimes;"],[0,"&bowtie;"],[0,"&ltimes;"],[0,"&rtimes;"],[0,"&leftthreetimes;"],[0,"&rightthreetimes;"],[0,"&backsimeq;"],[0,"&curlyvee;"],[0,"&curlywedge;"],[0,"&Sub;"],[0,"&Sup;"],[0,"&Cap;"],[0,"&Cup;"],[0,"&fork;"],[0,"&epar;"],[0,"&lessdot;"],[0,"&gtdot;"],[0,{v:"&Ll;",n:824,o:"&nLl;"}],[0,{v:"&Gg;",n:824,o:"&nGg;"}],[0,{v:"&leg;",n:65024,o:"&lesg;"}],[0,{v:"&gel;",n:65024,o:"&gesl;"}],[2,"&cuepr;"],[0,"&cuesc;"],[0,"&NotPrecedesSlantEqual;"],[0,"&NotSucceedsSlantEqual;"],[0,"&NotSquareSubsetEqual;"],[0,"&NotSquareSupersetEqual;"],[2,"&lnsim;"],[0,"&gnsim;"],[0,"&precnsim;"],[0,"&scnsim;"],[0,"&nltri;"],[0,"&NotRightTriangle;"],[0,"&nltrie;"],[0,"&NotRightTriangleEqual;"],[0,"&vellip;"],[0,"&ctdot;"],[0,"&utdot;"],[0,"&dtdot;"],[0,"&disin;"],[0,"&isinsv;"],[0,"&isins;"],[0,{v:"&isindot;",n:824,o:"&notindot;"}],[0,"&notinvc;"],[0,"&notinvb;"],[1,{v:"&isinE;",n:824,o:"&notinE;"}],[0,"&nisd;"],[0,"&xnis;"],[0,"&nis;"],[0,"&notnivc;"],[0,"&notnivb;"],[6,"&barwed;"],[0,"&Barwed;"],[1,"&lceil;"],[0,"&rceil;"],[0,"&LeftFloor;"],[0,"&rfloor;"],[0,"&drcrop;"],[0,"&dlcrop;"],[0,"&urcrop;"],[0,"&ulcrop;"],[0,"&bnot;"],[1,"&profline;"],[0,"&profsurf;"],[1,"&telrec;"],[0,"&target;"],[5,"&ulcorn;"],[0,"&urcorn;"],[0,"&dlcorn;"],[0,"&drcorn;"],[2,"&frown;"],[0,"&smile;"],[9,"&cylcty;"],[0,"&profalar;"],[7,"&topbot;"],[6,"&ovbar;"],[1,"&solbar;"],[60,"&angzarr;"],[51,"&lmoustache;"],[0,"&rmoustache;"],[2,"&OverBracket;"],[0,"&bbrk;"],[0,"&bbrktbrk;"],[37,"&OverParenthesis;"],[0,"&UnderParenthesis;"],[0,"&OverBrace;"],[0,"&UnderBrace;"],[2,"&trpezium;"],[4,"&elinters;"],[59,"&blank;"],[164,"&circledS;"],[55,"&boxh;"],[1,"&boxv;"],[9,"&boxdr;"],[3,"&boxdl;"],[3,"&boxur;"],[3,"&boxul;"],[3,"&boxvr;"],[7,"&boxvl;"],[7,"&boxhd;"],[7,"&boxhu;"],[7,"&boxvh;"],[19,"&boxH;"],[0,"&boxV;"],[0,"&boxdR;"],[0,"&boxDr;"],[0,"&boxDR;"],[0,"&boxdL;"],[0,"&boxDl;"],[0,"&boxDL;"],[0,"&boxuR;"],[0,"&boxUr;"],[0,"&boxUR;"],[0,"&boxuL;"],[0,"&boxUl;"],[0,"&boxUL;"],[0,"&boxvR;"],[0,"&boxVr;"],[0,"&boxVR;"],[0,"&boxvL;"],[0,"&boxVl;"],[0,"&boxVL;"],[0,"&boxHd;"],[0,"&boxhD;"],[0,"&boxHD;"],[0,"&boxHu;"],[0,"&boxhU;"],[0,"&boxHU;"],[0,"&boxvH;"],[0,"&boxVh;"],[0,"&boxVH;"],[19,"&uhblk;"],[3,"&lhblk;"],[3,"&block;"],[8,"&blk14;"],[0,"&blk12;"],[0,"&blk34;"],[13,"&square;"],[8,"&blacksquare;"],[0,"&EmptyVerySmallSquare;"],[1,"&rect;"],[0,"&marker;"],[2,"&fltns;"],[1,"&bigtriangleup;"],[0,"&blacktriangle;"],[0,"&triangle;"],[2,"&blacktriangleright;"],[0,"&rtri;"],[3,"&bigtriangledown;"],[0,"&blacktriangledown;"],[0,"&dtri;"],[2,"&blacktriangleleft;"],[0,"&ltri;"],[6,"&loz;"],[0,"&cir;"],[32,"&tridot;"],[2,"&bigcirc;"],[8,"&ultri;"],[0,"&urtri;"],[0,"&lltri;"],[0,"&EmptySmallSquare;"],[0,"&FilledSmallSquare;"],[8,"&bigstar;"],[0,"&star;"],[7,"&phone;"],[49,"&female;"],[1,"&male;"],[29,"&spades;"],[2,"&clubs;"],[1,"&hearts;"],[0,"&diamondsuit;"],[3,"&sung;"],[2,"&flat;"],[0,"&natural;"],[0,"&sharp;"],[163,"&check;"],[3,"&cross;"],[8,"&malt;"],[21,"&sext;"],[33,"&VerticalSeparator;"],[25,"&lbbrk;"],[0,"&rbbrk;"],[84,"&bsolhsub;"],[0,"&suphsol;"],[28,"&LeftDoubleBracket;"],[0,"&RightDoubleBracket;"],[0,"&lang;"],[0,"&rang;"],[0,"&Lang;"],[0,"&Rang;"],[0,"&loang;"],[0,"&roang;"],[7,"&longleftarrow;"],[0,"&longrightarrow;"],[0,"&longleftrightarrow;"],[0,"&DoubleLongLeftArrow;"],[0,"&DoubleLongRightArrow;"],[0,"&DoubleLongLeftRightArrow;"],[1,"&longmapsto;"],[2,"&dzigrarr;"],[258,"&nvlArr;"],[0,"&nvrArr;"],[0,"&nvHarr;"],[0,"&Map;"],[6,"&lbarr;"],[0,"&bkarow;"],[0,"&lBarr;"],[0,"&dbkarow;"],[0,"&drbkarow;"],[0,"&DDotrahd;"],[0,"&UpArrowBar;"],[0,"&DownArrowBar;"],[2,"&Rarrtl;"],[2,"&latail;"],[0,"&ratail;"],[0,"&lAtail;"],[0,"&rAtail;"],[0,"&larrfs;"],[0,"&rarrfs;"],[0,"&larrbfs;"],[0,"&rarrbfs;"],[2,"&nwarhk;"],[0,"&nearhk;"],[0,"&hksearow;"],[0,"&hkswarow;"],[0,"&nwnear;"],[0,"&nesear;"],[0,"&seswar;"],[0,"&swnwar;"],[8,{v:"&rarrc;",n:824,o:"&nrarrc;"}],[1,"&cudarrr;"],[0,"&ldca;"],[0,"&rdca;"],[0,"&cudarrl;"],[0,"&larrpl;"],[2,"&curarrm;"],[0,"&cularrp;"],[7,"&rarrpl;"],[2,"&harrcir;"],[0,"&Uarrocir;"],[0,"&lurdshar;"],[0,"&ldrushar;"],[2,"&LeftRightVector;"],[0,"&RightUpDownVector;"],[0,"&DownLeftRightVector;"],[0,"&LeftUpDownVector;"],[0,"&LeftVectorBar;"],[0,"&RightVectorBar;"],[0,"&RightUpVectorBar;"],[0,"&RightDownVectorBar;"],[0,"&DownLeftVectorBar;"],[0,"&DownRightVectorBar;"],[0,"&LeftUpVectorBar;"],[0,"&LeftDownVectorBar;"],[0,"&LeftTeeVector;"],[0,"&RightTeeVector;"],[0,"&RightUpTeeVector;"],[0,"&RightDownTeeVector;"],[0,"&DownLeftTeeVector;"],[0,"&DownRightTeeVector;"],[0,"&LeftUpTeeVector;"],[0,"&LeftDownTeeVector;"],[0,"&lHar;"],[0,"&uHar;"],[0,"&rHar;"],[0,"&dHar;"],[0,"&luruhar;"],[0,"&ldrdhar;"],[0,"&ruluhar;"],[0,"&rdldhar;"],[0,"&lharul;"],[0,"&llhard;"],[0,"&rharul;"],[0,"&lrhard;"],[0,"&udhar;"],[0,"&duhar;"],[0,"&RoundImplies;"],[0,"&erarr;"],[0,"&simrarr;"],[0,"&larrsim;"],[0,"&rarrsim;"],[0,"&rarrap;"],[0,"&ltlarr;"],[1,"&gtrarr;"],[0,"&subrarr;"],[1,"&suplarr;"],[0,"&lfisht;"],[0,"&rfisht;"],[0,"&ufisht;"],[0,"&dfisht;"],[5,"&lopar;"],[0,"&ropar;"],[4,"&lbrke;"],[0,"&rbrke;"],[0,"&lbrkslu;"],[0,"&rbrksld;"],[0,"&lbrksld;"],[0,"&rbrkslu;"],[0,"&langd;"],[0,"&rangd;"],[0,"&lparlt;"],[0,"&rpargt;"],[0,"&gtlPar;"],[0,"&ltrPar;"],[3,"&vzigzag;"],[1,"&vangrt;"],[0,"&angrtvbd;"],[6,"&ange;"],[0,"&range;"],[0,"&dwangle;"],[0,"&uwangle;"],[0,"&angmsdaa;"],[0,"&angmsdab;"],[0,"&angmsdac;"],[0,"&angmsdad;"],[0,"&angmsdae;"],[0,"&angmsdaf;"],[0,"&angmsdag;"],[0,"&angmsdah;"],[0,"&bemptyv;"],[0,"&demptyv;"],[0,"&cemptyv;"],[0,"&raemptyv;"],[0,"&laemptyv;"],[0,"&ohbar;"],[0,"&omid;"],[0,"&opar;"],[1,"&operp;"],[1,"&olcross;"],[0,"&odsold;"],[1,"&olcir;"],[0,"&ofcir;"],[0,"&olt;"],[0,"&ogt;"],[0,"&cirscir;"],[0,"&cirE;"],[0,"&solb;"],[0,"&bsolb;"],[3,"&boxbox;"],[3,"&trisb;"],[0,"&rtriltri;"],[0,{v:"&LeftTriangleBar;",n:824,o:"&NotLeftTriangleBar;"}],[0,{v:"&RightTriangleBar;",n:824,o:"&NotRightTriangleBar;"}],[11,"&iinfin;"],[0,"&infintie;"],[0,"&nvinfin;"],[4,"&eparsl;"],[0,"&smeparsl;"],[0,"&eqvparsl;"],[5,"&blacklozenge;"],[8,"&RuleDelayed;"],[1,"&dsol;"],[9,"&bigodot;"],[0,"&bigoplus;"],[0,"&bigotimes;"],[1,"&biguplus;"],[1,"&bigsqcup;"],[5,"&iiiint;"],[0,"&fpartint;"],[2,"&cirfnint;"],[0,"&awint;"],[0,"&rppolint;"],[0,"&scpolint;"],[0,"&npolint;"],[0,"&pointint;"],[0,"&quatint;"],[0,"&intlarhk;"],[10,"&pluscir;"],[0,"&plusacir;"],[0,"&simplus;"],[0,"&plusdu;"],[0,"&plussim;"],[0,"&plustwo;"],[1,"&mcomma;"],[0,"&minusdu;"],[2,"&loplus;"],[0,"&roplus;"],[0,"&Cross;"],[0,"&timesd;"],[0,"&timesbar;"],[1,"&smashp;"],[0,"&lotimes;"],[0,"&rotimes;"],[0,"&otimesas;"],[0,"&Otimes;"],[0,"&odiv;"],[0,"&triplus;"],[0,"&triminus;"],[0,"&tritime;"],[0,"&intprod;"],[2,"&amalg;"],[0,"&capdot;"],[1,"&ncup;"],[0,"&ncap;"],[0,"&capand;"],[0,"&cupor;"],[0,"&cupcap;"],[0,"&capcup;"],[0,"&cupbrcap;"],[0,"&capbrcup;"],[0,"&cupcup;"],[0,"&capcap;"],[0,"&ccups;"],[0,"&ccaps;"],[2,"&ccupssm;"],[2,"&And;"],[0,"&Or;"],[0,"&andand;"],[0,"&oror;"],[0,"&orslope;"],[0,"&andslope;"],[1,"&andv;"],[0,"&orv;"],[0,"&andd;"],[0,"&ord;"],[1,"&wedbar;"],[6,"&sdote;"],[3,"&simdot;"],[2,{v:"&congdot;",n:824,o:"&ncongdot;"}],[0,"&easter;"],[0,"&apacir;"],[0,{v:"&apE;",n:824,o:"&napE;"}],[0,"&eplus;"],[0,"&pluse;"],[0,"&Esim;"],[0,"&Colone;"],[0,"&Equal;"],[1,"&ddotseq;"],[0,"&equivDD;"],[0,"&ltcir;"],[0,"&gtcir;"],[0,"&ltquest;"],[0,"&gtquest;"],[0,{v:"&leqslant;",n:824,o:"&nleqslant;"}],[0,{v:"&geqslant;",n:824,o:"&ngeqslant;"}],[0,"&lesdot;"],[0,"&gesdot;"],[0,"&lesdoto;"],[0,"&gesdoto;"],[0,"&lesdotor;"],[0,"&gesdotol;"],[0,"&lap;"],[0,"&gap;"],[0,"&lne;"],[0,"&gne;"],[0,"&lnap;"],[0,"&gnap;"],[0,"&lEg;"],[0,"&gEl;"],[0,"&lsime;"],[0,"&gsime;"],[0,"&lsimg;"],[0,"&gsiml;"],[0,"&lgE;"],[0,"&glE;"],[0,"&lesges;"],[0,"&gesles;"],[0,"&els;"],[0,"&egs;"],[0,"&elsdot;"],[0,"&egsdot;"],[0,"&el;"],[0,"&eg;"],[2,"&siml;"],[0,"&simg;"],[0,"&simlE;"],[0,"&simgE;"],[0,{v:"&LessLess;",n:824,o:"&NotNestedLessLess;"}],[0,{v:"&GreaterGreater;",n:824,o:"&NotNestedGreaterGreater;"}],[1,"&glj;"],[0,"&gla;"],[0,"&ltcc;"],[0,"&gtcc;"],[0,"&lescc;"],[0,"&gescc;"],[0,"&smt;"],[0,"&lat;"],[0,{v:"&smte;",n:65024,o:"&smtes;"}],[0,{v:"&late;",n:65024,o:"&lates;"}],[0,"&bumpE;"],[0,{v:"&PrecedesEqual;",n:824,o:"&NotPrecedesEqual;"}],[0,{v:"&sce;",n:824,o:"&NotSucceedsEqual;"}],[2,"&prE;"],[0,"&scE;"],[0,"&precneqq;"],[0,"&scnE;"],[0,"&prap;"],[0,"&scap;"],[0,"&precnapprox;"],[0,"&scnap;"],[0,"&Pr;"],[0,"&Sc;"],[0,"&subdot;"],[0,"&supdot;"],[0,"&subplus;"],[0,"&supplus;"],[0,"&submult;"],[0,"&supmult;"],[0,"&subedot;"],[0,"&supedot;"],[0,{v:"&subE;",n:824,o:"&nsubE;"}],[0,{v:"&supE;",n:824,o:"&nsupE;"}],[0,"&subsim;"],[0,"&supsim;"],[2,{v:"&subnE;",n:65024,o:"&varsubsetneqq;"}],[0,{v:"&supnE;",n:65024,o:"&varsupsetneqq;"}],[2,"&csub;"],[0,"&csup;"],[0,"&csube;"],[0,"&csupe;"],[0,"&subsup;"],[0,"&supsub;"],[0,"&subsub;"],[0,"&supsup;"],[0,"&suphsub;"],[0,"&supdsub;"],[0,"&forkv;"],[0,"&topfork;"],[0,"&mlcp;"],[8,"&Dashv;"],[1,"&Vdashl;"],[0,"&Barv;"],[0,"&vBar;"],[0,"&vBarv;"],[1,"&Vbar;"],[0,"&Not;"],[0,"&bNot;"],[0,"&rnmid;"],[0,"&cirmid;"],[0,"&midcir;"],[0,"&topcir;"],[0,"&nhpar;"],[0,"&parsim;"],[9,{v:"&parsl;",n:8421,o:"&nparsl;"}],[44343,{n:new Map(n([[56476,"&Ascr;"],[1,"&Cscr;"],[0,"&Dscr;"],[2,"&Gscr;"],[2,"&Jscr;"],[0,"&Kscr;"],[2,"&Nscr;"],[0,"&Oscr;"],[0,"&Pscr;"],[0,"&Qscr;"],[1,"&Sscr;"],[0,"&Tscr;"],[0,"&Uscr;"],[0,"&Vscr;"],[0,"&Wscr;"],[0,"&Xscr;"],[0,"&Yscr;"],[0,"&Zscr;"],[0,"&ascr;"],[0,"&bscr;"],[0,"&cscr;"],[0,"&dscr;"],[1,"&fscr;"],[1,"&hscr;"],[0,"&iscr;"],[0,"&jscr;"],[0,"&kscr;"],[0,"&lscr;"],[0,"&mscr;"],[0,"&nscr;"],[1,"&pscr;"],[0,"&qscr;"],[0,"&rscr;"],[0,"&sscr;"],[0,"&tscr;"],[0,"&uscr;"],[0,"&vscr;"],[0,"&wscr;"],[0,"&xscr;"],[0,"&yscr;"],[0,"&zscr;"],[52,"&Afr;"],[0,"&Bfr;"],[1,"&Dfr;"],[0,"&Efr;"],[0,"&Ffr;"],[0,"&Gfr;"],[2,"&Jfr;"],[0,"&Kfr;"],[0,"&Lfr;"],[0,"&Mfr;"],[0,"&Nfr;"],[0,"&Ofr;"],[0,"&Pfr;"],[0,"&Qfr;"],[1,"&Sfr;"],[0,"&Tfr;"],[0,"&Ufr;"],[0,"&Vfr;"],[0,"&Wfr;"],[0,"&Xfr;"],[0,"&Yfr;"],[1,"&afr;"],[0,"&bfr;"],[0,"&cfr;"],[0,"&dfr;"],[0,"&efr;"],[0,"&ffr;"],[0,"&gfr;"],[0,"&hfr;"],[0,"&ifr;"],[0,"&jfr;"],[0,"&kfr;"],[0,"&lfr;"],[0,"&mfr;"],[0,"&nfr;"],[0,"&ofr;"],[0,"&pfr;"],[0,"&qfr;"],[0,"&rfr;"],[0,"&sfr;"],[0,"&tfr;"],[0,"&ufr;"],[0,"&vfr;"],[0,"&wfr;"],[0,"&xfr;"],[0,"&yfr;"],[0,"&zfr;"],[0,"&Aopf;"],[0,"&Bopf;"],[1,"&Dopf;"],[0,"&Eopf;"],[0,"&Fopf;"],[0,"&Gopf;"],[1,"&Iopf;"],[0,"&Jopf;"],[0,"&Kopf;"],[0,"&Lopf;"],[0,"&Mopf;"],[1,"&Oopf;"],[3,"&Sopf;"],[0,"&Topf;"],[0,"&Uopf;"],[0,"&Vopf;"],[0,"&Wopf;"],[0,"&Xopf;"],[0,"&Yopf;"],[1,"&aopf;"],[0,"&bopf;"],[0,"&copf;"],[0,"&dopf;"],[0,"&eopf;"],[0,"&fopf;"],[0,"&gopf;"],[0,"&hopf;"],[0,"&iopf;"],[0,"&jopf;"],[0,"&kopf;"],[0,"&lopf;"],[0,"&mopf;"],[0,"&nopf;"],[0,"&oopf;"],[0,"&popf;"],[0,"&qopf;"],[0,"&ropf;"],[0,"&sopf;"],[0,"&topf;"],[0,"&uopf;"],[0,"&vopf;"],[0,"&wopf;"],[0,"&xopf;"],[0,"&yopf;"],[0,"&zopf;"]]))}],[8906,"&fflig;"],[0,"&filig;"],[0,"&fllig;"],[0,"&ffilig;"],[0,"&ffllig;"]]))},2349:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return i(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.decodeXML=t.decodeHTMLStrict=t.decodeHTMLAttribute=t.decodeHTML=t.determineBranch=t.EntityDecoder=t.DecodingMode=t.BinTrieFlags=t.fromCodePoint=t.replaceCodePoint=t.decodeCodePoint=t.xmlDecodeTree=t.htmlDecodeTree=void 0;var a=s(n(2556));t.htmlDecodeTree=a.default;var l=s(n(3356));t.xmlDecodeTree=l.default;var c=o(n(855));t.decodeCodePoint=c.default;var u,h=n(855);Object.defineProperty(t,"replaceCodePoint",{enumerable:!0,get:function(){return h.replaceCodePoint}}),Object.defineProperty(t,"fromCodePoint",{enumerable:!0,get:function(){return h.fromCodePoint}}),function(e){e[e.NUM=35]="NUM",e[e.SEMI=59]="SEMI",e[e.EQUALS=61]="EQUALS",e[e.ZERO=48]="ZERO",e[e.NINE=57]="NINE",e[e.LOWER_A=97]="LOWER_A",e[e.LOWER_F=102]="LOWER_F",e[e.LOWER_X=120]="LOWER_X",e[e.LOWER_Z=122]="LOWER_Z",e[e.UPPER_A=65]="UPPER_A",e[e.UPPER_F=70]="UPPER_F",e[e.UPPER_Z=90]="UPPER_Z"}(u||(u={}));var f,d,p;function m(e){return e>=u.ZERO&&e<=u.NINE}function g(e){return e===u.EQUALS||function(e){return e>=u.UPPER_A&&e<=u.UPPER_Z||e>=u.LOWER_A&&e<=u.LOWER_Z||m(e)}(e)}!function(e){e[e.VALUE_LENGTH=49152]="VALUE_LENGTH",e[e.BRANCH_LENGTH=16256]="BRANCH_LENGTH",e[e.JUMP_TABLE=127]="JUMP_TABLE"}(f=t.BinTrieFlags||(t.BinTrieFlags={})),function(e){e[e.EntityStart=0]="EntityStart",e[e.NumericStart=1]="NumericStart",e[e.NumericDecimal=2]="NumericDecimal",e[e.NumericHex=3]="NumericHex",e[e.NamedEntity=4]="NamedEntity"}(d||(d={})),function(e){e[e.Legacy=0]="Legacy",e[e.Strict=1]="Strict",e[e.Attribute=2]="Attribute"}(p=t.DecodingMode||(t.DecodingMode={}));var y=function(){function e(e,t,n){this.decodeTree=e,this.emitCodePoint=t,this.errors=n,this.state=d.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=p.Strict}return e.prototype.startEntity=function(e){this.decodeMode=e,this.state=d.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1},e.prototype.write=function(e,t){switch(this.state){case d.EntityStart:return e.charCodeAt(t)===u.NUM?(this.state=d.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1)):(this.state=d.NamedEntity,this.stateNamedEntity(e,t));case d.NumericStart:return this.stateNumericStart(e,t);case d.NumericDecimal:return this.stateNumericDecimal(e,t);case d.NumericHex:return this.stateNumericHex(e,t);case d.NamedEntity:return this.stateNamedEntity(e,t)}},e.prototype.stateNumericStart=function(e,t){return t>=e.length?-1:(32|e.charCodeAt(t))===u.LOWER_X?(this.state=d.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=d.NumericDecimal,this.stateNumericDecimal(e,t))},e.prototype.addToNumericResult=function(e,t,n,r){if(t!==n){var i=n-t;this.result=this.result*Math.pow(r,i)+parseInt(e.substr(t,i),r),this.consumed+=i}},e.prototype.stateNumericHex=function(e,t){for(var n,r=t;t<e.length;){var i=e.charCodeAt(t);if(!(m(i)||(n=i,n>=u.UPPER_A&&n<=u.UPPER_F||n>=u.LOWER_A&&n<=u.LOWER_F)))return this.addToNumericResult(e,r,t,16),this.emitNumericEntity(i,3);t+=1}return this.addToNumericResult(e,r,t,16),-1},e.prototype.stateNumericDecimal=function(e,t){for(var n=t;t<e.length;){var r=e.charCodeAt(t);if(!m(r))return this.addToNumericResult(e,n,t,10),this.emitNumericEntity(r,2);t+=1}return this.addToNumericResult(e,n,t,10),-1},e.prototype.emitNumericEntity=function(e,t){var n;if(this.consumed<=t)return null===(n=this.errors)||void 0===n||n.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===u.SEMI)this.consumed+=1;else if(this.decodeMode===p.Strict)return 0;return this.emitCodePoint((0,c.replaceCodePoint)(this.result),this.consumed),this.errors&&(e!==u.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed},e.prototype.stateNamedEntity=function(e,t){for(var n=this.decodeTree,r=n[this.treeIndex],i=(r&f.VALUE_LENGTH)>>14;t<e.length;t++,this.excess++){var o=e.charCodeAt(t);if(this.treeIndex=b(n,r,this.treeIndex+Math.max(1,i),o),this.treeIndex<0)return 0===this.result||this.decodeMode===p.Attribute&&(0===i||g(o))?0:this.emitNotTerminatedNamedEntity();if(0!==(i=((r=n[this.treeIndex])&f.VALUE_LENGTH)>>14)){if(o===u.SEMI)return this.emitNamedEntityData(this.treeIndex,i,this.consumed+this.excess);this.decodeMode!==p.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1},e.prototype.emitNotTerminatedNamedEntity=function(){var e,t=this.result,n=(this.decodeTree[t]&f.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,n,this.consumed),null===(e=this.errors)||void 0===e||e.missingSemicolonAfterCharacterReference(),this.consumed},e.prototype.emitNamedEntityData=function(e,t,n){var r=this.decodeTree;return this.emitCodePoint(1===t?r[e]&~f.VALUE_LENGTH:r[e+1],n),3===t&&this.emitCodePoint(r[e+2],n),n},e.prototype.end=function(){var e;switch(this.state){case d.NamedEntity:return 0===this.result||this.decodeMode===p.Attribute&&this.result!==this.treeIndex?0:this.emitNotTerminatedNamedEntity();case d.NumericDecimal:return this.emitNumericEntity(0,2);case d.NumericHex:return this.emitNumericEntity(0,3);case d.NumericStart:return null===(e=this.errors)||void 0===e||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case d.EntityStart:return 0}},e}();function v(e){var t="",n=new y(e,(function(e){return t+=(0,c.fromCodePoint)(e)}));return function(e,r){for(var i=0,o=0;(o=e.indexOf("&",o))>=0;){t+=e.slice(i,o),n.startEntity(r);var s=n.write(e,o+1);if(s<0){i=o+n.end();break}i=o+s,o=0===s?i+1:i}var a=t+e.slice(i);return t="",a}}function b(e,t,n,r){var i=(t&f.BRANCH_LENGTH)>>7,o=t&f.JUMP_TABLE;if(0===i)return 0!==o&&r===o?n:-1;if(o){var s=r-o;return s<0||s>=i?-1:e[n+s]-1}for(var a=n,l=a+i-1;a<=l;){var c=a+l>>>1,u=e[c];if(u<r)a=c+1;else{if(!(u>r))return e[c+i];l=c-1}}return-1}t.EntityDecoder=y,t.determineBranch=b;var w=v(a.default),x=v(l.default);t.decodeHTML=function(e,t){return void 0===t&&(t=p.Legacy),w(e,t)},t.decodeHTMLAttribute=function(e){return w(e,p.Attribute)},t.decodeHTMLStrict=function(e){return w(e,p.Strict)},t.decodeXML=function(e){return x(e,p.Strict)}},2556:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map((function(e){return e.charCodeAt(0)})))},2739:()=>{},2772:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getFeed=function(e){var t=l(h,e);return t?"feed"===t.name?function(e){var t,n=e.children,r={type:"atom",items:(0,i.getElementsByTagName)("entry",n).map((function(e){var t,n=e.children,r={media:a(n)};u(r,"id","id",n),u(r,"title","title",n);var i=null===(t=l("link",n))||void 0===t?void 0:t.attribs.href;i&&(r.link=i);var o=c("summary",n)||c("content",n);o&&(r.description=o);var s=c("updated",n);return s&&(r.pubDate=new Date(s)),r}))};u(r,"id","id",n),u(r,"title","title",n);var o=null===(t=l("link",n))||void 0===t?void 0:t.attribs.href;o&&(r.link=o);u(r,"description","subtitle",n);var s=c("updated",n);s&&(r.updated=new Date(s));return u(r,"author","email",n,!0),r}(t):function(e){var t,n,r=null!==(n=null===(t=l("channel",e.children))||void 0===t?void 0:t.children)&&void 0!==n?n:[],o={type:e.name.substr(0,3),id:"",items:(0,i.getElementsByTagName)("item",e.children).map((function(e){var t=e.children,n={media:a(t)};u(n,"id","guid",t),u(n,"title","title",t),u(n,"link","link",t),u(n,"description","description",t);var r=c("pubDate",t)||c("dc:date",t);return r&&(n.pubDate=new Date(r)),n}))};u(o,"title","title",r),u(o,"link","link",r),u(o,"description","description",r);var s=c("lastBuildDate",r);s&&(o.updated=new Date(s));return u(o,"author","managingEditor",r,!0),o}(t):null};var r=n(9124),i=n(1974);var o=["url","type","lang"],s=["fileSize","bitrate","framerate","samplingrate","channels","duration","height","width"];function a(e){return(0,i.getElementsByTagName)("media:content",e).map((function(e){for(var t=e.attribs,n={medium:t.medium,isDefault:!!t.isDefault},r=0,i=o;r<i.length;r++){t[c=i[r]]&&(n[c]=t[c])}for(var a=0,l=s;a<l.length;a++){var c;t[c=l[a]]&&(n[c]=parseInt(t[c],10))}return t.expression&&(n.expression=t.expression),n}))}function l(e,t){return(0,i.getElementsByTagName)(e,t,!0,1)[0]}function c(e,t,n){return void 0===n&&(n=!1),(0,r.textContent)((0,i.getElementsByTagName)(e,t,n,1)).trim()}function u(e,t,n,r,i){void 0===i&&(i=!1);var o=c(n,r,i);o&&(e[t]=o)}function h(e){return"rss"===e||"feed"===e||"rdf:RDF"===e}},2834:e=>{"use strict";e.exports=e=>{if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}},2851:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getChildren=i,t.getParent=o,t.getSiblings=function(e){var t=o(e);if(null!=t)return i(t);var n=[e],r=e.prev,s=e.next;for(;null!=r;)n.unshift(r),r=r.prev;for(;null!=s;)n.push(s),s=s.next;return n},t.getAttributeValue=function(e,t){var n;return null===(n=e.attribs)||void 0===n?void 0:n[t]},t.hasAttrib=function(e,t){return null!=e.attribs&&Object.prototype.hasOwnProperty.call(e.attribs,t)&&null!=e.attribs[t]},t.getName=function(e){return e.name},t.nextElementSibling=function(e){var t=e.next;for(;null!==t&&!(0,r.isTag)(t);)t=t.next;return t},t.prevElementSibling=function(e){var t=e.prev;for(;null!==t&&!(0,r.isTag)(t);)t=t.prev;return t};var r=n(4128);function i(e){return(0,r.hasChildren)(e)?e.children:[]}function o(e){return e.parent||null}},2895:(e,t,n)=>{"use strict";var r=n(5606);let i=n(396),o=n(9371),s=n(7793),a=n(3614),l=n(5238),c=n(145),u=n(3438),h=n(1106),f=n(6966),d=n(1752),p=n(3152),m=n(9577),g=n(6846),y=n(3717),v=n(5644),b=n(1534),w=n(3303),x=n(38);function _(...e){return 1===e.length&&Array.isArray(e[0])&&(e=e[0]),new g(e)}_.plugin=function(e,t){let n,i=!1;function o(...n){console&&console.warn&&!i&&(i=!0,console.warn(e+": postcss.plugin was deprecated. Migration guide:\nhttps://evilmartians.com/chronicles/postcss-8-plugin-migration"),r.env.LANG&&r.env.LANG.startsWith("cn")&&console.warn(e+": 里面 postcss.plugin 被弃用. 迁移指南:\nhttps://www.w3ctech.com/topic/2226"));let o=t(...n);return o.postcssPlugin=e,o.postcssVersion=(new g).version,o}return Object.defineProperty(o,"postcss",{get:()=>(n||(n=o()),n)}),o.process=function(e,t,n){return _([o(n)]).process(e,t)},o},_.stringify=w,_.parse=m,_.fromJSON=u,_.list=d,_.comment=e=>new o(e),_.atRule=e=>new i(e),_.decl=e=>new l(e),_.rule=e=>new b(e),_.root=e=>new v(e),_.document=e=>new c(e),_.CssSyntaxError=a,_.Declaration=l,_.Container=s,_.Processor=g,_.Document=c,_.Comment=o,_.Warning=x,_.AtRule=i,_.Result=y,_.Input=h,_.Rule=b,_.Root=v,_.Node=p,f.registerPostcss(_),e.exports=_,_.default=_},3152:(e,t,n)=>{"use strict";let r=n(3614),i=n(7668),o=n(3303),{isClean:s,my:a}=n(4151);function l(e,t){let n=new e.constructor;for(let r in e){if(!Object.prototype.hasOwnProperty.call(e,r))continue;if("proxyCache"===r)continue;let i=e[r],o=typeof i;"parent"===r&&"object"===o?t&&(n[r]=t):"source"===r?n[r]=i:Array.isArray(i)?n[r]=i.map((e=>l(e,n))):("object"===o&&null!==i&&(i=l(i)),n[r]=i)}return n}function c(e,t){if(t&&void 0!==t.offset)return t.offset;let n=1,r=1,i=0;for(let o=0;o<e.length;o++){if(r===t.line&&n===t.column){i=o;break}"\n"===e[o]?(n=1,r+=1):n+=1}return i}class u{get proxyOf(){return this}constructor(e={}){this.raws={},this[s]=!1,this[a]=!0;for(let t in e)if("nodes"===t){this.nodes=[];for(let n of e[t])"function"==typeof n.clone?this.append(n.clone()):this.append(n)}else this[t]=e[t]}addToError(e){if(e.postcssNode=this,e.stack&&this.source&&/\n\s{4}at /.test(e.stack)){let t=this.source;e.stack=e.stack.replace(/\n\s{4}at /,`$&${t.input.from}:${t.start.line}:${t.start.column}$&`)}return e}after(e){return this.parent.insertAfter(this,e),this}assign(e={}){for(let t in e)this[t]=e[t];return this}before(e){return this.parent.insertBefore(this,e),this}cleanRaws(e){delete this.raws.before,delete this.raws.after,e||delete this.raws.between}clone(e={}){let t=l(this);for(let n in e)t[n]=e[n];return t}cloneAfter(e={}){let t=this.clone(e);return this.parent.insertAfter(this,t),t}cloneBefore(e={}){let t=this.clone(e);return this.parent.insertBefore(this,t),t}error(e,t={}){if(this.source){let{end:n,start:r}=this.rangeBy(t);return this.source.input.error(e,{column:r.column,line:r.line},{column:n.column,line:n.line},t)}return new r(e)}getProxyProcessor(){return{get:(e,t)=>"proxyOf"===t?e:"root"===t?()=>e.root().toProxy():e[t],set:(e,t,n)=>(e[t]===n||(e[t]=n,"prop"!==t&&"value"!==t&&"name"!==t&&"params"!==t&&"important"!==t&&"text"!==t||e.markDirty()),!0)}}markClean(){this[s]=!0}markDirty(){if(this[s]){this[s]=!1;let e=this;for(;e=e.parent;)e[s]=!1}}next(){if(!this.parent)return;let e=this.parent.index(this);return this.parent.nodes[e+1]}positionBy(e){let t=this.source.start;if(e.index)t=this.positionInside(e.index);else if(e.word){let n="document"in this.source.input?this.source.input.document:this.source.input.css,r=n.slice(c(n,this.source.start),c(n,this.source.end)).indexOf(e.word);-1!==r&&(t=this.positionInside(r))}return t}positionInside(e){let t=this.source.start.column,n=this.source.start.line,r="document"in this.source.input?this.source.input.document:this.source.input.css,i=c(r,this.source.start),o=i+e;for(let e=i;e<o;e++)"\n"===r[e]?(t=1,n+=1):t+=1;return{column:t,line:n}}prev(){if(!this.parent)return;let e=this.parent.index(this);return this.parent.nodes[e-1]}rangeBy(e){let t={column:this.source.start.column,line:this.source.start.line},n=this.source.end?{column:this.source.end.column+1,line:this.source.end.line}:{column:t.column+1,line:t.line};if(e.word){let r="document"in this.source.input?this.source.input.document:this.source.input.css,i=r.slice(c(r,this.source.start),c(r,this.source.end)).indexOf(e.word);-1!==i&&(t=this.positionInside(i),n=this.positionInside(i+e.word.length))}else e.start?t={column:e.start.column,line:e.start.line}:e.index&&(t=this.positionInside(e.index)),e.end?n={column:e.end.column,line:e.end.line}:"number"==typeof e.endIndex?n=this.positionInside(e.endIndex):e.index&&(n=this.positionInside(e.index+1));return(n.line<t.line||n.line===t.line&&n.column<=t.column)&&(n={column:t.column+1,line:t.line}),{end:n,start:t}}raw(e,t){return(new i).raw(this,e,t)}remove(){return this.parent&&this.parent.removeChild(this),this.parent=void 0,this}replaceWith(...e){if(this.parent){let t=this,n=!1;for(let r of e)r===this?n=!0:n?(this.parent.insertAfter(t,r),t=r):this.parent.insertBefore(t,r);n||this.remove()}return this}root(){let e=this;for(;e.parent&&"document"!==e.parent.type;)e=e.parent;return e}toJSON(e,t){let n={},r=null==t;t=t||new Map;let i=0;for(let e in this){if(!Object.prototype.hasOwnProperty.call(this,e))continue;if("parent"===e||"proxyCache"===e)continue;let r=this[e];if(Array.isArray(r))n[e]=r.map((e=>"object"==typeof e&&e.toJSON?e.toJSON(null,t):e));else if("object"==typeof r&&r.toJSON)n[e]=r.toJSON(null,t);else if("source"===e){let o=t.get(r.input);null==o&&(o=i,t.set(r.input,i),i++),n[e]={end:r.end,inputId:o,start:r.start}}else n[e]=r}return r&&(n.inputs=[...t.keys()].map((e=>e.toJSON()))),n}toProxy(){return this.proxyCache||(this.proxyCache=new Proxy(this,this.getProxyProcessor())),this.proxyCache}toString(e=o){e.stringify&&(e=e.stringify);let t="";return e(this,(e=>{t+=e})),t}warn(e,t,n){let r={node:this};for(let e in n)r[e]=n[e];return e.warn(t,r)}}e.exports=u,u.default=u},3303:(e,t,n)=>{"use strict";let r=n(7668);function i(e,t){new r(t).stringify(e)}e.exports=i,i.default=i},3356:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=new Uint16Array("Ȁaglq\tɭ\0\0p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map((function(e){return e.charCodeAt(0)})))},3438:(e,t,n)=>{"use strict";let r=n(396),i=n(9371),o=n(5238),s=n(1106),a=n(3878),l=n(5644),c=n(1534);function u(e,t){if(Array.isArray(e))return e.map((e=>u(e)));let{inputs:n,...h}=e;if(n){t=[];for(let e of n){let n={...e,__proto__:s.prototype};n.map&&(n.map={...n.map,__proto__:a.prototype}),t.push(n)}}if(h.nodes&&(h.nodes=e.nodes.map((e=>u(e,t)))),h.source){let{inputId:e,...n}=h.source;h.source=n,null!=e&&(h.source.input=t[e])}if("root"===h.type)return new l(h);if("decl"===h.type)return new o(h);if("rule"===h.type)return new c(h);if("comment"===h.type)return new i(h);if("atrule"===h.type)return new r(h);throw new Error("Unknown node type: "+e.type)}e.exports=u,u.default=u},3604:(e,t,n)=>{"use strict";var r=n(8287).hp;let{dirname:i,relative:o,resolve:s,sep:a}=n(197),{SourceMapConsumer:l,SourceMapGenerator:c}=n(1866),{pathToFileURL:u}=n(2739),h=n(1106),f=Boolean(l&&c),d=Boolean(i&&s&&o&&a);e.exports=class{constructor(e,t,n,r){this.stringify=e,this.mapOpts=n.map||{},this.root=t,this.opts=n,this.css=r,this.originalCSS=r,this.usesFileUrls=!this.mapOpts.from&&this.mapOpts.absolute,this.memoizedFileURLs=new Map,this.memoizedPaths=new Map,this.memoizedURLs=new Map}addAnnotation(){let e;e=this.isInline()?"data:application/json;base64,"+this.toBase64(this.map.toString()):"string"==typeof this.mapOpts.annotation?this.mapOpts.annotation:"function"==typeof this.mapOpts.annotation?this.mapOpts.annotation(this.opts.to,this.root):this.outputFile()+".map";let t="\n";this.css.includes("\r\n")&&(t="\r\n"),this.css+=t+"/*# sourceMappingURL="+e+" */"}applyPrevMaps(){for(let e of this.previous()){let t,n=this.toUrl(this.path(e.file)),r=e.root||i(e.file);!1===this.mapOpts.sourcesContent?(t=new l(e.text),t.sourcesContent&&(t.sourcesContent=null)):t=e.consumer(),this.map.applySourceMap(t,n,this.toUrl(this.path(r)))}}clearAnnotation(){if(!1!==this.mapOpts.annotation)if(this.root){let e;for(let t=this.root.nodes.length-1;t>=0;t--)e=this.root.nodes[t],"comment"===e.type&&e.text.startsWith("# sourceMappingURL=")&&this.root.removeChild(t)}else this.css&&(this.css=this.css.replace(/\n*\/\*#[\S\s]*?\*\/$/gm,""))}generate(){if(this.clearAnnotation(),d&&f&&this.isMap())return this.generateMap();{let e="";return this.stringify(this.root,(t=>{e+=t})),[e]}}generateMap(){if(this.root)this.generateString();else if(1===this.previous().length){let e=this.previous()[0].consumer();e.file=this.outputFile(),this.map=c.fromSourceMap(e,{ignoreInvalidMapping:!0})}else this.map=new c({file:this.outputFile(),ignoreInvalidMapping:!0}),this.map.addMapping({generated:{column:0,line:1},original:{column:0,line:1},source:this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>"});return this.isSourcesContent()&&this.setSourcesContent(),this.root&&this.previous().length>0&&this.applyPrevMaps(),this.isAnnotation()&&this.addAnnotation(),this.isInline()?[this.css]:[this.css,this.map]}generateString(){this.css="",this.map=new c({file:this.outputFile(),ignoreInvalidMapping:!0});let e,t,n=1,r=1,i="<no source>",o={generated:{column:0,line:0},original:{column:0,line:0},source:""};this.stringify(this.root,((s,a,l)=>{if(this.css+=s,a&&"end"!==l&&(o.generated.line=n,o.generated.column=r-1,a.source&&a.source.start?(o.source=this.sourcePath(a),o.original.line=a.source.start.line,o.original.column=a.source.start.column-1,this.map.addMapping(o)):(o.source=i,o.original.line=1,o.original.column=0,this.map.addMapping(o))),t=s.match(/\n/g),t?(n+=t.length,e=s.lastIndexOf("\n"),r=s.length-e):r+=s.length,a&&"start"!==l){let e=a.parent||{raws:{}};("decl"===a.type||"atrule"===a.type&&!a.nodes)&&a===e.last&&!e.raws.semicolon||(a.source&&a.source.end?(o.source=this.sourcePath(a),o.original.line=a.source.end.line,o.original.column=a.source.end.column-1,o.generated.line=n,o.generated.column=r-2,this.map.addMapping(o)):(o.source=i,o.original.line=1,o.original.column=0,o.generated.line=n,o.generated.column=r-1,this.map.addMapping(o)))}}))}isAnnotation(){return!!this.isInline()||(void 0!==this.mapOpts.annotation?this.mapOpts.annotation:!this.previous().length||this.previous().some((e=>e.annotation)))}isInline(){if(void 0!==this.mapOpts.inline)return this.mapOpts.inline;let e=this.mapOpts.annotation;return(void 0===e||!0===e)&&(!this.previous().length||this.previous().some((e=>e.inline)))}isMap(){return void 0!==this.opts.map?!!this.opts.map:this.previous().length>0}isSourcesContent(){return void 0!==this.mapOpts.sourcesContent?this.mapOpts.sourcesContent:!this.previous().length||this.previous().some((e=>e.withContent()))}outputFile(){return this.opts.to?this.path(this.opts.to):this.opts.from?this.path(this.opts.from):"to.css"}path(e){if(this.mapOpts.absolute)return e;if(60===e.charCodeAt(0))return e;if(/^\w+:\/\//.test(e))return e;let t=this.memoizedPaths.get(e);if(t)return t;let n=this.opts.to?i(this.opts.to):".";"string"==typeof this.mapOpts.annotation&&(n=i(s(n,this.mapOpts.annotation)));let r=o(n,e);return this.memoizedPaths.set(e,r),r}previous(){if(!this.previousMaps)if(this.previousMaps=[],this.root)this.root.walk((e=>{if(e.source&&e.source.input.map){let t=e.source.input.map;this.previousMaps.includes(t)||this.previousMaps.push(t)}}));else{let e=new h(this.originalCSS,this.opts);e.map&&this.previousMaps.push(e.map)}return this.previousMaps}setSourcesContent(){let e={};if(this.root)this.root.walk((t=>{if(t.source){let n=t.source.input.from;if(n&&!e[n]){e[n]=!0;let r=this.usesFileUrls?this.toFileUrl(n):this.toUrl(this.path(n));this.map.setSourceContent(r,t.source.input.css)}}}));else if(this.css){let e=this.opts.from?this.toUrl(this.path(this.opts.from)):"<no source>";this.map.setSourceContent(e,this.css)}}sourcePath(e){return this.mapOpts.from?this.toUrl(this.mapOpts.from):this.usesFileUrls?this.toFileUrl(e.source.input.from):this.toUrl(this.path(e.source.input.from))}toBase64(e){return r?r.from(e).toString("base64"):window.btoa(unescape(encodeURIComponent(e)))}toFileUrl(e){let t=this.memoizedFileURLs.get(e);if(t)return t;if(u){let t=u(e).toString();return this.memoizedFileURLs.set(e,t),t}throw new Error("`map.absolute` option is not available in this PostCSS build")}toUrl(e){let t=this.memoizedURLs.get(e);if(t)return t;"\\"===a&&(e=e.replace(/\\/g,"/"));let n=encodeURI(e).replace(/[#?]/g,encodeURIComponent);return this.memoizedURLs.set(e,n),n}}},3614:(e,t,n)=>{"use strict";let r=n(8633),i=n(9746);class o extends Error{constructor(e,t,n,r,i,s){super(e),this.name="CssSyntaxError",this.reason=e,i&&(this.file=i),r&&(this.source=r),s&&(this.plugin=s),void 0!==t&&void 0!==n&&("number"==typeof t?(this.line=t,this.column=n):(this.line=t.line,this.column=t.column,this.endLine=n.line,this.endColumn=n.column)),this.setMessage(),Error.captureStackTrace&&Error.captureStackTrace(this,o)}setMessage(){this.message=this.plugin?this.plugin+": ":"",this.message+=this.file?this.file:"<css input>",void 0!==this.line&&(this.message+=":"+this.line+":"+this.column),this.message+=": "+this.reason}showSourceCode(e){if(!this.source)return"";let t=this.source;null==e&&(e=r.isColorSupported);let n=e=>e,o=e=>e,s=e=>e;if(e){let{bold:e,gray:t,red:a}=r.createColors(!0);o=t=>e(a(t)),n=e=>t(e),i&&(s=e=>i(e))}let a=t.split(/\r?\n/),l=Math.max(this.line-3,0),c=Math.min(this.line+2,a.length),u=String(c).length;return a.slice(l,c).map(((e,t)=>{let r=l+1+t,i=" "+(" "+r).slice(-u)+" | ";if(r===this.line){if(e.length>160){let t=20,r=Math.max(0,this.column-t),a=Math.max(this.column+t,this.endColumn+t),l=e.slice(r,a),c=n(i.replace(/\d/g," "))+e.slice(0,Math.min(this.column-1,t-1)).replace(/[^\t]/g," ");return o(">")+n(i)+s(l)+"\n "+c+o("^")}let t=n(i.replace(/\d/g," "))+e.slice(0,this.column-1).replace(/[^\t]/g," ");return o(">")+n(i)+s(e)+"\n "+t+o("^")}return" "+n(i)+s(e)})).join("\n")}toString(){let e=this.showSourceCode();return e&&(e="\n\n"+e+"\n"),this.name+": "+this.message+e}}e.exports=o,o.default=o},3717:(e,t,n)=>{"use strict";let r=n(38);class i{get content(){return this.css}constructor(e,t,n){this.processor=e,this.messages=[],this.root=t,this.opts=n,this.css=void 0,this.map=void 0}toString(){return this.css}warn(e,t={}){t.plugin||this.lastPlugin&&this.lastPlugin.postcssPlugin&&(t.plugin=this.lastPlugin.postcssPlugin);let n=new r(e,t);return this.messages.push(n),n}warnings(){return this.messages.filter((e=>"warning"===e.type))}}e.exports=i,i.default=i},3878:(e,t,n)=>{"use strict";var r=n(8287).hp;let{existsSync:i,readFileSync:o}=n(9977),{dirname:s,join:a}=n(197),{SourceMapConsumer:l,SourceMapGenerator:c}=n(1866);class u{constructor(e,t){if(!1===t.map)return;this.loadAnnotation(e),this.inline=this.startWith(this.annotation,"data:");let n=t.map?t.map.prev:void 0,r=this.loadMap(t.from,n);!this.mapFile&&t.from&&(this.mapFile=t.from),this.mapFile&&(this.root=s(this.mapFile)),r&&(this.text=r)}consumer(){return this.consumerCache||(this.consumerCache=new l(this.text)),this.consumerCache}decodeInline(e){let t=e.match(/^data:application\/json;charset=utf-?8,/)||e.match(/^data:application\/json,/);if(t)return decodeURIComponent(e.substr(t[0].length));let n=e.match(/^data:application\/json;charset=utf-?8;base64,/)||e.match(/^data:application\/json;base64,/);if(n)return i=e.substr(n[0].length),r?r.from(i,"base64").toString():window.atob(i);var i;let o=e.match(/data:application\/json;([^,]+),/)[1];throw new Error("Unsupported source map encoding "+o)}getAnnotationURL(e){return e.replace(/^\/\*\s*# sourceMappingURL=/,"").trim()}isMap(e){return"object"==typeof e&&("string"==typeof e.mappings||"string"==typeof e._mappings||Array.isArray(e.sections))}loadAnnotation(e){let t=e.match(/\/\*\s*# sourceMappingURL=/g);if(!t)return;let n=e.lastIndexOf(t.pop()),r=e.indexOf("*/",n);n>-1&&r>-1&&(this.annotation=this.getAnnotationURL(e.substring(n,r)))}loadFile(e){if(this.root=s(e),i(e))return this.mapFile=e,o(e,"utf-8").toString().trim()}loadMap(e,t){if(!1===t)return!1;if(t){if("string"==typeof t)return t;if("function"!=typeof t){if(t instanceof l)return c.fromSourceMap(t).toString();if(t instanceof c)return t.toString();if(this.isMap(t))return JSON.stringify(t);throw new Error("Unsupported previous source map format: "+t.toString())}{let n=t(e);if(n){let e=this.loadFile(n);if(!e)throw new Error("Unable to load previous source map: "+n.toString());return e}}}else{if(this.inline)return this.decodeInline(this.annotation);if(this.annotation){let t=this.annotation;return e&&(t=a(s(e),t)),this.loadFile(t)}}}startWith(e,t){return!!e&&e.substr(0,t.length)===t}withContent(){return!!(this.consumer().sourcesContent&&this.consumer().sourcesContent.length>0)}}e.exports=u,u.default=u},4128:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||r(t,e,n)};Object.defineProperty(t,"__esModule",{value:!0}),t.DomHandler=void 0;var o=n(5413),s=n(430);i(n(430),t);var a={withStartIndices:!1,withEndIndices:!1,xmlMode:!1},l=function(){function e(e,t,n){this.dom=[],this.root=new s.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof t&&(n=t,t=a),"object"==typeof e&&(t=e,e=void 0),this.callback=null!=e?e:null,this.options=null!=t?t:a,this.elementCB=null!=n?n:null}return e.prototype.onparserinit=function(e){this.parser=e},e.prototype.onreset=function(){this.dom=[],this.root=new s.Document(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null},e.prototype.onend=function(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))},e.prototype.onerror=function(e){this.handleCallback(e)},e.prototype.onclosetag=function(){this.lastNode=null;var e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)},e.prototype.onopentag=function(e,t){var n=this.options.xmlMode?o.ElementType.Tag:void 0,r=new s.Element(e,t,void 0,n);this.addNode(r),this.tagStack.push(r)},e.prototype.ontext=function(e){var t=this.lastNode;if(t&&t.type===o.ElementType.Text)t.data+=e,this.options.withEndIndices&&(t.endIndex=this.parser.endIndex);else{var n=new s.Text(e);this.addNode(n),this.lastNode=n}},e.prototype.oncomment=function(e){if(this.lastNode&&this.lastNode.type===o.ElementType.Comment)this.lastNode.data+=e;else{var t=new s.Comment(e);this.addNode(t),this.lastNode=t}},e.prototype.oncommentend=function(){this.lastNode=null},e.prototype.oncdatastart=function(){var e=new s.Text(""),t=new s.CDATA([e]);this.addNode(t),e.parent=t,this.lastNode=e},e.prototype.oncdataend=function(){this.lastNode=null},e.prototype.onprocessinginstruction=function(e,t){var n=new s.ProcessingInstruction(e,t);this.addNode(n)},e.prototype.handleCallback=function(e){if("function"==typeof this.callback)this.callback(e,this.dom);else if(e)throw e},e.prototype.addNode=function(e){var t=this.tagStack[this.tagStack.length-1],n=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),n&&(e.prev=n,n.next=e),e.parent=t,this.lastNode=null},e}();t.DomHandler=l,t.default=l},4151:e=>{"use strict";e.exports.isClean=Symbol("isClean"),e.exports.my=Symbol("my")},4211:(e,t,n)=>{"use strict";let r=n(3604),i=n(9577);const o=n(3717);let s=n(3303);n(6156);class a{get content(){return this.result.css}get css(){return this.result.css}get map(){return this.result.map}get messages(){return[]}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){if(this._root)return this._root;let e,t=i;try{e=t(this._css,this._opts)}catch(e){this.error=e}if(this.error)throw this.error;return this._root=e,e}get[Symbol.toStringTag](){return"NoWorkResult"}constructor(e,t,n){let i;t=t.toString(),this.stringified=!1,this._processor=e,this._css=t,this._opts=n,this._map=void 0;let a=s;this.result=new o(this._processor,i,this._opts),this.result.css=t;let l=this;Object.defineProperty(this.result,"root",{get:()=>l.root});let c=new r(a,i,this._opts,t);if(c.isMap()){let[e,t]=c.generate();e&&(this.result.css=e),t&&(this.result.map=t)}else c.clearAnnotation(),this.result.css=c.css}async(){return this.error?Promise.reject(this.error):Promise.resolve(this.result)}catch(e){return this.async().catch(e)}finally(e){return this.async().then(e,e)}sync(){if(this.error)throw this.error;return this.result}then(e,t){return this.async().then(e,t)}toString(){return this._css}warnings(){return[]}}e.exports=a,a.default=a},4634:e=>{var t={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==t.call(e)}},4692:function(e,t){var n;
/*!
 * jQuery JavaScript Library v3.7.1
 * https://jquery.com/
 *
 * Copyright OpenJS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2023-08-28T13:37Z
 */!function(t,n){"use strict";"object"==typeof e.exports?e.exports=t.document?n(t,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return n(e)}:n(t)}("undefined"!=typeof window?window:this,(function(r,i){"use strict";var o=[],s=Object.getPrototypeOf,a=o.slice,l=o.flat?function(e){return o.flat.call(e)}:function(e){return o.concat.apply([],e)},c=o.push,u=o.indexOf,h={},f=h.toString,d=h.hasOwnProperty,p=d.toString,m=p.call(Object),g={},y=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType&&"function"!=typeof e.item},v=function(e){return null!=e&&e===e.window},b=r.document,w={type:!0,src:!0,nonce:!0,noModule:!0};function x(e,t,n){var r,i,o=(n=n||b).createElement("script");if(o.text=e,t)for(r in w)(i=t[r]||t.getAttribute&&t.getAttribute(r))&&o.setAttribute(r,i);n.head.appendChild(o).parentNode.removeChild(o)}function _(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?h[f.call(e)]||"object":typeof e}var T="3.7.1",E=/HTML$/i,A=function(e,t){return new A.fn.init(e,t)};function S(e){var t=!!e&&"length"in e&&e.length,n=_(e);return!y(e)&&!v(e)&&("array"===n||0===t||"number"==typeof t&&t>0&&t-1 in e)}function C(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}A.fn=A.prototype={jquery:T,constructor:A,length:0,toArray:function(){return a.call(this)},get:function(e){return null==e?a.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=A.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return A.each(this,e)},map:function(e){return this.pushStack(A.map(this,(function(t,n){return e.call(t,n,t)})))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(A.grep(this,(function(e,t){return(t+1)%2})))},odd:function(){return this.pushStack(A.grep(this,(function(e,t){return t%2})))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(n>=0&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:c,sort:o.sort,splice:o.splice},A.extend=A.fn.extend=function(){var e,t,n,r,i,o,s=arguments[0]||{},a=1,l=arguments.length,c=!1;for("boolean"==typeof s&&(c=s,s=arguments[a]||{},a++),"object"==typeof s||y(s)||(s={}),a===l&&(s=this,a--);a<l;a++)if(null!=(e=arguments[a]))for(t in e)r=e[t],"__proto__"!==t&&s!==r&&(c&&r&&(A.isPlainObject(r)||(i=Array.isArray(r)))?(n=s[t],o=i&&!Array.isArray(n)?[]:i||A.isPlainObject(n)?n:{},i=!1,s[t]=A.extend(c,o,r)):void 0!==r&&(s[t]=r));return s},A.extend({expando:"jQuery"+(T+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==f.call(e))&&(!(t=s(e))||"function"==typeof(n=d.call(t,"constructor")&&t.constructor)&&p.call(n)===m)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,n){x(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,r=0;if(S(e))for(n=e.length;r<n&&!1!==t.call(e[r],r,e[r]);r++);else for(r in e)if(!1===t.call(e[r],r,e[r]))break;return e},text:function(e){var t,n="",r=0,i=e.nodeType;if(!i)for(;t=e[r++];)n+=A.text(t);return 1===i||11===i?e.textContent:9===i?e.documentElement.textContent:3===i||4===i?e.nodeValue:n},makeArray:function(e,t){var n=t||[];return null!=e&&(S(Object(e))?A.merge(n,"string"==typeof e?[e]:e):c.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:u.call(t,e,n)},isXMLDoc:function(e){var t=e&&e.namespaceURI,n=e&&(e.ownerDocument||e).documentElement;return!E.test(t||n&&n.nodeName||"HTML")},merge:function(e,t){for(var n=+t.length,r=0,i=e.length;r<n;r++)e[i++]=t[r];return e.length=i,e},grep:function(e,t,n){for(var r=[],i=0,o=e.length,s=!n;i<o;i++)!t(e[i],i)!==s&&r.push(e[i]);return r},map:function(e,t,n){var r,i,o=0,s=[];if(S(e))for(r=e.length;o<r;o++)null!=(i=t(e[o],o,n))&&s.push(i);else for(o in e)null!=(i=t(e[o],o,n))&&s.push(i);return l(s)},guid:1,support:g}),"function"==typeof Symbol&&(A.fn[Symbol.iterator]=o[Symbol.iterator]),A.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(e,t){h["[object "+t+"]"]=t.toLowerCase()}));var k=o.pop,O=o.sort,N=o.splice,D="[\\x20\\t\\r\\n\\f]",L=new RegExp("^"+D+"+|((?:^|[^\\\\])(?:\\\\.)*)"+D+"+$","g");A.contains=function(e,t){var n=t&&t.parentNode;return e===n||!(!n||1!==n.nodeType||!(e.contains?e.contains(n):e.compareDocumentPosition&&16&e.compareDocumentPosition(n)))};var P=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function I(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}A.escapeSelector=function(e){return(e+"").replace(P,I)};var j=b,M=c;!function(){var e,t,n,i,s,l,c,h,f,p,m=M,y=A.expando,v=0,b=0,w=ee(),x=ee(),_=ee(),T=ee(),E=function(e,t){return e===t&&(s=!0),0},S="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",P="(?:\\\\[\\da-fA-F]{1,6}"+D+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",I="\\["+D+"*("+P+")(?:"+D+"*([*^$|!~]?=)"+D+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+P+"))|)"+D+"*\\]",q=":("+P+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+I+")*)|.*)\\)|)",R=new RegExp(D+"+","g"),B=new RegExp("^"+D+"*,"+D+"*"),$=new RegExp("^"+D+"*([>+~]|"+D+")"+D+"*"),H=new RegExp(D+"|>"),U=new RegExp(q),F=new RegExp("^"+P+"$"),V={ID:new RegExp("^#("+P+")"),CLASS:new RegExp("^\\.("+P+")"),TAG:new RegExp("^("+P+"|[*])"),ATTR:new RegExp("^"+I),PSEUDO:new RegExp("^"+q),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+D+"*(even|odd|(([+-]|)(\\d*)n|)"+D+"*(?:([+-]|)"+D+"*(\\d+)|))"+D+"*\\)|)","i"),bool:new RegExp("^(?:"+S+")$","i"),needsContext:new RegExp("^"+D+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+D+"*((?:-\\d)?\\d*)"+D+"*\\)|)(?=[^-]|$)","i")},z=/^(?:input|select|textarea|button)$/i,W=/^h\d$/i,G=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,Y=/[+~]/,X=new RegExp("\\\\[\\da-fA-F]{1,6}"+D+"?|\\\\([^\\r\\n\\f])","g"),Q=function(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(n+65536):String.fromCharCode(n>>10|55296,1023&n|56320))},J=function(){le()},Z=fe((function(e){return!0===e.disabled&&C(e,"fieldset")}),{dir:"parentNode",next:"legend"});try{m.apply(o=a.call(j.childNodes),j.childNodes),o[j.childNodes.length].nodeType}catch(e){m={apply:function(e,t){M.apply(e,a.call(t))},call:function(e){M.apply(e,a.call(arguments,1))}}}function K(e,t,n,r){var i,o,s,a,c,u,d,p=t&&t.ownerDocument,v=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==v&&9!==v&&11!==v)return n;if(!r&&(le(t),t=t||l,h)){if(11!==v&&(c=G.exec(e)))if(i=c[1]){if(9===v){if(!(s=t.getElementById(i)))return n;if(s.id===i)return m.call(n,s),n}else if(p&&(s=p.getElementById(i))&&K.contains(t,s)&&s.id===i)return m.call(n,s),n}else{if(c[2])return m.apply(n,t.getElementsByTagName(e)),n;if((i=c[3])&&t.getElementsByClassName)return m.apply(n,t.getElementsByClassName(i)),n}if(!(T[e+" "]||f&&f.test(e))){if(d=e,p=t,1===v&&(H.test(e)||$.test(e))){for((p=Y.test(e)&&ae(t.parentNode)||t)==t&&g.scope||((a=t.getAttribute("id"))?a=A.escapeSelector(a):t.setAttribute("id",a=y)),o=(u=ue(e)).length;o--;)u[o]=(a?"#"+a:":scope")+" "+he(u[o]);d=u.join(",")}try{return m.apply(n,p.querySelectorAll(d)),n}catch(t){T(e,!0)}finally{a===y&&t.removeAttribute("id")}}}return ve(e.replace(L,"$1"),t,n,r)}function ee(){var e=[];return function n(r,i){return e.push(r+" ")>t.cacheLength&&delete n[e.shift()],n[r+" "]=i}}function te(e){return e[y]=!0,e}function ne(e){var t=l.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function re(e){return function(t){return C(t,"input")&&t.type===e}}function ie(e){return function(t){return(C(t,"input")||C(t,"button"))&&t.type===e}}function oe(e){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===e:t.disabled===e:t.isDisabled===e||t.isDisabled!==!e&&Z(t)===e:t.disabled===e:"label"in t&&t.disabled===e}}function se(e){return te((function(t){return t=+t,te((function(n,r){for(var i,o=e([],n.length,t),s=o.length;s--;)n[i=o[s]]&&(n[i]=!(r[i]=n[i]))}))}))}function ae(e){return e&&void 0!==e.getElementsByTagName&&e}function le(e){var n,r=e?e.ownerDocument||e:j;return r!=l&&9===r.nodeType&&r.documentElement?(c=(l=r).documentElement,h=!A.isXMLDoc(l),p=c.matches||c.webkitMatchesSelector||c.msMatchesSelector,c.msMatchesSelector&&j!=l&&(n=l.defaultView)&&n.top!==n&&n.addEventListener("unload",J),g.getById=ne((function(e){return c.appendChild(e).id=A.expando,!l.getElementsByName||!l.getElementsByName(A.expando).length})),g.disconnectedMatch=ne((function(e){return p.call(e,"*")})),g.scope=ne((function(){return l.querySelectorAll(":scope")})),g.cssHas=ne((function(){try{return l.querySelector(":has(*,:jqfake)"),!1}catch(e){return!0}})),g.getById?(t.filter.ID=function(e){var t=e.replace(X,Q);return function(e){return e.getAttribute("id")===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&h){var n=t.getElementById(e);return n?[n]:[]}}):(t.filter.ID=function(e){var t=e.replace(X,Q);return function(e){var n=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return n&&n.value===t}},t.find.ID=function(e,t){if(void 0!==t.getElementById&&h){var n,r,i,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(i=t.getElementsByName(e),r=0;o=i[r++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),t.find.TAG=function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):t.querySelectorAll(e)},t.find.CLASS=function(e,t){if(void 0!==t.getElementsByClassName&&h)return t.getElementsByClassName(e)},f=[],ne((function(e){var t;c.appendChild(e).innerHTML="<a id='"+y+"' href='' disabled='disabled'></a><select id='"+y+"-\r\\' disabled='disabled'><option selected=''></option></select>",e.querySelectorAll("[selected]").length||f.push("\\["+D+"*(?:value|"+S+")"),e.querySelectorAll("[id~="+y+"-]").length||f.push("~="),e.querySelectorAll("a#"+y+"+*").length||f.push(".#.+[+~]"),e.querySelectorAll(":checked").length||f.push(":checked"),(t=l.createElement("input")).setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),c.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&f.push(":enabled",":disabled"),(t=l.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||f.push("\\["+D+"*name"+D+"*="+D+"*(?:''|\"\")")})),g.cssHas||f.push(":has"),f=f.length&&new RegExp(f.join("|")),E=function(e,t){if(e===t)return s=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!g.sortDetached&&t.compareDocumentPosition(e)===n?e===l||e.ownerDocument==j&&K.contains(j,e)?-1:t===l||t.ownerDocument==j&&K.contains(j,t)?1:i?u.call(i,e)-u.call(i,t):0:4&n?-1:1)},l):l}for(e in K.matches=function(e,t){return K(e,null,null,t)},K.matchesSelector=function(e,t){if(le(e),h&&!T[t+" "]&&(!f||!f.test(t)))try{var n=p.call(e,t);if(n||g.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){T(t,!0)}return K(t,l,null,[e]).length>0},K.contains=function(e,t){return(e.ownerDocument||e)!=l&&le(e),A.contains(e,t)},K.attr=function(e,n){(e.ownerDocument||e)!=l&&le(e);var r=t.attrHandle[n.toLowerCase()],i=r&&d.call(t.attrHandle,n.toLowerCase())?r(e,n,!h):void 0;return void 0!==i?i:e.getAttribute(n)},K.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},A.uniqueSort=function(e){var t,n=[],r=0,o=0;if(s=!g.sortStable,i=!g.sortStable&&a.call(e,0),O.call(e,E),s){for(;t=e[o++];)t===e[o]&&(r=n.push(o));for(;r--;)N.call(e,n[r],1)}return i=null,e},A.fn.uniqueSort=function(){return this.pushStack(A.uniqueSort(a.apply(this)))},t=A.expr={cacheLength:50,createPseudo:te,match:V,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(X,Q),e[3]=(e[3]||e[4]||e[5]||"").replace(X,Q),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||K.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&K.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return V.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&U.test(n)&&(t=ue(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(X,Q).toLowerCase();return"*"===e?function(){return!0}:function(e){return C(e,t)}},CLASS:function(e){var t=w[e+" "];return t||(t=new RegExp("(^|"+D+")"+e+"("+D+"|$)"))&&w(e,(function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")}))},ATTR:function(e,t,n){return function(r){var i=K.attr(r,e);return null==i?"!="===t:!t||(i+="","="===t?i===n:"!="===t?i!==n:"^="===t?n&&0===i.indexOf(n):"*="===t?n&&i.indexOf(n)>-1:"$="===t?n&&i.slice(-n.length)===n:"~="===t?(" "+i.replace(R," ")+" ").indexOf(n)>-1:"|="===t&&(i===n||i.slice(0,n.length+1)===n+"-"))}},CHILD:function(e,t,n,r,i){var o="nth"!==e.slice(0,3),s="last"!==e.slice(-4),a="of-type"===t;return 1===r&&0===i?function(e){return!!e.parentNode}:function(t,n,l){var c,u,h,f,d,p=o!==s?"nextSibling":"previousSibling",m=t.parentNode,g=a&&t.nodeName.toLowerCase(),b=!l&&!a,w=!1;if(m){if(o){for(;p;){for(h=t;h=h[p];)if(a?C(h,g):1===h.nodeType)return!1;d=p="only"===e&&!d&&"nextSibling"}return!0}if(d=[s?m.firstChild:m.lastChild],s&&b){for(w=(f=(c=(u=m[y]||(m[y]={}))[e]||[])[0]===v&&c[1])&&c[2],h=f&&m.childNodes[f];h=++f&&h&&h[p]||(w=f=0)||d.pop();)if(1===h.nodeType&&++w&&h===t){u[e]=[v,f,w];break}}else if(b&&(w=f=(c=(u=t[y]||(t[y]={}))[e]||[])[0]===v&&c[1]),!1===w)for(;(h=++f&&h&&h[p]||(w=f=0)||d.pop())&&(!(a?C(h,g):1===h.nodeType)||!++w||(b&&((u=h[y]||(h[y]={}))[e]=[v,w]),h!==t)););return(w-=i)===r||w%r==0&&w/r>=0}}},PSEUDO:function(e,n){var r,i=t.pseudos[e]||t.setFilters[e.toLowerCase()]||K.error("unsupported pseudo: "+e);return i[y]?i(n):i.length>1?(r=[e,e,"",n],t.setFilters.hasOwnProperty(e.toLowerCase())?te((function(e,t){for(var r,o=i(e,n),s=o.length;s--;)e[r=u.call(e,o[s])]=!(t[r]=o[s])})):function(e){return i(e,0,r)}):i}},pseudos:{not:te((function(e){var t=[],n=[],r=ye(e.replace(L,"$1"));return r[y]?te((function(e,t,n,i){for(var o,s=r(e,null,i,[]),a=e.length;a--;)(o=s[a])&&(e[a]=!(t[a]=o))})):function(e,i,o){return t[0]=e,r(t,null,o,n),t[0]=null,!n.pop()}})),has:te((function(e){return function(t){return K(e,t).length>0}})),contains:te((function(e){return e=e.replace(X,Q),function(t){return(t.textContent||A.text(t)).indexOf(e)>-1}})),lang:te((function(e){return F.test(e||"")||K.error("unsupported lang: "+e),e=e.replace(X,Q).toLowerCase(),function(t){var n;do{if(n=h?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(n=n.toLowerCase())===e||0===n.indexOf(e+"-")}while((t=t.parentNode)&&1===t.nodeType);return!1}})),target:function(e){var t=r.location&&r.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===c},focus:function(e){return e===function(){try{return l.activeElement}catch(e){}}()&&l.hasFocus()&&!!(e.type||e.href||~e.tabIndex)},enabled:oe(!1),disabled:oe(!0),checked:function(e){return C(e,"input")&&!!e.checked||C(e,"option")&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!t.pseudos.empty(e)},header:function(e){return W.test(e.nodeName)},input:function(e){return z.test(e.nodeName)},button:function(e){return C(e,"input")&&"button"===e.type||C(e,"button")},text:function(e){var t;return C(e,"input")&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:se((function(){return[0]})),last:se((function(e,t){return[t-1]})),eq:se((function(e,t,n){return[n<0?n+t:n]})),even:se((function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e})),odd:se((function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e})),lt:se((function(e,t,n){var r;for(r=n<0?n+t:n>t?t:n;--r>=0;)e.push(r);return e})),gt:se((function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e}))}},t.pseudos.nth=t.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[e]=re(e);for(e in{submit:!0,reset:!0})t.pseudos[e]=ie(e);function ce(){}function ue(e,n){var r,i,o,s,a,l,c,u=x[e+" "];if(u)return n?0:u.slice(0);for(a=e,l=[],c=t.preFilter;a;){for(s in r&&!(i=B.exec(a))||(i&&(a=a.slice(i[0].length)||a),l.push(o=[])),r=!1,(i=$.exec(a))&&(r=i.shift(),o.push({value:r,type:i[0].replace(L," ")}),a=a.slice(r.length)),t.filter)!(i=V[s].exec(a))||c[s]&&!(i=c[s](i))||(r=i.shift(),o.push({value:r,type:s,matches:i}),a=a.slice(r.length));if(!r)break}return n?a.length:a?K.error(e):x(e,l).slice(0)}function he(e){for(var t=0,n=e.length,r="";t<n;t++)r+=e[t].value;return r}function fe(e,t,n){var r=t.dir,i=t.next,o=i||r,s=n&&"parentNode"===o,a=b++;return t.first?function(t,n,i){for(;t=t[r];)if(1===t.nodeType||s)return e(t,n,i);return!1}:function(t,n,l){var c,u,h=[v,a];if(l){for(;t=t[r];)if((1===t.nodeType||s)&&e(t,n,l))return!0}else for(;t=t[r];)if(1===t.nodeType||s)if(u=t[y]||(t[y]={}),i&&C(t,i))t=t[r]||t;else{if((c=u[o])&&c[0]===v&&c[1]===a)return h[2]=c[2];if(u[o]=h,h[2]=e(t,n,l))return!0}return!1}}function de(e){return e.length>1?function(t,n,r){for(var i=e.length;i--;)if(!e[i](t,n,r))return!1;return!0}:e[0]}function pe(e,t,n,r,i){for(var o,s=[],a=0,l=e.length,c=null!=t;a<l;a++)(o=e[a])&&(n&&!n(o,r,i)||(s.push(o),c&&t.push(a)));return s}function me(e,t,n,r,i,o){return r&&!r[y]&&(r=me(r)),i&&!i[y]&&(i=me(i,o)),te((function(o,s,a,l){var c,h,f,d,p=[],g=[],y=s.length,v=o||function(e,t,n){for(var r=0,i=t.length;r<i;r++)K(e,t[r],n);return n}(t||"*",a.nodeType?[a]:a,[]),b=!e||!o&&t?v:pe(v,p,e,a,l);if(n?n(b,d=i||(o?e:y||r)?[]:s,a,l):d=b,r)for(c=pe(d,g),r(c,[],a,l),h=c.length;h--;)(f=c[h])&&(d[g[h]]=!(b[g[h]]=f));if(o){if(i||e){if(i){for(c=[],h=d.length;h--;)(f=d[h])&&c.push(b[h]=f);i(null,d=[],c,l)}for(h=d.length;h--;)(f=d[h])&&(c=i?u.call(o,f):p[h])>-1&&(o[c]=!(s[c]=f))}}else d=pe(d===s?d.splice(y,d.length):d),i?i(null,s,d,l):m.apply(s,d)}))}function ge(e){for(var r,i,o,s=e.length,a=t.relative[e[0].type],l=a||t.relative[" "],c=a?1:0,h=fe((function(e){return e===r}),l,!0),f=fe((function(e){return u.call(r,e)>-1}),l,!0),d=[function(e,t,i){var o=!a&&(i||t!=n)||((r=t).nodeType?h(e,t,i):f(e,t,i));return r=null,o}];c<s;c++)if(i=t.relative[e[c].type])d=[fe(de(d),i)];else{if((i=t.filter[e[c].type].apply(null,e[c].matches))[y]){for(o=++c;o<s&&!t.relative[e[o].type];o++);return me(c>1&&de(d),c>1&&he(e.slice(0,c-1).concat({value:" "===e[c-2].type?"*":""})).replace(L,"$1"),i,c<o&&ge(e.slice(c,o)),o<s&&ge(e=e.slice(o)),o<s&&he(e))}d.push(i)}return de(d)}function ye(e,r){var i,o=[],s=[],a=_[e+" "];if(!a){for(r||(r=ue(e)),i=r.length;i--;)(a=ge(r[i]))[y]?o.push(a):s.push(a);a=_(e,function(e,r){var i=r.length>0,o=e.length>0,s=function(s,a,c,u,f){var d,p,g,y=0,b="0",w=s&&[],x=[],_=n,T=s||o&&t.find.TAG("*",f),E=v+=null==_?1:Math.random()||.1,S=T.length;for(f&&(n=a==l||a||f);b!==S&&null!=(d=T[b]);b++){if(o&&d){for(p=0,a||d.ownerDocument==l||(le(d),c=!h);g=e[p++];)if(g(d,a||l,c)){m.call(u,d);break}f&&(v=E)}i&&((d=!g&&d)&&y--,s&&w.push(d))}if(y+=b,i&&b!==y){for(p=0;g=r[p++];)g(w,x,a,c);if(s){if(y>0)for(;b--;)w[b]||x[b]||(x[b]=k.call(u));x=pe(x)}m.apply(u,x),f&&!s&&x.length>0&&y+r.length>1&&A.uniqueSort(u)}return f&&(v=E,n=_),w};return i?te(s):s}(s,o)),a.selector=e}return a}function ve(e,n,r,i){var o,s,a,l,c,u="function"==typeof e&&e,f=!i&&ue(e=u.selector||e);if(r=r||[],1===f.length){if((s=f[0]=f[0].slice(0)).length>2&&"ID"===(a=s[0]).type&&9===n.nodeType&&h&&t.relative[s[1].type]){if(!(n=(t.find.ID(a.matches[0].replace(X,Q),n)||[])[0]))return r;u&&(n=n.parentNode),e=e.slice(s.shift().value.length)}for(o=V.needsContext.test(e)?0:s.length;o--&&(a=s[o],!t.relative[l=a.type]);)if((c=t.find[l])&&(i=c(a.matches[0].replace(X,Q),Y.test(s[0].type)&&ae(n.parentNode)||n))){if(s.splice(o,1),!(e=i.length&&he(s)))return m.apply(r,i),r;break}}return(u||ye(e,f))(i,n,!h,r,!n||Y.test(e)&&ae(n.parentNode)||n),r}ce.prototype=t.filters=t.pseudos,t.setFilters=new ce,g.sortStable=y.split("").sort(E).join("")===y,le(),g.sortDetached=ne((function(e){return 1&e.compareDocumentPosition(l.createElement("fieldset"))})),A.find=K,A.expr[":"]=A.expr.pseudos,A.unique=A.uniqueSort,K.compile=ye,K.select=ve,K.setDocument=le,K.tokenize=ue,K.escape=A.escapeSelector,K.getText=A.text,K.isXML=A.isXMLDoc,K.selectors=A.expr,K.support=A.support,K.uniqueSort=A.uniqueSort}();var q=function(e,t,n){for(var r=[],i=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(i&&A(e).is(n))break;r.push(e)}return r},R=function(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n},B=A.expr.match.needsContext,$=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function H(e,t,n){return y(t)?A.grep(e,(function(e,r){return!!t.call(e,r,e)!==n})):t.nodeType?A.grep(e,(function(e){return e===t!==n})):"string"!=typeof t?A.grep(e,(function(e){return u.call(t,e)>-1!==n})):A.filter(t,e,n)}A.filter=function(e,t,n){var r=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===r.nodeType?A.find.matchesSelector(r,e)?[r]:[]:A.find.matches(e,A.grep(t,(function(e){return 1===e.nodeType})))},A.fn.extend({find:function(e){var t,n,r=this.length,i=this;if("string"!=typeof e)return this.pushStack(A(e).filter((function(){for(t=0;t<r;t++)if(A.contains(i[t],this))return!0})));for(n=this.pushStack([]),t=0;t<r;t++)A.find(e,i[t],n);return r>1?A.uniqueSort(n):n},filter:function(e){return this.pushStack(H(this,e||[],!1))},not:function(e){return this.pushStack(H(this,e||[],!0))},is:function(e){return!!H(this,"string"==typeof e&&B.test(e)?A(e):e||[],!1).length}});var U,F=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(A.fn.init=function(e,t,n){var r,i;if(!e)return this;if(n=n||U,"string"==typeof e){if(!(r="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:F.exec(e))||!r[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(r[1]){if(t=t instanceof A?t[0]:t,A.merge(this,A.parseHTML(r[1],t&&t.nodeType?t.ownerDocument||t:b,!0)),$.test(r[1])&&A.isPlainObject(t))for(r in t)y(this[r])?this[r](t[r]):this.attr(r,t[r]);return this}return(i=b.getElementById(r[2]))&&(this[0]=i,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):y(e)?void 0!==n.ready?n.ready(e):e(A):A.makeArray(e,this)}).prototype=A.fn,U=A(b);var V=/^(?:parents|prev(?:Until|All))/,z={children:!0,contents:!0,next:!0,prev:!0};function W(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}A.fn.extend({has:function(e){var t=A(e,this),n=t.length;return this.filter((function(){for(var e=0;e<n;e++)if(A.contains(this,t[e]))return!0}))},closest:function(e,t){var n,r=0,i=this.length,o=[],s="string"!=typeof e&&A(e);if(!B.test(e))for(;r<i;r++)for(n=this[r];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?s.index(n)>-1:1===n.nodeType&&A.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(o.length>1?A.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?u.call(A(e),this[0]):u.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(A.uniqueSort(A.merge(this.get(),A(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),A.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return q(e,"parentNode")},parentsUntil:function(e,t,n){return q(e,"parentNode",n)},next:function(e){return W(e,"nextSibling")},prev:function(e){return W(e,"previousSibling")},nextAll:function(e){return q(e,"nextSibling")},prevAll:function(e){return q(e,"previousSibling")},nextUntil:function(e,t,n){return q(e,"nextSibling",n)},prevUntil:function(e,t,n){return q(e,"previousSibling",n)},siblings:function(e){return R((e.parentNode||{}).firstChild,e)},children:function(e){return R(e.firstChild)},contents:function(e){return null!=e.contentDocument&&s(e.contentDocument)?e.contentDocument:(C(e,"template")&&(e=e.content||e),A.merge([],e.childNodes))}},(function(e,t){A.fn[e]=function(n,r){var i=A.map(this,t,n);return"Until"!==e.slice(-5)&&(r=n),r&&"string"==typeof r&&(i=A.filter(r,i)),this.length>1&&(z[e]||A.uniqueSort(i),V.test(e)&&i.reverse()),this.pushStack(i)}}));var G=/[^\x20\t\r\n\f]+/g;function Y(e){return e}function X(e){throw e}function Q(e,t,n,r){var i;try{e&&y(i=e.promise)?i.call(e).done(t).fail(n):e&&y(i=e.then)?i.call(e,t,n):t.apply(void 0,[e].slice(r))}catch(e){n.apply(void 0,[e])}}A.Callbacks=function(e){e="string"==typeof e?function(e){var t={};return A.each(e.match(G)||[],(function(e,n){t[n]=!0})),t}(e):A.extend({},e);var t,n,r,i,o=[],s=[],a=-1,l=function(){for(i=i||e.once,r=t=!0;s.length;a=-1)for(n=s.shift();++a<o.length;)!1===o[a].apply(n[0],n[1])&&e.stopOnFalse&&(a=o.length,n=!1);e.memory||(n=!1),t=!1,i&&(o=n?[]:"")},c={add:function(){return o&&(n&&!t&&(a=o.length-1,s.push(n)),function t(n){A.each(n,(function(n,r){y(r)?e.unique&&c.has(r)||o.push(r):r&&r.length&&"string"!==_(r)&&t(r)}))}(arguments),n&&!t&&l()),this},remove:function(){return A.each(arguments,(function(e,t){for(var n;(n=A.inArray(t,o,n))>-1;)o.splice(n,1),n<=a&&a--})),this},has:function(e){return e?A.inArray(e,o)>-1:o.length>0},empty:function(){return o&&(o=[]),this},disable:function(){return i=s=[],o=n="",this},disabled:function(){return!o},lock:function(){return i=s=[],n||t||(o=n=""),this},locked:function(){return!!i},fireWith:function(e,n){return i||(n=[e,(n=n||[]).slice?n.slice():n],s.push(n),t||l()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},A.extend({Deferred:function(e){var t=[["notify","progress",A.Callbacks("memory"),A.Callbacks("memory"),2],["resolve","done",A.Callbacks("once memory"),A.Callbacks("once memory"),0,"resolved"],["reject","fail",A.Callbacks("once memory"),A.Callbacks("once memory"),1,"rejected"]],n="pending",i={state:function(){return n},always:function(){return o.done(arguments).fail(arguments),this},catch:function(e){return i.then(null,e)},pipe:function(){var e=arguments;return A.Deferred((function(n){A.each(t,(function(t,r){var i=y(e[r[4]])&&e[r[4]];o[r[1]]((function(){var e=i&&i.apply(this,arguments);e&&y(e.promise)?e.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[r[0]+"With"](this,i?[e]:arguments)}))})),e=null})).promise()},then:function(e,n,i){var o=0;function s(e,t,n,i){return function(){var a=this,l=arguments,c=function(){var r,c;if(!(e<o)){if((r=n.apply(a,l))===t.promise())throw new TypeError("Thenable self-resolution");c=r&&("object"==typeof r||"function"==typeof r)&&r.then,y(c)?i?c.call(r,s(o,t,Y,i),s(o,t,X,i)):(o++,c.call(r,s(o,t,Y,i),s(o,t,X,i),s(o,t,Y,t.notifyWith))):(n!==Y&&(a=void 0,l=[r]),(i||t.resolveWith)(a,l))}},u=i?c:function(){try{c()}catch(r){A.Deferred.exceptionHook&&A.Deferred.exceptionHook(r,u.error),e+1>=o&&(n!==X&&(a=void 0,l=[r]),t.rejectWith(a,l))}};e?u():(A.Deferred.getErrorHook?u.error=A.Deferred.getErrorHook():A.Deferred.getStackHook&&(u.error=A.Deferred.getStackHook()),r.setTimeout(u))}}return A.Deferred((function(r){t[0][3].add(s(0,r,y(i)?i:Y,r.notifyWith)),t[1][3].add(s(0,r,y(e)?e:Y)),t[2][3].add(s(0,r,y(n)?n:X))})).promise()},promise:function(e){return null!=e?A.extend(e,i):i}},o={};return A.each(t,(function(e,r){var s=r[2],a=r[5];i[r[1]]=s.add,a&&s.add((function(){n=a}),t[3-e][2].disable,t[3-e][3].disable,t[0][2].lock,t[0][3].lock),s.add(r[3].fire),o[r[0]]=function(){return o[r[0]+"With"](this===o?void 0:this,arguments),this},o[r[0]+"With"]=s.fireWith})),i.promise(o),e&&e.call(o,o),o},when:function(e){var t=arguments.length,n=t,r=Array(n),i=a.call(arguments),o=A.Deferred(),s=function(e){return function(n){r[e]=this,i[e]=arguments.length>1?a.call(arguments):n,--t||o.resolveWith(r,i)}};if(t<=1&&(Q(e,o.done(s(n)).resolve,o.reject,!t),"pending"===o.state()||y(i[n]&&i[n].then)))return o.then();for(;n--;)Q(i[n],s(n),o.reject);return o.promise()}});var J=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;A.Deferred.exceptionHook=function(e,t){r.console&&r.console.warn&&e&&J.test(e.name)&&r.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},A.readyException=function(e){r.setTimeout((function(){throw e}))};var Z=A.Deferred();function K(){b.removeEventListener("DOMContentLoaded",K),r.removeEventListener("load",K),A.ready()}A.fn.ready=function(e){return Z.then(e).catch((function(e){A.readyException(e)})),this},A.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--A.readyWait:A.isReady)||(A.isReady=!0,!0!==e&&--A.readyWait>0||Z.resolveWith(b,[A]))}}),A.ready.then=Z.then,"complete"===b.readyState||"loading"!==b.readyState&&!b.documentElement.doScroll?r.setTimeout(A.ready):(b.addEventListener("DOMContentLoaded",K),r.addEventListener("load",K));var ee=function(e,t,n,r,i,o,s){var a=0,l=e.length,c=null==n;if("object"===_(n))for(a in i=!0,n)ee(e,t,a,n[a],!0,o,s);else if(void 0!==r&&(i=!0,y(r)||(s=!0),c&&(s?(t.call(e,r),t=null):(c=t,t=function(e,t,n){return c.call(A(e),n)})),t))for(;a<l;a++)t(e[a],n,s?r:r.call(e[a],a,t(e[a],n)));return i?e:c?t.call(e):l?t(e[0],n):o},te=/^-ms-/,ne=/-([a-z])/g;function re(e,t){return t.toUpperCase()}function ie(e){return e.replace(te,"ms-").replace(ne,re)}var oe=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function se(){this.expando=A.expando+se.uid++}se.uid=1,se.prototype={cache:function(e){var t=e[this.expando];return t||(t={},oe(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var r,i=this.cache(e);if("string"==typeof t)i[ie(t)]=n;else for(r in t)i[ie(r)]=t[r];return i},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][ie(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,r=e[this.expando];if(void 0!==r){if(void 0!==t){n=(t=Array.isArray(t)?t.map(ie):(t=ie(t))in r?[t]:t.match(G)||[]).length;for(;n--;)delete r[t[n]]}(void 0===t||A.isEmptyObject(r))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!A.isEmptyObject(t)}};var ae=new se,le=new se,ce=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ue=/[A-Z]/g;function he(e,t,n){var r;if(void 0===n&&1===e.nodeType)if(r="data-"+t.replace(ue,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(r))){try{n=function(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:ce.test(e)?JSON.parse(e):e)}(n)}catch(e){}le.set(e,t,n)}else n=void 0;return n}A.extend({hasData:function(e){return le.hasData(e)||ae.hasData(e)},data:function(e,t,n){return le.access(e,t,n)},removeData:function(e,t){le.remove(e,t)},_data:function(e,t,n){return ae.access(e,t,n)},_removeData:function(e,t){ae.remove(e,t)}}),A.fn.extend({data:function(e,t){var n,r,i,o=this[0],s=o&&o.attributes;if(void 0===e){if(this.length&&(i=le.get(o),1===o.nodeType&&!ae.get(o,"hasDataAttrs"))){for(n=s.length;n--;)s[n]&&0===(r=s[n].name).indexOf("data-")&&(r=ie(r.slice(5)),he(o,r,i[r]));ae.set(o,"hasDataAttrs",!0)}return i}return"object"==typeof e?this.each((function(){le.set(this,e)})):ee(this,(function(t){var n;if(o&&void 0===t)return void 0!==(n=le.get(o,e))||void 0!==(n=he(o,e))?n:void 0;this.each((function(){le.set(this,e,t)}))}),null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each((function(){le.remove(this,e)}))}}),A.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=ae.get(e,t),n&&(!r||Array.isArray(n)?r=ae.access(e,t,A.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=A.queue(e,t),r=n.length,i=n.shift(),o=A._queueHooks(e,t);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===t&&n.unshift("inprogress"),delete o.stop,i.call(e,(function(){A.dequeue(e,t)}),o)),!r&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return ae.get(e,n)||ae.access(e,n,{empty:A.Callbacks("once memory").add((function(){ae.remove(e,[t+"queue",n])}))})}}),A.fn.extend({queue:function(e,t){var n=2;return"string"!=typeof e&&(t=e,e="fx",n--),arguments.length<n?A.queue(this[0],e):void 0===t?this:this.each((function(){var n=A.queue(this,e,t);A._queueHooks(this,e),"fx"===e&&"inprogress"!==n[0]&&A.dequeue(this,e)}))},dequeue:function(e){return this.each((function(){A.dequeue(this,e)}))},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var n,r=1,i=A.Deferred(),o=this,s=this.length,a=function(){--r||i.resolveWith(o,[o])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";s--;)(n=ae.get(o[s],e+"queueHooks"))&&n.empty&&(r++,n.empty.add(a));return a(),i.promise(t)}});var fe=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,de=new RegExp("^(?:([+-])=|)("+fe+")([a-z%]*)$","i"),pe=["Top","Right","Bottom","Left"],me=b.documentElement,ge=function(e){return A.contains(e.ownerDocument,e)},ye={composed:!0};me.getRootNode&&(ge=function(e){return A.contains(e.ownerDocument,e)||e.getRootNode(ye)===e.ownerDocument});var ve=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&ge(e)&&"none"===A.css(e,"display")};function be(e,t,n,r){var i,o,s=20,a=r?function(){return r.cur()}:function(){return A.css(e,t,"")},l=a(),c=n&&n[3]||(A.cssNumber[t]?"":"px"),u=e.nodeType&&(A.cssNumber[t]||"px"!==c&&+l)&&de.exec(A.css(e,t));if(u&&u[3]!==c){for(l/=2,c=c||u[3],u=+l||1;s--;)A.style(e,t,u+c),(1-o)*(1-(o=a()/l||.5))<=0&&(s=0),u/=o;u*=2,A.style(e,t,u+c),n=n||[]}return n&&(u=+u||+l||0,i=n[1]?u+(n[1]+1)*n[2]:+n[2],r&&(r.unit=c,r.start=u,r.end=i)),i}var we={};function xe(e){var t,n=e.ownerDocument,r=e.nodeName,i=we[r];return i||(t=n.body.appendChild(n.createElement(r)),i=A.css(t,"display"),t.parentNode.removeChild(t),"none"===i&&(i="block"),we[r]=i,i)}function _e(e,t){for(var n,r,i=[],o=0,s=e.length;o<s;o++)(r=e[o]).style&&(n=r.style.display,t?("none"===n&&(i[o]=ae.get(r,"display")||null,i[o]||(r.style.display="")),""===r.style.display&&ve(r)&&(i[o]=xe(r))):"none"!==n&&(i[o]="none",ae.set(r,"display",n)));for(o=0;o<s;o++)null!=i[o]&&(e[o].style.display=i[o]);return e}A.fn.extend({show:function(){return _e(this,!0)},hide:function(){return _e(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each((function(){ve(this)?A(this).show():A(this).hide()}))}});var Te,Ee,Ae=/^(?:checkbox|radio)$/i,Se=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,Ce=/^$|^module$|\/(?:java|ecma)script/i;Te=b.createDocumentFragment().appendChild(b.createElement("div")),(Ee=b.createElement("input")).setAttribute("type","radio"),Ee.setAttribute("checked","checked"),Ee.setAttribute("name","t"),Te.appendChild(Ee),g.checkClone=Te.cloneNode(!0).cloneNode(!0).lastChild.checked,Te.innerHTML="<textarea>x</textarea>",g.noCloneChecked=!!Te.cloneNode(!0).lastChild.defaultValue,Te.innerHTML="<option></option>",g.option=!!Te.lastChild;var ke={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Oe(e,t){var n;return n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&C(e,t)?A.merge([e],n):n}function Ne(e,t){for(var n=0,r=e.length;n<r;n++)ae.set(e[n],"globalEval",!t||ae.get(t[n],"globalEval"))}ke.tbody=ke.tfoot=ke.colgroup=ke.caption=ke.thead,ke.th=ke.td,g.option||(ke.optgroup=ke.option=[1,"<select multiple='multiple'>","</select>"]);var De=/<|&#?\w+;/;function Le(e,t,n,r,i){for(var o,s,a,l,c,u,h=t.createDocumentFragment(),f=[],d=0,p=e.length;d<p;d++)if((o=e[d])||0===o)if("object"===_(o))A.merge(f,o.nodeType?[o]:o);else if(De.test(o)){for(s=s||h.appendChild(t.createElement("div")),a=(Se.exec(o)||["",""])[1].toLowerCase(),l=ke[a]||ke._default,s.innerHTML=l[1]+A.htmlPrefilter(o)+l[2],u=l[0];u--;)s=s.lastChild;A.merge(f,s.childNodes),(s=h.firstChild).textContent=""}else f.push(t.createTextNode(o));for(h.textContent="",d=0;o=f[d++];)if(r&&A.inArray(o,r)>-1)i&&i.push(o);else if(c=ge(o),s=Oe(h.appendChild(o),"script"),c&&Ne(s),n)for(u=0;o=s[u++];)Ce.test(o.type||"")&&n.push(o);return h}var Pe=/^([^.]*)(?:\.(.+)|)/;function Ie(){return!0}function je(){return!1}function Me(e,t,n,r,i,o){var s,a;if("object"==typeof t){for(a in"string"!=typeof n&&(r=r||n,n=void 0),t)Me(e,a,n,r,t[a],o);return e}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=je;else if(!i)return e;return 1===o&&(s=i,i=function(e){return A().off(e),s.apply(this,arguments)},i.guid=s.guid||(s.guid=A.guid++)),e.each((function(){A.event.add(this,t,i,r,n)}))}function qe(e,t,n){n?(ae.set(e,t,!1),A.event.add(e,t,{namespace:!1,handler:function(e){var n,r=ae.get(this,t);if(1&e.isTrigger&&this[t]){if(r)(A.event.special[t]||{}).delegateType&&e.stopPropagation();else if(r=a.call(arguments),ae.set(this,t,r),this[t](),n=ae.get(this,t),ae.set(this,t,!1),r!==n)return e.stopImmediatePropagation(),e.preventDefault(),n}else r&&(ae.set(this,t,A.event.trigger(r[0],r.slice(1),this)),e.stopPropagation(),e.isImmediatePropagationStopped=Ie)}})):void 0===ae.get(e,t)&&A.event.add(e,t,Ie)}A.event={global:{},add:function(e,t,n,r,i){var o,s,a,l,c,u,h,f,d,p,m,g=ae.get(e);if(oe(e))for(n.handler&&(n=(o=n).handler,i=o.selector),i&&A.find.matchesSelector(me,i),n.guid||(n.guid=A.guid++),(l=g.events)||(l=g.events=Object.create(null)),(s=g.handle)||(s=g.handle=function(t){return void 0!==A&&A.event.triggered!==t.type?A.event.dispatch.apply(e,arguments):void 0}),c=(t=(t||"").match(G)||[""]).length;c--;)d=m=(a=Pe.exec(t[c])||[])[1],p=(a[2]||"").split(".").sort(),d&&(h=A.event.special[d]||{},d=(i?h.delegateType:h.bindType)||d,h=A.event.special[d]||{},u=A.extend({type:d,origType:m,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&A.expr.match.needsContext.test(i),namespace:p.join(".")},o),(f=l[d])||((f=l[d]=[]).delegateCount=0,h.setup&&!1!==h.setup.call(e,r,p,s)||e.addEventListener&&e.addEventListener(d,s)),h.add&&(h.add.call(e,u),u.handler.guid||(u.handler.guid=n.guid)),i?f.splice(f.delegateCount++,0,u):f.push(u),A.event.global[d]=!0)},remove:function(e,t,n,r,i){var o,s,a,l,c,u,h,f,d,p,m,g=ae.hasData(e)&&ae.get(e);if(g&&(l=g.events)){for(c=(t=(t||"").match(G)||[""]).length;c--;)if(d=m=(a=Pe.exec(t[c])||[])[1],p=(a[2]||"").split(".").sort(),d){for(h=A.event.special[d]||{},f=l[d=(r?h.delegateType:h.bindType)||d]||[],a=a[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=f.length;o--;)u=f[o],!i&&m!==u.origType||n&&n.guid!==u.guid||a&&!a.test(u.namespace)||r&&r!==u.selector&&("**"!==r||!u.selector)||(f.splice(o,1),u.selector&&f.delegateCount--,h.remove&&h.remove.call(e,u));s&&!f.length&&(h.teardown&&!1!==h.teardown.call(e,p,g.handle)||A.removeEvent(e,d,g.handle),delete l[d])}else for(d in l)A.event.remove(e,d+t[c],n,r,!0);A.isEmptyObject(l)&&ae.remove(e,"handle events")}},dispatch:function(e){var t,n,r,i,o,s,a=new Array(arguments.length),l=A.event.fix(e),c=(ae.get(this,"events")||Object.create(null))[l.type]||[],u=A.event.special[l.type]||{};for(a[0]=l,t=1;t<arguments.length;t++)a[t]=arguments[t];if(l.delegateTarget=this,!u.preDispatch||!1!==u.preDispatch.call(this,l)){for(s=A.event.handlers.call(this,l,c),t=0;(i=s[t++])&&!l.isPropagationStopped();)for(l.currentTarget=i.elem,n=0;(o=i.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==o.namespace&&!l.rnamespace.test(o.namespace)||(l.handleObj=o,l.data=o.data,void 0!==(r=((A.event.special[o.origType]||{}).handle||o.handler).apply(i.elem,a))&&!1===(l.result=r)&&(l.preventDefault(),l.stopPropagation()));return u.postDispatch&&u.postDispatch.call(this,l),l.result}},handlers:function(e,t){var n,r,i,o,s,a=[],l=t.delegateCount,c=e.target;if(l&&c.nodeType&&!("click"===e.type&&e.button>=1))for(;c!==this;c=c.parentNode||this)if(1===c.nodeType&&("click"!==e.type||!0!==c.disabled)){for(o=[],s={},n=0;n<l;n++)void 0===s[i=(r=t[n]).selector+" "]&&(s[i]=r.needsContext?A(i,this).index(c)>-1:A.find(i,this,null,[c]).length),s[i]&&o.push(r);o.length&&a.push({elem:c,handlers:o})}return c=this,l<t.length&&a.push({elem:c,handlers:t.slice(l)}),a},addProp:function(e,t){Object.defineProperty(A.Event.prototype,e,{enumerable:!0,configurable:!0,get:y(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(t){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(e){return e[A.expando]?e:new A.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return Ae.test(t.type)&&t.click&&C(t,"input")&&qe(t,"click",!0),!1},trigger:function(e){var t=this||e;return Ae.test(t.type)&&t.click&&C(t,"input")&&qe(t,"click"),!0},_default:function(e){var t=e.target;return Ae.test(t.type)&&t.click&&C(t,"input")&&ae.get(t,"click")||C(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},A.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},A.Event=function(e,t){if(!(this instanceof A.Event))return new A.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Ie:je,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&A.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[A.expando]=!0},A.Event.prototype={constructor:A.Event,isDefaultPrevented:je,isPropagationStopped:je,isImmediatePropagationStopped:je,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Ie,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Ie,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Ie,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},A.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},A.event.addProp),A.each({focus:"focusin",blur:"focusout"},(function(e,t){function n(e){if(b.documentMode){var n=ae.get(this,"handle"),r=A.event.fix(e);r.type="focusin"===e.type?"focus":"blur",r.isSimulated=!0,n(e),r.target===r.currentTarget&&n(r)}else A.event.simulate(t,e.target,A.event.fix(e))}A.event.special[e]={setup:function(){var r;if(qe(this,e,!0),!b.documentMode)return!1;(r=ae.get(this,t))||this.addEventListener(t,n),ae.set(this,t,(r||0)+1)},trigger:function(){return qe(this,e),!0},teardown:function(){var e;if(!b.documentMode)return!1;(e=ae.get(this,t)-1)?ae.set(this,t,e):(this.removeEventListener(t,n),ae.remove(this,t))},_default:function(t){return ae.get(t.target,e)},delegateType:t},A.event.special[t]={setup:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=ae.get(i,t);o||(b.documentMode?this.addEventListener(t,n):r.addEventListener(e,n,!0)),ae.set(i,t,(o||0)+1)},teardown:function(){var r=this.ownerDocument||this.document||this,i=b.documentMode?this:r,o=ae.get(i,t)-1;o?ae.set(i,t,o):(b.documentMode?this.removeEventListener(t,n):r.removeEventListener(e,n,!0),ae.remove(i,t))}}})),A.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(e,t){A.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=e.relatedTarget,i=e.handleObj;return r&&(r===this||A.contains(this,r))||(e.type=i.origType,n=i.handler.apply(this,arguments),e.type=t),n}}})),A.fn.extend({on:function(e,t,n,r){return Me(this,e,t,n,r)},one:function(e,t,n,r){return Me(this,e,t,n,r,1)},off:function(e,t,n){var r,i;if(e&&e.preventDefault&&e.handleObj)return r=e.handleObj,A(e.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler),this;if("object"==typeof e){for(i in e)this.off(i,t,e[i]);return this}return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=je),this.each((function(){A.event.remove(this,e,n,t)}))}});var Re=/<script|<style|<link/i,Be=/checked\s*(?:[^=]|=\s*.checked.)/i,$e=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function He(e,t){return C(e,"table")&&C(11!==t.nodeType?t:t.firstChild,"tr")&&A(e).children("tbody")[0]||e}function Ue(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Fe(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Ve(e,t){var n,r,i,o,s,a;if(1===t.nodeType){if(ae.hasData(e)&&(a=ae.get(e).events))for(i in ae.remove(t,"handle events"),a)for(n=0,r=a[i].length;n<r;n++)A.event.add(t,i,a[i][n]);le.hasData(e)&&(o=le.access(e),s=A.extend({},o),le.set(t,s))}}function ze(e,t){var n=t.nodeName.toLowerCase();"input"===n&&Ae.test(e.type)?t.checked=e.checked:"input"!==n&&"textarea"!==n||(t.defaultValue=e.defaultValue)}function We(e,t,n,r){t=l(t);var i,o,s,a,c,u,h=0,f=e.length,d=f-1,p=t[0],m=y(p);if(m||f>1&&"string"==typeof p&&!g.checkClone&&Be.test(p))return e.each((function(i){var o=e.eq(i);m&&(t[0]=p.call(this,i,o.html())),We(o,t,n,r)}));if(f&&(o=(i=Le(t,e[0].ownerDocument,!1,e,r)).firstChild,1===i.childNodes.length&&(i=o),o||r)){for(a=(s=A.map(Oe(i,"script"),Ue)).length;h<f;h++)c=i,h!==d&&(c=A.clone(c,!0,!0),a&&A.merge(s,Oe(c,"script"))),n.call(e[h],c,h);if(a)for(u=s[s.length-1].ownerDocument,A.map(s,Fe),h=0;h<a;h++)c=s[h],Ce.test(c.type||"")&&!ae.access(c,"globalEval")&&A.contains(u,c)&&(c.src&&"module"!==(c.type||"").toLowerCase()?A._evalUrl&&!c.noModule&&A._evalUrl(c.src,{nonce:c.nonce||c.getAttribute("nonce")},u):x(c.textContent.replace($e,""),c,u))}return e}function Ge(e,t,n){for(var r,i=t?A.filter(t,e):e,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||A.cleanData(Oe(r)),r.parentNode&&(n&&ge(r)&&Ne(Oe(r,"script")),r.parentNode.removeChild(r));return e}A.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var r,i,o,s,a=e.cloneNode(!0),l=ge(e);if(!(g.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||A.isXMLDoc(e)))for(s=Oe(a),r=0,i=(o=Oe(e)).length;r<i;r++)ze(o[r],s[r]);if(t)if(n)for(o=o||Oe(e),s=s||Oe(a),r=0,i=o.length;r<i;r++)Ve(o[r],s[r]);else Ve(e,a);return(s=Oe(a,"script")).length>0&&Ne(s,!l&&Oe(e,"script")),a},cleanData:function(e){for(var t,n,r,i=A.event.special,o=0;void 0!==(n=e[o]);o++)if(oe(n)){if(t=n[ae.expando]){if(t.events)for(r in t.events)i[r]?A.event.remove(n,r):A.removeEvent(n,r,t.handle);n[ae.expando]=void 0}n[le.expando]&&(n[le.expando]=void 0)}}}),A.fn.extend({detach:function(e){return Ge(this,e,!0)},remove:function(e){return Ge(this,e)},text:function(e){return ee(this,(function(e){return void 0===e?A.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)}))}),null,e,arguments.length)},append:function(){return We(this,arguments,(function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||He(this,e).appendChild(e)}))},prepend:function(){return We(this,arguments,(function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=He(this,e);t.insertBefore(e,t.firstChild)}}))},before:function(){return We(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this)}))},after:function(){return We(this,arguments,(function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)}))},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(A.cleanData(Oe(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map((function(){return A.clone(this,e,t)}))},html:function(e){return ee(this,(function(e){var t=this[0]||{},n=0,r=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Re.test(e)&&!ke[(Se.exec(e)||["",""])[1].toLowerCase()]){e=A.htmlPrefilter(e);try{for(;n<r;n++)1===(t=this[n]||{}).nodeType&&(A.cleanData(Oe(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)}),null,e,arguments.length)},replaceWith:function(){var e=[];return We(this,arguments,(function(t){var n=this.parentNode;A.inArray(this,e)<0&&(A.cleanData(Oe(this)),n&&n.replaceChild(t,this))}),e)}}),A.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(e,t){A.fn[e]=function(e){for(var n,r=[],i=A(e),o=i.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),A(i[s])[t](n),c.apply(r,n.get());return this.pushStack(r)}}));var Ye=new RegExp("^("+fe+")(?!px)[a-z%]+$","i"),Xe=/^--/,Qe=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=r),t.getComputedStyle(e)},Je=function(e,t,n){var r,i,o={};for(i in t)o[i]=e.style[i],e.style[i]=t[i];for(i in r=n.call(e),t)e.style[i]=o[i];return r},Ze=new RegExp(pe.join("|"),"i");function Ke(e,t,n){var r,i,o,s,a=Xe.test(t),l=e.style;return(n=n||Qe(e))&&(s=n.getPropertyValue(t)||n[t],a&&s&&(s=s.replace(L,"$1")||void 0),""!==s||ge(e)||(s=A.style(e,t)),!g.pixelBoxStyles()&&Ye.test(s)&&Ze.test(t)&&(r=l.width,i=l.minWidth,o=l.maxWidth,l.minWidth=l.maxWidth=l.width=s,s=n.width,l.width=r,l.minWidth=i,l.maxWidth=o)),void 0!==s?s+"":s}function et(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(u){c.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",me.appendChild(c).appendChild(u);var e=r.getComputedStyle(u);n="1%"!==e.top,l=12===t(e.marginLeft),u.style.right="60%",s=36===t(e.right),i=36===t(e.width),u.style.position="absolute",o=12===t(u.offsetWidth/3),me.removeChild(c),u=null}}function t(e){return Math.round(parseFloat(e))}var n,i,o,s,a,l,c=b.createElement("div"),u=b.createElement("div");u.style&&(u.style.backgroundClip="content-box",u.cloneNode(!0).style.backgroundClip="",g.clearCloneStyle="content-box"===u.style.backgroundClip,A.extend(g,{boxSizingReliable:function(){return e(),i},pixelBoxStyles:function(){return e(),s},pixelPosition:function(){return e(),n},reliableMarginLeft:function(){return e(),l},scrollboxSize:function(){return e(),o},reliableTrDimensions:function(){var e,t,n,i;return null==a&&(e=b.createElement("table"),t=b.createElement("tr"),n=b.createElement("div"),e.style.cssText="position:absolute;left:-11111px;border-collapse:separate",t.style.cssText="box-sizing:content-box;border:1px solid",t.style.height="1px",n.style.height="9px",n.style.display="block",me.appendChild(e).appendChild(t).appendChild(n),i=r.getComputedStyle(t),a=parseInt(i.height,10)+parseInt(i.borderTopWidth,10)+parseInt(i.borderBottomWidth,10)===t.offsetHeight,me.removeChild(e)),a}}))}();var tt=["Webkit","Moz","ms"],nt=b.createElement("div").style,rt={};function it(e){var t=A.cssProps[e]||rt[e];return t||(e in nt?e:rt[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=tt.length;n--;)if((e=tt[n]+t)in nt)return e}(e)||e)}var ot=/^(none|table(?!-c[ea]).+)/,st={position:"absolute",visibility:"hidden",display:"block"},at={letterSpacing:"0",fontWeight:"400"};function lt(e,t,n){var r=de.exec(t);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):t}function ct(e,t,n,r,i,o){var s="width"===t?1:0,a=0,l=0,c=0;if(n===(r?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(c+=A.css(e,n+pe[s],!0,i)),r?("content"===n&&(l-=A.css(e,"padding"+pe[s],!0,i)),"margin"!==n&&(l-=A.css(e,"border"+pe[s]+"Width",!0,i))):(l+=A.css(e,"padding"+pe[s],!0,i),"padding"!==n?l+=A.css(e,"border"+pe[s]+"Width",!0,i):a+=A.css(e,"border"+pe[s]+"Width",!0,i));return!r&&o>=0&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-l-a-.5))||0),l+c}function ut(e,t,n){var r=Qe(e),i=(!g.boxSizingReliable()||n)&&"border-box"===A.css(e,"boxSizing",!1,r),o=i,s=Ke(e,t,r),a="offset"+t[0].toUpperCase()+t.slice(1);if(Ye.test(s)){if(!n)return s;s="auto"}return(!g.boxSizingReliable()&&i||!g.reliableTrDimensions()&&C(e,"tr")||"auto"===s||!parseFloat(s)&&"inline"===A.css(e,"display",!1,r))&&e.getClientRects().length&&(i="border-box"===A.css(e,"boxSizing",!1,r),(o=a in e)&&(s=e[a])),(s=parseFloat(s)||0)+ct(e,t,n||(i?"border":"content"),o,r,s)+"px"}function ht(e,t,n,r,i){return new ht.prototype.init(e,t,n,r,i)}A.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ke(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,n,r){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var i,o,s,a=ie(t),l=Xe.test(t),c=e.style;if(l||(t=it(a)),s=A.cssHooks[t]||A.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(i=s.get(e,!1,r))?i:c[t];"string"===(o=typeof n)&&(i=de.exec(n))&&i[1]&&(n=be(e,t,i),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=i&&i[3]||(A.cssNumber[a]?"":"px")),g.clearCloneStyle||""!==n||0!==t.indexOf("background")||(c[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,r))||(l?c.setProperty(t,n):c[t]=n))}},css:function(e,t,n,r){var i,o,s,a=ie(t);return Xe.test(t)||(t=it(a)),(s=A.cssHooks[t]||A.cssHooks[a])&&"get"in s&&(i=s.get(e,!0,n)),void 0===i&&(i=Ke(e,t,r)),"normal"===i&&t in at&&(i=at[t]),""===n||n?(o=parseFloat(i),!0===n||isFinite(o)?o||0:i):i}}),A.each(["height","width"],(function(e,t){A.cssHooks[t]={get:function(e,n,r){if(n)return!ot.test(A.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?ut(e,t,r):Je(e,st,(function(){return ut(e,t,r)}))},set:function(e,n,r){var i,o=Qe(e),s=!g.scrollboxSize()&&"absolute"===o.position,a=(s||r)&&"border-box"===A.css(e,"boxSizing",!1,o),l=r?ct(e,t,r,a,o):0;return a&&s&&(l-=Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(o[t])-ct(e,t,"border",!1,o)-.5)),l&&(i=de.exec(n))&&"px"!==(i[3]||"px")&&(e.style[t]=n,n=A.css(e,t)),lt(0,n,l)}}})),A.cssHooks.marginLeft=et(g.reliableMarginLeft,(function(e,t){if(t)return(parseFloat(Ke(e,"marginLeft"))||e.getBoundingClientRect().left-Je(e,{marginLeft:0},(function(){return e.getBoundingClientRect().left})))+"px"})),A.each({margin:"",padding:"",border:"Width"},(function(e,t){A.cssHooks[e+t]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[e+pe[r]+t]=o[r]||o[r-2]||o[0];return i}},"margin"!==e&&(A.cssHooks[e+t].set=lt)})),A.fn.extend({css:function(e,t){return ee(this,(function(e,t,n){var r,i,o={},s=0;if(Array.isArray(t)){for(r=Qe(e),i=t.length;s<i;s++)o[t[s]]=A.css(e,t[s],!1,r);return o}return void 0!==n?A.style(e,t,n):A.css(e,t)}),e,t,arguments.length>1)}}),A.Tween=ht,ht.prototype={constructor:ht,init:function(e,t,n,r,i,o){this.elem=e,this.prop=n,this.easing=i||A.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=o||(A.cssNumber[n]?"":"px")},cur:function(){var e=ht.propHooks[this.prop];return e&&e.get?e.get(this):ht.propHooks._default.get(this)},run:function(e){var t,n=ht.propHooks[this.prop];return this.options.duration?this.pos=t=A.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):ht.propHooks._default.set(this),this}},ht.prototype.init.prototype=ht.prototype,ht.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=A.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){A.fx.step[e.prop]?A.fx.step[e.prop](e):1!==e.elem.nodeType||!A.cssHooks[e.prop]&&null==e.elem.style[it(e.prop)]?e.elem[e.prop]=e.now:A.style(e.elem,e.prop,e.now+e.unit)}}},ht.propHooks.scrollTop=ht.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},A.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},A.fx=ht.prototype.init,A.fx.step={};var ft,dt,pt=/^(?:toggle|show|hide)$/,mt=/queueHooks$/;function gt(){dt&&(!1===b.hidden&&r.requestAnimationFrame?r.requestAnimationFrame(gt):r.setTimeout(gt,A.fx.interval),A.fx.tick())}function yt(){return r.setTimeout((function(){ft=void 0})),ft=Date.now()}function vt(e,t){var n,r=0,i={height:e};for(t=t?1:0;r<4;r+=2-t)i["margin"+(n=pe[r])]=i["padding"+n]=e;return t&&(i.opacity=i.width=e),i}function bt(e,t,n){for(var r,i=(wt.tweeners[t]||[]).concat(wt.tweeners["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,t,e))return r}function wt(e,t,n){var r,i,o=0,s=wt.prefilters.length,a=A.Deferred().always((function(){delete l.elem})),l=function(){if(i)return!1;for(var t=ft||yt(),n=Math.max(0,c.startTime+c.duration-t),r=1-(n/c.duration||0),o=0,s=c.tweens.length;o<s;o++)c.tweens[o].run(r);return a.notifyWith(e,[c,r,n]),r<1&&s?n:(s||a.notifyWith(e,[c,1,0]),a.resolveWith(e,[c]),!1)},c=a.promise({elem:e,props:A.extend({},t),opts:A.extend(!0,{specialEasing:{},easing:A.easing._default},n),originalProperties:t,originalOptions:n,startTime:ft||yt(),duration:n.duration,tweens:[],createTween:function(t,n){var r=A.Tween(e,c.opts,t,n,c.opts.specialEasing[t]||c.opts.easing);return c.tweens.push(r),r},stop:function(t){var n=0,r=t?c.tweens.length:0;if(i)return this;for(i=!0;n<r;n++)c.tweens[n].run(1);return t?(a.notifyWith(e,[c,1,0]),a.resolveWith(e,[c,t])):a.rejectWith(e,[c,t]),this}}),u=c.props;for(!function(e,t){var n,r,i,o,s;for(n in e)if(i=t[r=ie(n)],o=e[n],Array.isArray(o)&&(i=o[1],o=e[n]=o[0]),n!==r&&(e[r]=o,delete e[n]),(s=A.cssHooks[r])&&"expand"in s)for(n in o=s.expand(o),delete e[r],o)n in e||(e[n]=o[n],t[n]=i);else t[r]=i}(u,c.opts.specialEasing);o<s;o++)if(r=wt.prefilters[o].call(c,e,u,c.opts))return y(r.stop)&&(A._queueHooks(c.elem,c.opts.queue).stop=r.stop.bind(r)),r;return A.map(u,bt,c),y(c.opts.start)&&c.opts.start.call(e,c),c.progress(c.opts.progress).done(c.opts.done,c.opts.complete).fail(c.opts.fail).always(c.opts.always),A.fx.timer(A.extend(l,{elem:e,anim:c,queue:c.opts.queue})),c}A.Animation=A.extend(wt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return be(n.elem,e,de.exec(t),n),n}]},tweener:function(e,t){y(e)?(t=e,e=["*"]):e=e.match(G);for(var n,r=0,i=e.length;r<i;r++)n=e[r],wt.tweeners[n]=wt.tweeners[n]||[],wt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var r,i,o,s,a,l,c,u,h="width"in t||"height"in t,f=this,d={},p=e.style,m=e.nodeType&&ve(e),g=ae.get(e,"fxshow");for(r in n.queue||(null==(s=A._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,f.always((function(){f.always((function(){s.unqueued--,A.queue(e,"fx").length||s.empty.fire()}))}))),t)if(i=t[r],pt.test(i)){if(delete t[r],o=o||"toggle"===i,i===(m?"hide":"show")){if("show"!==i||!g||void 0===g[r])continue;m=!0}d[r]=g&&g[r]||A.style(e,r)}if((l=!A.isEmptyObject(t))||!A.isEmptyObject(d))for(r in h&&1===e.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(c=g&&g.display)&&(c=ae.get(e,"display")),"none"===(u=A.css(e,"display"))&&(c?u=c:(_e([e],!0),c=e.style.display||c,u=A.css(e,"display"),_e([e]))),("inline"===u||"inline-block"===u&&null!=c)&&"none"===A.css(e,"float")&&(l||(f.done((function(){p.display=c})),null==c&&(u=p.display,c="none"===u?"":u)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",f.always((function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}))),l=!1,d)l||(g?"hidden"in g&&(m=g.hidden):g=ae.access(e,"fxshow",{display:c}),o&&(g.hidden=!m),m&&_e([e],!0),f.done((function(){for(r in m||_e([e]),ae.remove(e,"fxshow"),d)A.style(e,r,d[r])}))),l=bt(m?g[r]:0,r,f),r in g||(g[r]=l.start,m&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?wt.prefilters.unshift(e):wt.prefilters.push(e)}}),A.speed=function(e,t,n){var r=e&&"object"==typeof e?A.extend({},e):{complete:n||!n&&t||y(e)&&e,duration:e,easing:n&&t||t&&!y(t)&&t};return A.fx.off?r.duration=0:"number"!=typeof r.duration&&(r.duration in A.fx.speeds?r.duration=A.fx.speeds[r.duration]:r.duration=A.fx.speeds._default),null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){y(r.old)&&r.old.call(this),r.queue&&A.dequeue(this,r.queue)},r},A.fn.extend({fadeTo:function(e,t,n,r){return this.filter(ve).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=A.isEmptyObject(e),o=A.speed(t,n,r),s=function(){var t=wt(this,A.extend({},e),o);(i||ae.get(this,"finish"))&&t.stop(!0)};return s.finish=s,i||!1===o.queue?this.each(s):this.queue(o.queue,s)},stop:function(e,t,n){var r=function(e){var t=e.stop;delete e.stop,t(n)};return"string"!=typeof e&&(n=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each((function(){var t=!0,i=null!=e&&e+"queueHooks",o=A.timers,s=ae.get(this);if(i)s[i]&&s[i].stop&&r(s[i]);else for(i in s)s[i]&&s[i].stop&&mt.test(i)&&r(s[i]);for(i=o.length;i--;)o[i].elem!==this||null!=e&&o[i].queue!==e||(o[i].anim.stop(n),t=!1,o.splice(i,1));!t&&n||A.dequeue(this,e)}))},finish:function(e){return!1!==e&&(e=e||"fx"),this.each((function(){var t,n=ae.get(this),r=n[e+"queue"],i=n[e+"queueHooks"],o=A.timers,s=r?r.length:0;for(n.finish=!0,A.queue(this,e,[]),i&&i.stop&&i.stop.call(this,!0),t=o.length;t--;)o[t].elem===this&&o[t].queue===e&&(o[t].anim.stop(!0),o.splice(t,1));for(t=0;t<s;t++)r[t]&&r[t].finish&&r[t].finish.call(this);delete n.finish}))}}),A.each(["toggle","show","hide"],(function(e,t){var n=A.fn[t];A.fn[t]=function(e,r,i){return null==e||"boolean"==typeof e?n.apply(this,arguments):this.animate(vt(t,!0),e,r,i)}})),A.each({slideDown:vt("show"),slideUp:vt("hide"),slideToggle:vt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(e,t){A.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}})),A.timers=[],A.fx.tick=function(){var e,t=0,n=A.timers;for(ft=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||A.fx.stop(),ft=void 0},A.fx.timer=function(e){A.timers.push(e),A.fx.start()},A.fx.interval=13,A.fx.start=function(){dt||(dt=!0,gt())},A.fx.stop=function(){dt=null},A.fx.speeds={slow:600,fast:200,_default:400},A.fn.delay=function(e,t){return e=A.fx&&A.fx.speeds[e]||e,t=t||"fx",this.queue(t,(function(t,n){var i=r.setTimeout(t,e);n.stop=function(){r.clearTimeout(i)}}))},function(){var e=b.createElement("input"),t=b.createElement("select").appendChild(b.createElement("option"));e.type="checkbox",g.checkOn=""!==e.value,g.optSelected=t.selected,(e=b.createElement("input")).value="t",e.type="radio",g.radioValue="t"===e.value}();var xt,_t=A.expr.attrHandle;A.fn.extend({attr:function(e,t){return ee(this,A.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each((function(){A.removeAttr(this,e)}))}}),A.extend({attr:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?A.prop(e,t,n):(1===o&&A.isXMLDoc(e)||(i=A.attrHooks[t.toLowerCase()]||(A.expr.match.bool.test(t)?xt:void 0)),void 0!==n?null===n?void A.removeAttr(e,t):i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:(e.setAttribute(t,n+""),n):i&&"get"in i&&null!==(r=i.get(e,t))?r:null==(r=A.find.attr(e,t))?void 0:r)},attrHooks:{type:{set:function(e,t){if(!g.radioValue&&"radio"===t&&C(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,r=0,i=t&&t.match(G);if(i&&1===e.nodeType)for(;n=i[r++];)e.removeAttribute(n)}}),xt={set:function(e,t,n){return!1===t?A.removeAttr(e,n):e.setAttribute(n,n),n}},A.each(A.expr.match.bool.source.match(/\w+/g),(function(e,t){var n=_t[t]||A.find.attr;_t[t]=function(e,t,r){var i,o,s=t.toLowerCase();return r||(o=_t[s],_t[s]=i,i=null!=n(e,t,r)?s:null,_t[s]=o),i}}));var Tt=/^(?:input|select|textarea|button)$/i,Et=/^(?:a|area)$/i;function At(e){return(e.match(G)||[]).join(" ")}function St(e){return e.getAttribute&&e.getAttribute("class")||""}function Ct(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(G)||[]}A.fn.extend({prop:function(e,t){return ee(this,A.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each((function(){delete this[A.propFix[e]||e]}))}}),A.extend({prop:function(e,t,n){var r,i,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&A.isXMLDoc(e)||(t=A.propFix[t]||t,i=A.propHooks[t]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(e,n,t))?r:e[t]=n:i&&"get"in i&&null!==(r=i.get(e,t))?r:e[t]},propHooks:{tabIndex:{get:function(e){var t=A.find.attr(e,"tabindex");return t?parseInt(t,10):Tt.test(e.nodeName)||Et.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),g.optSelected||(A.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),A.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){A.propFix[this.toLowerCase()]=this})),A.fn.extend({addClass:function(e){var t,n,r,i,o,s;return y(e)?this.each((function(t){A(this).addClass(e.call(this,t,St(this)))})):(t=Ct(e)).length?this.each((function(){if(r=St(this),n=1===this.nodeType&&" "+At(r)+" "){for(o=0;o<t.length;o++)i=t[o],n.indexOf(" "+i+" ")<0&&(n+=i+" ");s=At(n),r!==s&&this.setAttribute("class",s)}})):this},removeClass:function(e){var t,n,r,i,o,s;return y(e)?this.each((function(t){A(this).removeClass(e.call(this,t,St(this)))})):arguments.length?(t=Ct(e)).length?this.each((function(){if(r=St(this),n=1===this.nodeType&&" "+At(r)+" "){for(o=0;o<t.length;o++)for(i=t[o];n.indexOf(" "+i+" ")>-1;)n=n.replace(" "+i+" "," ");s=At(n),r!==s&&this.setAttribute("class",s)}})):this:this.attr("class","")},toggleClass:function(e,t){var n,r,i,o,s=typeof e,a="string"===s||Array.isArray(e);return y(e)?this.each((function(n){A(this).toggleClass(e.call(this,n,St(this),t),t)})):"boolean"==typeof t&&a?t?this.addClass(e):this.removeClass(e):(n=Ct(e),this.each((function(){if(a)for(o=A(this),i=0;i<n.length;i++)r=n[i],o.hasClass(r)?o.removeClass(r):o.addClass(r);else void 0!==e&&"boolean"!==s||((r=St(this))&&ae.set(this,"__className__",r),this.setAttribute&&this.setAttribute("class",r||!1===e?"":ae.get(this,"__className__")||""))})))},hasClass:function(e){var t,n,r=0;for(t=" "+e+" ";n=this[r++];)if(1===n.nodeType&&(" "+At(St(n))+" ").indexOf(t)>-1)return!0;return!1}});var kt=/\r/g;A.fn.extend({val:function(e){var t,n,r,i=this[0];return arguments.length?(r=y(e),this.each((function(n){var i;1===this.nodeType&&(null==(i=r?e.call(this,n,A(this).val()):e)?i="":"number"==typeof i?i+="":Array.isArray(i)&&(i=A.map(i,(function(e){return null==e?"":e+""}))),(t=A.valHooks[this.type]||A.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,i,"value")||(this.value=i))}))):i?(t=A.valHooks[i.type]||A.valHooks[i.nodeName.toLowerCase()])&&"get"in t&&void 0!==(n=t.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(kt,""):null==n?"":n:void 0}}),A.extend({valHooks:{option:{get:function(e){var t=A.find.attr(e,"value");return null!=t?t:At(A.text(e))}},select:{get:function(e){var t,n,r,i=e.options,o=e.selectedIndex,s="select-one"===e.type,a=s?null:[],l=s?o+1:i.length;for(r=o<0?l:s?o:0;r<l;r++)if(((n=i[r]).selected||r===o)&&!n.disabled&&(!n.parentNode.disabled||!C(n.parentNode,"optgroup"))){if(t=A(n).val(),s)return t;a.push(t)}return a},set:function(e,t){for(var n,r,i=e.options,o=A.makeArray(t),s=i.length;s--;)((r=i[s]).selected=A.inArray(A.valHooks.option.get(r),o)>-1)&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),A.each(["radio","checkbox"],(function(){A.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=A.inArray(A(e).val(),t)>-1}},g.checkOn||(A.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}));var Ot=r.location,Nt={guid:Date.now()},Dt=/\?/;A.parseXML=function(e){var t,n;if(!e||"string"!=typeof e)return null;try{t=(new r.DOMParser).parseFromString(e,"text/xml")}catch(e){}return n=t&&t.getElementsByTagName("parsererror")[0],t&&!n||A.error("Invalid XML: "+(n?A.map(n.childNodes,(function(e){return e.textContent})).join("\n"):e)),t};var Lt=/^(?:focusinfocus|focusoutblur)$/,Pt=function(e){e.stopPropagation()};A.extend(A.event,{trigger:function(e,t,n,i){var o,s,a,l,c,u,h,f,p=[n||b],m=d.call(e,"type")?e.type:e,g=d.call(e,"namespace")?e.namespace.split("."):[];if(s=f=a=n=n||b,3!==n.nodeType&&8!==n.nodeType&&!Lt.test(m+A.event.triggered)&&(m.indexOf(".")>-1&&(g=m.split("."),m=g.shift(),g.sort()),c=m.indexOf(":")<0&&"on"+m,(e=e[A.expando]?e:new A.Event(m,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=g.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+g.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:A.makeArray(t,[e]),h=A.event.special[m]||{},i||!h.trigger||!1!==h.trigger.apply(n,t))){if(!i&&!h.noBubble&&!v(n)){for(l=h.delegateType||m,Lt.test(l+m)||(s=s.parentNode);s;s=s.parentNode)p.push(s),a=s;a===(n.ownerDocument||b)&&p.push(a.defaultView||a.parentWindow||r)}for(o=0;(s=p[o++])&&!e.isPropagationStopped();)f=s,e.type=o>1?l:h.bindType||m,(u=(ae.get(s,"events")||Object.create(null))[e.type]&&ae.get(s,"handle"))&&u.apply(s,t),(u=c&&s[c])&&u.apply&&oe(s)&&(e.result=u.apply(s,t),!1===e.result&&e.preventDefault());return e.type=m,i||e.isDefaultPrevented()||h._default&&!1!==h._default.apply(p.pop(),t)||!oe(n)||c&&y(n[m])&&!v(n)&&((a=n[c])&&(n[c]=null),A.event.triggered=m,e.isPropagationStopped()&&f.addEventListener(m,Pt),n[m](),e.isPropagationStopped()&&f.removeEventListener(m,Pt),A.event.triggered=void 0,a&&(n[c]=a)),e.result}},simulate:function(e,t,n){var r=A.extend(new A.Event,n,{type:e,isSimulated:!0});A.event.trigger(r,null,t)}}),A.fn.extend({trigger:function(e,t){return this.each((function(){A.event.trigger(e,t,this)}))},triggerHandler:function(e,t){var n=this[0];if(n)return A.event.trigger(e,t,n,!0)}});var It=/\[\]$/,jt=/\r?\n/g,Mt=/^(?:submit|button|image|reset|file)$/i,qt=/^(?:input|select|textarea|keygen)/i;function Rt(e,t,n,r){var i;if(Array.isArray(t))A.each(t,(function(t,i){n||It.test(e)?r(e,i):Rt(e+"["+("object"==typeof i&&null!=i?t:"")+"]",i,n,r)}));else if(n||"object"!==_(t))r(e,t);else for(i in t)Rt(e+"["+i+"]",t[i],n,r)}A.param=function(e,t){var n,r=[],i=function(e,t){var n=y(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!A.isPlainObject(e))A.each(e,(function(){i(this.name,this.value)}));else for(n in e)Rt(n,e[n],t,i);return r.join("&")},A.fn.extend({serialize:function(){return A.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var e=A.prop(this,"elements");return e?A.makeArray(e):this})).filter((function(){var e=this.type;return this.name&&!A(this).is(":disabled")&&qt.test(this.nodeName)&&!Mt.test(e)&&(this.checked||!Ae.test(e))})).map((function(e,t){var n=A(this).val();return null==n?null:Array.isArray(n)?A.map(n,(function(e){return{name:t.name,value:e.replace(jt,"\r\n")}})):{name:t.name,value:n.replace(jt,"\r\n")}})).get()}});var Bt=/%20/g,$t=/#.*$/,Ht=/([?&])_=[^&]*/,Ut=/^(.*?):[ \t]*([^\r\n]*)$/gm,Ft=/^(?:GET|HEAD)$/,Vt=/^\/\//,zt={},Wt={},Gt="*/".concat("*"),Yt=b.createElement("a");function Xt(e){return function(t,n){"string"!=typeof t&&(n=t,t="*");var r,i=0,o=t.toLowerCase().match(G)||[];if(y(n))for(;r=o[i++];)"+"===r[0]?(r=r.slice(1)||"*",(e[r]=e[r]||[]).unshift(n)):(e[r]=e[r]||[]).push(n)}}function Qt(e,t,n,r){var i={},o=e===Wt;function s(a){var l;return i[a]=!0,A.each(e[a]||[],(function(e,a){var c=a(t,n,r);return"string"!=typeof c||o||i[c]?o?!(l=c):void 0:(t.dataTypes.unshift(c),s(c),!1)})),l}return s(t.dataTypes[0])||!i["*"]&&s("*")}function Jt(e,t){var n,r,i=A.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((i[n]?e:r||(r={}))[n]=t[n]);return r&&A.extend(!0,e,r),e}Yt.href=Ot.href,A.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ot.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Ot.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Gt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":A.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Jt(Jt(e,A.ajaxSettings),t):Jt(A.ajaxSettings,e)},ajaxPrefilter:Xt(zt),ajaxTransport:Xt(Wt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var n,i,o,s,a,l,c,u,h,f,d=A.ajaxSetup({},t),p=d.context||d,m=d.context&&(p.nodeType||p.jquery)?A(p):A.event,g=A.Deferred(),y=A.Callbacks("once memory"),v=d.statusCode||{},w={},x={},_="canceled",T={readyState:0,getResponseHeader:function(e){var t;if(c){if(!s)for(s={};t=Ut.exec(o);)s[t[1].toLowerCase()+" "]=(s[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=s[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return c?o:null},setRequestHeader:function(e,t){return null==c&&(e=x[e.toLowerCase()]=x[e.toLowerCase()]||e,w[e]=t),this},overrideMimeType:function(e){return null==c&&(d.mimeType=e),this},statusCode:function(e){var t;if(e)if(c)T.always(e[T.status]);else for(t in e)v[t]=[v[t],e[t]];return this},abort:function(e){var t=e||_;return n&&n.abort(t),E(0,t),this}};if(g.promise(T),d.url=((e||d.url||Ot.href)+"").replace(Vt,Ot.protocol+"//"),d.type=t.method||t.type||d.method||d.type,d.dataTypes=(d.dataType||"*").toLowerCase().match(G)||[""],null==d.crossDomain){l=b.createElement("a");try{l.href=d.url,l.href=l.href,d.crossDomain=Yt.protocol+"//"+Yt.host!=l.protocol+"//"+l.host}catch(e){d.crossDomain=!0}}if(d.data&&d.processData&&"string"!=typeof d.data&&(d.data=A.param(d.data,d.traditional)),Qt(zt,d,t,T),c)return T;for(h in(u=A.event&&d.global)&&0==A.active++&&A.event.trigger("ajaxStart"),d.type=d.type.toUpperCase(),d.hasContent=!Ft.test(d.type),i=d.url.replace($t,""),d.hasContent?d.data&&d.processData&&0===(d.contentType||"").indexOf("application/x-www-form-urlencoded")&&(d.data=d.data.replace(Bt,"+")):(f=d.url.slice(i.length),d.data&&(d.processData||"string"==typeof d.data)&&(i+=(Dt.test(i)?"&":"?")+d.data,delete d.data),!1===d.cache&&(i=i.replace(Ht,"$1"),f=(Dt.test(i)?"&":"?")+"_="+Nt.guid+++f),d.url=i+f),d.ifModified&&(A.lastModified[i]&&T.setRequestHeader("If-Modified-Since",A.lastModified[i]),A.etag[i]&&T.setRequestHeader("If-None-Match",A.etag[i])),(d.data&&d.hasContent&&!1!==d.contentType||t.contentType)&&T.setRequestHeader("Content-Type",d.contentType),T.setRequestHeader("Accept",d.dataTypes[0]&&d.accepts[d.dataTypes[0]]?d.accepts[d.dataTypes[0]]+("*"!==d.dataTypes[0]?", "+Gt+"; q=0.01":""):d.accepts["*"]),d.headers)T.setRequestHeader(h,d.headers[h]);if(d.beforeSend&&(!1===d.beforeSend.call(p,T,d)||c))return T.abort();if(_="abort",y.add(d.complete),T.done(d.success),T.fail(d.error),n=Qt(Wt,d,t,T)){if(T.readyState=1,u&&m.trigger("ajaxSend",[T,d]),c)return T;d.async&&d.timeout>0&&(a=r.setTimeout((function(){T.abort("timeout")}),d.timeout));try{c=!1,n.send(w,E)}catch(e){if(c)throw e;E(-1,e)}}else E(-1,"No Transport");function E(e,t,s,l){var h,f,b,w,x,_=t;c||(c=!0,a&&r.clearTimeout(a),n=void 0,o=l||"",T.readyState=e>0?4:0,h=e>=200&&e<300||304===e,s&&(w=function(e,t,n){for(var r,i,o,s,a=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===r&&(r=e.mimeType||t.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){l.unshift(i);break}if(l[0]in n)o=l[0];else{for(i in n){if(!l[0]||e.converters[i+" "+l[0]]){o=i;break}s||(s=i)}o=o||s}if(o)return o!==l[0]&&l.unshift(o),n[o]}(d,T,s)),!h&&A.inArray("script",d.dataTypes)>-1&&A.inArray("json",d.dataTypes)<0&&(d.converters["text script"]=function(){}),w=function(e,t,n,r){var i,o,s,a,l,c={},u=e.dataTypes.slice();if(u[1])for(s in e.converters)c[s.toLowerCase()]=e.converters[s];for(o=u.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&r&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=u.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(s=c[l+" "+o]||c["* "+o]))for(i in c)if((a=i.split(" "))[1]===o&&(s=c[l+" "+a[0]]||c["* "+a[0]])){!0===s?s=c[i]:!0!==c[i]&&(o=a[0],u.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(d,w,T,h),h?(d.ifModified&&((x=T.getResponseHeader("Last-Modified"))&&(A.lastModified[i]=x),(x=T.getResponseHeader("etag"))&&(A.etag[i]=x)),204===e||"HEAD"===d.type?_="nocontent":304===e?_="notmodified":(_=w.state,f=w.data,h=!(b=w.error))):(b=_,!e&&_||(_="error",e<0&&(e=0))),T.status=e,T.statusText=(t||_)+"",h?g.resolveWith(p,[f,_,T]):g.rejectWith(p,[T,_,b]),T.statusCode(v),v=void 0,u&&m.trigger(h?"ajaxSuccess":"ajaxError",[T,d,h?f:b]),y.fireWith(p,[T,_]),u&&(m.trigger("ajaxComplete",[T,d]),--A.active||A.event.trigger("ajaxStop")))}return T},getJSON:function(e,t,n){return A.get(e,t,n,"json")},getScript:function(e,t){return A.get(e,void 0,t,"script")}}),A.each(["get","post"],(function(e,t){A[t]=function(e,n,r,i){return y(n)&&(i=i||r,r=n,n=void 0),A.ajax(A.extend({url:e,type:t,dataType:i,data:n,success:r},A.isPlainObject(e)&&e))}})),A.ajaxPrefilter((function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")})),A._evalUrl=function(e,t,n){return A.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){A.globalEval(e,t,n)}})},A.fn.extend({wrapAll:function(e){var t;return this[0]&&(y(e)&&(e=e.call(this[0])),t=A(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map((function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e})).append(this)),this},wrapInner:function(e){return y(e)?this.each((function(t){A(this).wrapInner(e.call(this,t))})):this.each((function(){var t=A(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)}))},wrap:function(e){var t=y(e);return this.each((function(n){A(this).wrapAll(t?e.call(this,n):e)}))},unwrap:function(e){return this.parent(e).not("body").each((function(){A(this).replaceWith(this.childNodes)})),this}}),A.expr.pseudos.hidden=function(e){return!A.expr.pseudos.visible(e)},A.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},A.ajaxSettings.xhr=function(){try{return new r.XMLHttpRequest}catch(e){}};var Zt={0:200,1223:204},Kt=A.ajaxSettings.xhr();g.cors=!!Kt&&"withCredentials"in Kt,g.ajax=Kt=!!Kt,A.ajaxTransport((function(e){var t,n;if(g.cors||Kt&&!e.crossDomain)return{send:function(i,o){var s,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)a[s]=e.xhrFields[s];for(s in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)a.setRequestHeader(s,i[s]);t=function(e){return function(){t&&(t=n=a.onload=a.onerror=a.onabort=a.ontimeout=a.onreadystatechange=null,"abort"===e?a.abort():"error"===e?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Zt[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=t(),n=a.onerror=a.ontimeout=t("error"),void 0!==a.onabort?a.onabort=n:a.onreadystatechange=function(){4===a.readyState&&r.setTimeout((function(){t&&n()}))},t=t("abort");try{a.send(e.hasContent&&e.data||null)}catch(e){if(t)throw e}},abort:function(){t&&t()}}})),A.ajaxPrefilter((function(e){e.crossDomain&&(e.contents.script=!1)})),A.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return A.globalEval(e),e}}}),A.ajaxPrefilter("script",(function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")})),A.ajaxTransport("script",(function(e){var t,n;if(e.crossDomain||e.scriptAttrs)return{send:function(r,i){t=A("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",n=function(e){t.remove(),n=null,e&&i("error"===e.type?404:200,e.type)}),b.head.appendChild(t[0])},abort:function(){n&&n()}}}));var en,tn=[],nn=/(=)\?(?=&|$)|\?\?/;A.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=tn.pop()||A.expando+"_"+Nt.guid++;return this[e]=!0,e}}),A.ajaxPrefilter("json jsonp",(function(e,t,n){var i,o,s,a=!1!==e.jsonp&&(nn.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&nn.test(e.data)&&"data");if(a||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=y(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(nn,"$1"+i):!1!==e.jsonp&&(e.url+=(Dt.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return s||A.error(i+" was not called"),s[0]},e.dataTypes[0]="json",o=r[i],r[i]=function(){s=arguments},n.always((function(){void 0===o?A(r).removeProp(i):r[i]=o,e[i]&&(e.jsonpCallback=t.jsonpCallback,tn.push(i)),s&&y(o)&&o(s[0]),s=o=void 0})),"script"})),g.createHTMLDocument=((en=b.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===en.childNodes.length),A.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(g.createHTMLDocument?((r=(t=b.implementation.createHTMLDocument("")).createElement("base")).href=b.location.href,t.head.appendChild(r)):t=b),o=!n&&[],(i=$.exec(e))?[t.createElement(i[1])]:(i=Le([e],t,o),o&&o.length&&A(o).remove(),A.merge([],i.childNodes)));var r,i,o},A.fn.load=function(e,t,n){var r,i,o,s=this,a=e.indexOf(" ");return a>-1&&(r=At(e.slice(a)),e=e.slice(0,a)),y(t)?(n=t,t=void 0):t&&"object"==typeof t&&(i="POST"),s.length>0&&A.ajax({url:e,type:i||"GET",dataType:"html",data:t}).done((function(e){o=arguments,s.html(r?A("<div>").append(A.parseHTML(e)).find(r):e)})).always(n&&function(e,t){s.each((function(){n.apply(this,o||[e.responseText,t,e])}))}),this},A.expr.pseudos.animated=function(e){return A.grep(A.timers,(function(t){return e===t.elem})).length},A.offset={setOffset:function(e,t,n){var r,i,o,s,a,l,c=A.css(e,"position"),u=A(e),h={};"static"===c&&(e.style.position="relative"),a=u.offset(),o=A.css(e,"top"),l=A.css(e,"left"),("absolute"===c||"fixed"===c)&&(o+l).indexOf("auto")>-1?(s=(r=u.position()).top,i=r.left):(s=parseFloat(o)||0,i=parseFloat(l)||0),y(t)&&(t=t.call(e,n,A.extend({},a))),null!=t.top&&(h.top=t.top-a.top+s),null!=t.left&&(h.left=t.left-a.left+i),"using"in t?t.using.call(e,h):u.css(h)}},A.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each((function(t){A.offset.setOffset(this,e,t)}));var t,n,r=this[0];return r?r.getClientRects().length?(t=r.getBoundingClientRect(),n=r.ownerDocument.defaultView,{top:t.top+n.pageYOffset,left:t.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,r=this[0],i={top:0,left:0};if("fixed"===A.css(r,"position"))t=r.getBoundingClientRect();else{for(t=this.offset(),n=r.ownerDocument,e=r.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===A.css(e,"position");)e=e.parentNode;e&&e!==r&&1===e.nodeType&&((i=A(e).offset()).top+=A.css(e,"borderTopWidth",!0),i.left+=A.css(e,"borderLeftWidth",!0))}return{top:t.top-i.top-A.css(r,"marginTop",!0),left:t.left-i.left-A.css(r,"marginLeft",!0)}}},offsetParent:function(){return this.map((function(){for(var e=this.offsetParent;e&&"static"===A.css(e,"position");)e=e.offsetParent;return e||me}))}}),A.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(e,t){var n="pageYOffset"===t;A.fn[e]=function(r){return ee(this,(function(e,r,i){var o;if(v(e)?o=e:9===e.nodeType&&(o=e.defaultView),void 0===i)return o?o[t]:e[r];o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):e[r]=i}),e,r,arguments.length)}})),A.each(["top","left"],(function(e,t){A.cssHooks[t]=et(g.pixelPosition,(function(e,n){if(n)return n=Ke(e,t),Ye.test(n)?A(e).position()[t]+"px":n}))})),A.each({Height:"height",Width:"width"},(function(e,t){A.each({padding:"inner"+e,content:t,"":"outer"+e},(function(n,r){A.fn[r]=function(i,o){var s=arguments.length&&(n||"boolean"!=typeof i),a=n||(!0===i||!0===o?"margin":"border");return ee(this,(function(t,n,i){var o;return v(t)?0===r.indexOf("outer")?t["inner"+e]:t.document.documentElement["client"+e]:9===t.nodeType?(o=t.documentElement,Math.max(t.body["scroll"+e],o["scroll"+e],t.body["offset"+e],o["offset"+e],o["client"+e])):void 0===i?A.css(t,n,a):A.style(t,n,i,a)}),t,s?i:void 0,s)}}))})),A.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(e,t){A.fn[t]=function(e){return this.on(t,e)}})),A.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),A.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),(function(e,t){A.fn[t]=function(e,n){return arguments.length>0?this.on(t,null,e,n):this.trigger(t)}}));var rn=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;A.proxy=function(e,t){var n,r,i;if("string"==typeof t&&(n=e[t],t=e,e=n),y(e))return r=a.call(arguments,2),i=function(){return e.apply(t||this,r.concat(a.call(arguments)))},i.guid=e.guid=e.guid||A.guid++,i},A.holdReady=function(e){e?A.readyWait++:A.ready(!0)},A.isArray=Array.isArray,A.parseJSON=JSON.parse,A.nodeName=C,A.isFunction=y,A.isWindow=v,A.camelCase=ie,A.type=_,A.now=Date.now,A.isNumeric=function(e){var t=A.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},A.trim=function(e){return null==e?"":(e+"").replace(rn,"$1")},void 0===(n=function(){return A}.apply(t,[]))||(e.exports=n);var on=r.jQuery,sn=r.$;return A.noConflict=function(e){return r.$===A&&(r.$=sn),e&&r.jQuery===A&&(r.jQuery=on),A},void 0===i&&(r.jQuery=r.$=A),A}))},4728:(e,t,n)=>{const r=n(6808),i=n(2834),{isPlainObject:o}=n(8682),s=n(4744),a=n(9466),{parse:l}=n(2895),c=["img","audio","video","picture","svg","object","map","iframe","embed"],u=["script","style"];function h(e,t){e&&Object.keys(e).forEach((function(n){t(e[n],n)}))}function f(e,t){return{}.hasOwnProperty.call(e,t)}function d(e,t){const n=[];return h(e,(function(e){t(e)&&n.push(e)})),n}e.exports=m;const p=/^[^\0\t\n\f\r /<=>]+$/;function m(e,t,n){if(null==e)return"";"number"==typeof e&&(e=e.toString());let y="",v="";function b(e,t){const n=this;this.tag=e,this.attribs=t||{},this.tagPosition=y.length,this.text="",this.mediaChildren=[],this.updateParentNodeText=function(){if(N.length){N[N.length-1].text+=n.text}},this.updateParentNodeMediaChildren=function(){if(N.length&&c.includes(this.tag)){N[N.length-1].mediaChildren.push(this.tag)}}}(t=Object.assign({},m.defaults,t)).parser=Object.assign({},g,t.parser);const w=function(e){return!1===t.allowedTags||(t.allowedTags||[]).indexOf(e)>-1};u.forEach((function(e){w(e)&&!t.allowVulnerableTags&&console.warn(`\n\n⚠️ Your \`allowedTags\` option includes, \`${e}\`, which is inherently\nvulnerable to XSS attacks. Please remove it from \`allowedTags\`.\nOr, to disable this warning, add the \`allowVulnerableTags\` option\nand ensure you are accounting for this risk.\n\n`)}));const x=t.nonTextTags||["script","style","textarea","option"];let _,T;t.allowedAttributes&&(_={},T={},h(t.allowedAttributes,(function(e,t){_[t]=[];const n=[];e.forEach((function(e){"string"==typeof e&&e.indexOf("*")>=0?n.push(i(e).replace(/\\\*/g,".*")):_[t].push(e)})),n.length&&(T[t]=new RegExp("^("+n.join("|")+")$"))})));const E={},A={},S={};h(t.allowedClasses,(function(e,t){if(_&&(f(_,t)||(_[t]=[]),_[t].push("class")),E[t]=e,Array.isArray(e)){const n=[];E[t]=[],S[t]=[],e.forEach((function(e){"string"==typeof e&&e.indexOf("*")>=0?n.push(i(e).replace(/\\\*/g,".*")):e instanceof RegExp?S[t].push(e):E[t].push(e)})),n.length&&(A[t]=new RegExp("^("+n.join("|")+")$"))}}));const C={};let k,O,N,D,L,P,I;h(t.transformTags,(function(e,t){let n;"function"==typeof e?n=e:"string"==typeof e&&(n=m.simpleTransform(e)),"*"===t?k=n:C[t]=n}));let j=!1;q();const M=new r.Parser({onopentag:function(e,n){if(t.enforceHtmlBoundary&&"html"===e&&q(),P)return void I++;const r=new b(e,n);N.push(r);let i=!1;const c=!!r.text;let u;if(f(C,e)&&(u=C[e](e,n),r.attribs=n=u.attribs,void 0!==u.text&&(r.innerText=u.text),e!==u.tagName&&(r.name=e=u.tagName,L[O]=u.tagName)),k&&(u=k(e,n),r.attribs=n=u.attribs,e!==u.tagName&&(r.name=e=u.tagName,L[O]=u.tagName)),(!w(e)||"recursiveEscape"===t.disallowedTagsMode&&!function(e){for(const t in e)if(f(e,t))return!1;return!0}(D)||null!=t.nestingLimit&&O>=t.nestingLimit)&&(i=!0,D[O]=!0,"discard"!==t.disallowedTagsMode&&"completelyDiscard"!==t.disallowedTagsMode||-1!==x.indexOf(e)&&(P=!0,I=1),D[O]=!0),O++,i){if("discard"===t.disallowedTagsMode||"completelyDiscard"===t.disallowedTagsMode){if(r.innerText&&!c){const n=R(r.innerText);t.textFilter?y+=t.textFilter(n,e):y+=R(r.innerText),j=!0}return}v=y,y=""}y+="<"+e,"script"===e&&(t.allowedScriptHostnames||t.allowedScriptDomains)&&(r.innerText=""),(!_||f(_,e)||_["*"])&&h(n,(function(n,i){if(!p.test(i))return void delete r.attribs[i];if(""===n&&!t.allowedEmptyAttributes.includes(i)&&(t.nonBooleanAttributes.includes(i)||t.nonBooleanAttributes.includes("*")))return void delete r.attribs[i];let c=!1;if(!_||f(_,e)&&-1!==_[e].indexOf(i)||_["*"]&&-1!==_["*"].indexOf(i)||f(T,e)&&T[e].test(i)||T["*"]&&T["*"].test(i))c=!0;else if(_&&_[e])for(const t of _[e])if(o(t)&&t.name&&t.name===i){c=!0;let e="";if(!0===t.multiple){const r=n.split(" ");for(const n of r)-1!==t.values.indexOf(n)&&(""===e?e=n:e+=" "+n)}else t.values.indexOf(n)>=0&&(e=n);n=e}if(c){if(-1!==t.allowedSchemesAppliedToAttributes.indexOf(i)&&B(e,n))return void delete r.attribs[i];if("script"===e&&"src"===i){let e=!0;try{const r=$(n);if(t.allowedScriptHostnames||t.allowedScriptDomains){const n=(t.allowedScriptHostnames||[]).find((function(e){return e===r.url.hostname})),i=(t.allowedScriptDomains||[]).find((function(e){return r.url.hostname===e||r.url.hostname.endsWith(`.${e}`)}));e=n||i}}catch(t){e=!1}if(!e)return void delete r.attribs[i]}if("iframe"===e&&"src"===i){let e=!0;try{const r=$(n);if(r.isRelativeUrl)e=f(t,"allowIframeRelativeUrls")?t.allowIframeRelativeUrls:!t.allowedIframeHostnames&&!t.allowedIframeDomains;else if(t.allowedIframeHostnames||t.allowedIframeDomains){const n=(t.allowedIframeHostnames||[]).find((function(e){return e===r.url.hostname})),i=(t.allowedIframeDomains||[]).find((function(e){return r.url.hostname===e||r.url.hostname.endsWith(`.${e}`)}));e=n||i}}catch(t){e=!1}if(!e)return void delete r.attribs[i]}if("srcset"===i)try{let e=a(n);if(e.forEach((function(e){B("srcset",e.url)&&(e.evil=!0)})),e=d(e,(function(e){return!e.evil})),!e.length)return void delete r.attribs[i];n=d(e,(function(e){return!e.evil})).map((function(e){if(!e.url)throw new Error("URL missing");return e.url+(e.w?` ${e.w}w`:"")+(e.h?` ${e.h}h`:"")+(e.d?` ${e.d}x`:"")})).join(", "),r.attribs[i]=n}catch(e){return void delete r.attribs[i]}if("class"===i){const t=E[e],o=E["*"],a=A[e],l=S[e],c=S["*"],u=[a,A["*"]].concat(l,c).filter((function(e){return e}));if(!(n=H(n,t&&o?s(t,o):t||o,u)).length)return void delete r.attribs[i]}if("style"===i)if(t.parseStyleAttributes)try{const o=function(e,t){if(!t)return e;const n=e.nodes[0];let r;r=t[n.selector]&&t["*"]?s(t[n.selector],t["*"]):t[n.selector]||t["*"];r&&(e.nodes[0].nodes=n.nodes.reduce(function(e){return function(t,n){if(f(e,n.prop)){e[n.prop].some((function(e){return e.test(n.value)}))&&t.push(n)}return t}}(r),[]));return e}(l(e+" {"+n+"}",{map:!1}),t.allowedStyles);if(n=function(e){return e.nodes[0].nodes.reduce((function(e,t){return e.push(`${t.prop}:${t.value}${t.important?" !important":""}`),e}),[]).join(";")}(o),0===n.length)return void delete r.attribs[i]}catch(t){return"undefined"!=typeof window&&console.warn('Failed to parse "'+e+" {"+n+"}\", If you're running this in a browser, we recommend to disable style parsing: options.parseStyleAttributes: false, since this only works in a node environment due to a postcss dependency, More info: https://github.com/apostrophecms/sanitize-html/issues/547"),void delete r.attribs[i]}else if(t.allowedStyles)throw new Error("allowedStyles option cannot be used together with parseStyleAttributes: false.");y+=" "+i,n&&n.length?y+='="'+R(n,!0)+'"':t.allowedEmptyAttributes.includes(i)&&(y+='=""')}else delete r.attribs[i]})),-1!==t.selfClosing.indexOf(e)?y+=" />":(y+=">",!r.innerText||c||t.textFilter||(y+=R(r.innerText),j=!0)),i&&(y=v+R(y),v="")},ontext:function(e){if(P)return;const n=N[N.length-1];let r;if(n&&(r=n.tag,e=void 0!==n.innerText?n.innerText:e),"completelyDiscard"!==t.disallowedTagsMode||w(r))if("discard"!==t.disallowedTagsMode&&"completelyDiscard"!==t.disallowedTagsMode||"script"!==r&&"style"!==r){const n=R(e,!1);t.textFilter&&!j?y+=t.textFilter(n,r):j||(y+=n)}else y+=e;else e="";if(N.length){N[N.length-1].text+=e}},onclosetag:function(e,n){if(P){if(I--,I)return;P=!1}const r=N.pop();if(!r)return;if(r.tag!==e)return void N.push(r);P=!!t.enforceHtmlBoundary&&"html"===e,O--;const i=D[O];if(i){if(delete D[O],"discard"===t.disallowedTagsMode||"completelyDiscard"===t.disallowedTagsMode)return void r.updateParentNodeText();v=y,y=""}L[O]&&(e=L[O],delete L[O]),t.exclusiveFilter&&t.exclusiveFilter(r)?y=y.substr(0,r.tagPosition):(r.updateParentNodeMediaChildren(),r.updateParentNodeText(),-1!==t.selfClosing.indexOf(e)||n&&!w(e)&&["escape","recursiveEscape"].indexOf(t.disallowedTagsMode)>=0?i&&(y=v,v=""):(y+="</"+e+">",i&&(y=v+R(y),v=""),j=!1))}},t.parser);return M.write(e),M.end(),y;function q(){y="",O=0,N=[],D={},L={},P=!1,I=0}function R(e,n){return"string"!=typeof e&&(e+=""),t.parser.decodeEntities&&(e=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),n&&(e=e.replace(/"/g,"&quot;"))),e=e.replace(/&(?![a-zA-Z0-9#]{1,20};)/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),n&&(e=e.replace(/"/g,"&quot;")),e}function B(e,n){for(n=n.replace(/[\x00-\x20]+/g,"");;){const e=n.indexOf("\x3c!--");if(-1===e)break;const t=n.indexOf("--\x3e",e+4);if(-1===t)break;n=n.substring(0,e)+n.substring(t+3)}const r=n.match(/^([a-zA-Z][a-zA-Z0-9.\-+]*):/);if(!r)return!!n.match(/^[/\\]{2}/)&&!t.allowProtocolRelative;const i=r[1].toLowerCase();return f(t.allowedSchemesByTag,e)?-1===t.allowedSchemesByTag[e].indexOf(i):!t.allowedSchemes||-1===t.allowedSchemes.indexOf(i)}function $(e){if((e=e.replace(/^(\w+:)?\s*[\\/]\s*[\\/]/,"$1//")).startsWith("relative:"))throw new Error("relative: exploit attempt");let t="relative://relative-site";for(let e=0;e<100;e++)t+=`/${e}`;const n=new URL(e,t);return{isRelativeUrl:n&&"relative-site"===n.hostname&&"relative:"===n.protocol,url:n}}function H(e,t,n){return t?(e=e.split(/\s+/)).filter((function(e){return-1!==t.indexOf(e)||n.some((function(t){return t.test(e)}))})).join(" "):e}}const g={decodeEntities:!0};m.defaults={allowedTags:["address","article","aside","footer","header","h1","h2","h3","h4","h5","h6","hgroup","main","nav","section","blockquote","dd","div","dl","dt","figcaption","figure","hr","li","main","ol","p","pre","ul","a","abbr","b","bdi","bdo","br","cite","code","data","dfn","em","i","kbd","mark","q","rb","rp","rt","rtc","ruby","s","samp","small","span","strong","sub","sup","time","u","var","wbr","caption","col","colgroup","table","tbody","td","tfoot","th","thead","tr"],nonBooleanAttributes:["abbr","accept","accept-charset","accesskey","action","allow","alt","as","autocapitalize","autocomplete","blocking","charset","cite","class","color","cols","colspan","content","contenteditable","coords","crossorigin","data","datetime","decoding","dir","dirname","download","draggable","enctype","enterkeyhint","fetchpriority","for","form","formaction","formenctype","formmethod","formtarget","headers","height","hidden","high","href","hreflang","http-equiv","id","imagesizes","imagesrcset","inputmode","integrity","is","itemid","itemprop","itemref","itemtype","kind","label","lang","list","loading","low","max","maxlength","media","method","min","minlength","name","nonce","optimum","pattern","ping","placeholder","popover","popovertarget","popovertargetaction","poster","preload","referrerpolicy","rel","rows","rowspan","sandbox","scope","shape","size","sizes","slot","span","spellcheck","src","srcdoc","srclang","srcset","start","step","style","tabindex","target","title","translate","type","usemap","value","width","wrap","onauxclick","onafterprint","onbeforematch","onbeforeprint","onbeforeunload","onbeforetoggle","onblur","oncancel","oncanplay","oncanplaythrough","onchange","onclick","onclose","oncontextlost","oncontextmenu","oncontextrestored","oncopy","oncuechange","oncut","ondblclick","ondrag","ondragend","ondragenter","ondragleave","ondragover","ondragstart","ondrop","ondurationchange","onemptied","onended","onerror","onfocus","onformdata","onhashchange","oninput","oninvalid","onkeydown","onkeypress","onkeyup","onlanguagechange","onload","onloadeddata","onloadedmetadata","onloadstart","onmessage","onmessageerror","onmousedown","onmouseenter","onmouseleave","onmousemove","onmouseout","onmouseover","onmouseup","onoffline","ononline","onpagehide","onpageshow","onpaste","onpause","onplay","onplaying","onpopstate","onprogress","onratechange","onreset","onresize","onrejectionhandled","onscroll","onscrollend","onsecuritypolicyviolation","onseeked","onseeking","onselect","onslotchange","onstalled","onstorage","onsubmit","onsuspend","ontimeupdate","ontoggle","onunhandledrejection","onunload","onvolumechange","onwaiting","onwheel"],disallowedTagsMode:"discard",allowedAttributes:{a:["href","name","target"],img:["src","srcset","alt","title","width","height","loading"]},allowedEmptyAttributes:["alt"],selfClosing:["img","br","hr","area","base","basefont","input","link","meta"],allowedSchemes:["http","https","ftp","mailto","tel"],allowedSchemesByTag:{},allowedSchemesAppliedToAttributes:["href","src","cite"],allowProtocolRelative:!0,enforceHtmlBoundary:!1,parseStyleAttributes:!0},m.simpleTransform=function(e,t,n){return n=void 0===n||n,t=t||{},function(r,i){let o;if(n)for(o in t)i[o]=t[o];else i=t;return{tagName:e,attribs:i}}}},4744:e=>{"use strict";var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===n}(e)}(e)};var n="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function r(e,t){return!1!==t.clone&&t.isMergeableObject(e)?l((n=e,Array.isArray(n)?[]:{}),e,t):e;var n}function i(e,t,n){return e.concat(t).map((function(e){return r(e,n)}))}function o(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function s(e,t){try{return t in e}catch(e){return!1}}function a(e,t,n){var i={};return n.isMergeableObject(e)&&o(e).forEach((function(t){i[t]=r(e[t],n)})),o(t).forEach((function(o){(function(e,t){return s(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,o)||(s(e,o)&&n.isMergeableObject(t[o])?i[o]=function(e,t){if(!t.customMerge)return l;var n=t.customMerge(e);return"function"==typeof n?n:l}(o,n)(e[o],t[o],n):i[o]=r(t[o],n))})),i}function l(e,n,o){(o=o||{}).arrayMerge=o.arrayMerge||i,o.isMergeableObject=o.isMergeableObject||t,o.cloneUnlessOtherwiseSpecified=r;var s=Array.isArray(n);return s===Array.isArray(e)?s?o.arrayMerge(e,n,o):a(e,n,o):r(n,o)}l.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,n){return l(e,n,t)}),{})};var c=l;e.exports=c},5042:e=>{e.exports={nanoid:(e=21)=>{let t="",n=0|e;for(;n--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[64*Math.random()|0];return t},customAlphabet:(e,t=21)=>(n=t)=>{let r="",i=0|n;for(;i--;)r+=e[Math.random()*e.length|0];return r}}},5238:(e,t,n)=>{"use strict";let r=n(3152);class i extends r{get variable(){return this.prop.startsWith("--")||"$"===this.prop[0]}constructor(e){e&&void 0!==e.value&&"string"!=typeof e.value&&(e={...e,value:String(e.value)}),super(e),this.type="decl"}}e.exports=i,i.default=i},5413:(e,t)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),t.Doctype=t.CDATA=t.Tag=t.Style=t.Script=t.Comment=t.Directive=t.Text=t.Root=t.isTag=t.ElementType=void 0,function(e){e.Root="root",e.Text="text",e.Directive="directive",e.Comment="comment",e.Script="script",e.Style="style",e.Tag="tag",e.CDATA="cdata",e.Doctype="doctype"}(n=t.ElementType||(t.ElementType={})),t.isTag=function(e){return e.type===n.Tag||e.type===n.Script||e.type===n.Style},t.Root=n.Root,t.Text=n.Text,t.Directive=n.Directive,t.Comment=n.Comment,t.Script=n.Script,t.Style=n.Style,t.Tag=n.Tag,t.CDATA=n.CDATA,t.Doctype=n.Doctype},5606:e=>{var t,n,r=e.exports={};function i(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function s(e){if(t===setTimeout)return setTimeout(e,0);if((t===i||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(n){try{return t.call(null,e,0)}catch(n){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:i}catch(e){t=i}try{n="function"==typeof clearTimeout?clearTimeout:o}catch(e){n=o}}();var a,l=[],c=!1,u=-1;function h(){c&&a&&(c=!1,a.length?l=a.concat(l):u=-1,l.length&&f())}function f(){if(!c){var e=s(h);c=!0;for(var t=l.length;t;){for(a=l,l=[];++u<t;)a&&a[u].run();u=-1,t=l.length}a=null,c=!1,function(e){if(n===clearTimeout)return clearTimeout(e);if((n===o||!n)&&clearTimeout)return n=clearTimeout,clearTimeout(e);try{return n(e)}catch(t){try{return n.call(null,e)}catch(t){return n.call(this,e)}}}(e)}}function d(e,t){this.fun=e,this.array=t}function p(){}r.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];l.push(new d(e,t)),1!==l.length||c||s(f)},d.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={},r.on=p,r.addListener=p,r.once=p,r.off=p,r.removeListener=p,r.removeAllListeners=p,r.emit=p,r.prependListener=p,r.prependOnceListener=p,r.listeners=function(e){return[]},r.binding=function(e){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(e){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}},5644:(e,t,n)=>{"use strict";let r,i,o=n(7793);class s extends o{constructor(e){super(e),this.type="root",this.nodes||(this.nodes=[])}normalize(e,t,n){let r=super.normalize(e);if(t)if("prepend"===n)this.nodes.length>1?t.raws.before=this.nodes[1].raws.before:delete t.raws.before;else if(this.first!==t)for(let e of r)e.raws.before=t.raws.before;return r}removeChild(e,t){let n=this.index(e);return!t&&0===n&&this.nodes.length>1&&(this.nodes[1].raws.before=this.nodes[n].raws.before),super.removeChild(e)}toResult(e={}){return new r(new i,this,e).stringify()}}s.registerLazyResult=e=>{r=e},s.registerProcessor=e=>{i=e},e.exports=s,s.default=s,o.registerRoot(s)},5781:e=>{"use strict";const t="'".charCodeAt(0),n='"'.charCodeAt(0),r="\\".charCodeAt(0),i="/".charCodeAt(0),o="\n".charCodeAt(0),s=" ".charCodeAt(0),a="\f".charCodeAt(0),l="\t".charCodeAt(0),c="\r".charCodeAt(0),u="[".charCodeAt(0),h="]".charCodeAt(0),f="(".charCodeAt(0),d=")".charCodeAt(0),p="{".charCodeAt(0),m="}".charCodeAt(0),g=";".charCodeAt(0),y="*".charCodeAt(0),v=":".charCodeAt(0),b="@".charCodeAt(0),w=/[\t\n\f\r "#'()/;[\\\]{}]/g,x=/[\t\n\f\r !"#'():;@[\\\]{}]|\/(?=\*)/g,_=/.[\r\n"'(/\\]/,T=/[\da-f]/i;e.exports=function(e,E={}){let A,S,C,k,O,N,D,L,P,I,j=e.css.valueOf(),M=E.ignoreErrors,q=j.length,R=0,B=[],$=[];function H(t){throw e.error("Unclosed "+t,R)}return{back:function(e){$.push(e)},endOfFile:function(){return 0===$.length&&R>=q},nextToken:function(e){if($.length)return $.pop();if(R>=q)return;let E=!!e&&e.ignoreUnclosed;switch(A=j.charCodeAt(R),A){case o:case s:case l:case c:case a:k=R;do{k+=1,A=j.charCodeAt(k)}while(A===s||A===o||A===l||A===c||A===a);N=["space",j.slice(R,k)],R=k-1;break;case u:case h:case p:case m:case v:case g:case d:{let e=String.fromCharCode(A);N=[e,e,R];break}case f:if(I=B.length?B.pop()[1]:"",P=j.charCodeAt(R+1),"url"===I&&P!==t&&P!==n&&P!==s&&P!==o&&P!==l&&P!==a&&P!==c){k=R;do{if(D=!1,k=j.indexOf(")",k+1),-1===k){if(M||E){k=R;break}H("bracket")}for(L=k;j.charCodeAt(L-1)===r;)L-=1,D=!D}while(D);N=["brackets",j.slice(R,k+1),R,k],R=k}else k=j.indexOf(")",R+1),S=j.slice(R,k+1),-1===k||_.test(S)?N=["(","(",R]:(N=["brackets",S,R,k],R=k);break;case t:case n:O=A===t?"'":'"',k=R;do{if(D=!1,k=j.indexOf(O,k+1),-1===k){if(M||E){k=R+1;break}H("string")}for(L=k;j.charCodeAt(L-1)===r;)L-=1,D=!D}while(D);N=["string",j.slice(R,k+1),R,k],R=k;break;case b:w.lastIndex=R+1,w.test(j),k=0===w.lastIndex?j.length-1:w.lastIndex-2,N=["at-word",j.slice(R,k+1),R,k],R=k;break;case r:for(k=R,C=!0;j.charCodeAt(k+1)===r;)k+=1,C=!C;if(A=j.charCodeAt(k+1),C&&A!==i&&A!==s&&A!==o&&A!==l&&A!==c&&A!==a&&(k+=1,T.test(j.charAt(k)))){for(;T.test(j.charAt(k+1));)k+=1;j.charCodeAt(k+1)===s&&(k+=1)}N=["word",j.slice(R,k+1),R,k],R=k;break;default:A===i&&j.charCodeAt(R+1)===y?(k=j.indexOf("*/",R+2)+1,0===k&&(M||E?k=j.length:H("comment")),N=["comment",j.slice(R,k+1),R,k],R=k):(x.lastIndex=R+1,x.test(j),k=0===x.lastIndex?j.length-1:x.lastIndex-2,N=["word",j.slice(R,k+1),R,k],B.push(N),R=k)}return R++,N},position:function(){return R}}}},5936:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DocumentPosition=void 0,t.removeSubsets=function(e){var t=e.length;for(;--t>=0;){var n=e[t];if(t>0&&e.lastIndexOf(n,t-1)>=0)e.splice(t,1);else for(var r=n.parent;r;r=r.parent)if(e.includes(r)){e.splice(t,1);break}}return e},t.compareDocumentPosition=o,t.uniqueSort=function(e){return(e=e.filter((function(e,t,n){return!n.includes(e,t+1)}))).sort((function(e,t){var n=o(e,t);return n&r.PRECEDING?-1:n&r.FOLLOWING?1:0})),e};var r,i=n(4128);function o(e,t){var n=[],o=[];if(e===t)return 0;for(var s=(0,i.hasChildren)(e)?e:e.parent;s;)n.unshift(s),s=s.parent;for(s=(0,i.hasChildren)(t)?t:t.parent;s;)o.unshift(s),s=s.parent;for(var a=Math.min(n.length,o.length),l=0;l<a&&n[l]===o[l];)l++;if(0===l)return r.DISCONNECTED;var c=n[l-1],u=c.children,h=n[l],f=o[l];return u.indexOf(h)>u.indexOf(f)?c===t?r.FOLLOWING|r.CONTAINED_BY:r.FOLLOWING:c===e?r.PRECEDING|r.CONTAINS:r.PRECEDING}!function(e){e[e.DISCONNECTED=1]="DISCONNECTED",e[e.PRECEDING=2]="PRECEDING",e[e.FOLLOWING=4]="FOLLOWING",e[e.CONTAINS=8]="CONTAINS",e[e.CONTAINED_BY=16]="CONTAINED_BY"}(r||(t.DocumentPosition=r={}))},6156:e=>{"use strict";let t={};e.exports=function(e){t[e]||(t[e]=!0,"undefined"!=typeof console&&console.warn&&console.warn(e))}},6808:function(e,t,n){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),o=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return i(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.DomUtils=t.parseFeed=t.getFeed=t.ElementType=t.Tokenizer=t.createDomStream=t.parseDOM=t.parseDocument=t.DefaultHandler=t.DomHandler=t.Parser=void 0;var a=n(221),l=n(221);Object.defineProperty(t,"Parser",{enumerable:!0,get:function(){return l.Parser}});var c=n(4128),u=n(4128);function h(e,t){var n=new c.DomHandler(void 0,t);return new a.Parser(n,t).end(e),n.root}function f(e,t){return h(e,t).children}Object.defineProperty(t,"DomHandler",{enumerable:!0,get:function(){return u.DomHandler}}),Object.defineProperty(t,"DefaultHandler",{enumerable:!0,get:function(){return u.DomHandler}}),t.parseDocument=h,t.parseDOM=f,t.createDomStream=function(e,t,n){var r=new c.DomHandler(e,t,n);return new a.Parser(r,t)};var d=n(357);Object.defineProperty(t,"Tokenizer",{enumerable:!0,get:function(){return s(d).default}}),t.ElementType=o(n(5413));var p=n(1941),m=n(1941);Object.defineProperty(t,"getFeed",{enumerable:!0,get:function(){return m.getFeed}});var g={xmlMode:!0};t.parseFeed=function(e,t){return void 0===t&&(t=g),(0,p.getFeed)(f(e,t))},t.DomUtils=o(n(1941))},6835:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.decodeXMLStrict=t.decodeHTML5Strict=t.decodeHTML4Strict=t.decodeHTML5=t.decodeHTML4=t.decodeHTMLAttribute=t.decodeHTMLStrict=t.decodeHTML=t.decodeXML=t.DecodingMode=t.EntityDecoder=t.encodeHTML5=t.encodeHTML4=t.encodeNonAsciiHTML=t.encodeHTML=t.escapeText=t.escapeAttribute=t.escapeUTF8=t.escape=t.encodeXML=t.encode=t.decodeStrict=t.decode=t.EncodingMode=t.EntityLevel=void 0;var r,i,o=n(2349),s=n(8149),a=n(1560);function l(e,t){if(void 0===t&&(t=r.XML),("number"==typeof t?t:t.level)===r.HTML){var n="object"==typeof t?t.mode:void 0;return(0,o.decodeHTML)(e,n)}return(0,o.decodeXML)(e)}!function(e){e[e.XML=0]="XML",e[e.HTML=1]="HTML"}(r=t.EntityLevel||(t.EntityLevel={})),function(e){e[e.UTF8=0]="UTF8",e[e.ASCII=1]="ASCII",e[e.Extensive=2]="Extensive",e[e.Attribute=3]="Attribute",e[e.Text=4]="Text"}(i=t.EncodingMode||(t.EncodingMode={})),t.decode=l,t.decodeStrict=function(e,t){var n;void 0===t&&(t=r.XML);var i="number"==typeof t?{level:t}:t;return null!==(n=i.mode)&&void 0!==n||(i.mode=o.DecodingMode.Strict),l(e,i)},t.encode=function(e,t){void 0===t&&(t=r.XML);var n="number"==typeof t?{level:t}:t;return n.mode===i.UTF8?(0,a.escapeUTF8)(e):n.mode===i.Attribute?(0,a.escapeAttribute)(e):n.mode===i.Text?(0,a.escapeText)(e):n.level===r.HTML?n.mode===i.ASCII?(0,s.encodeNonAsciiHTML)(e):(0,s.encodeHTML)(e):(0,a.encodeXML)(e)};var c=n(1560);Object.defineProperty(t,"encodeXML",{enumerable:!0,get:function(){return c.encodeXML}}),Object.defineProperty(t,"escape",{enumerable:!0,get:function(){return c.escape}}),Object.defineProperty(t,"escapeUTF8",{enumerable:!0,get:function(){return c.escapeUTF8}}),Object.defineProperty(t,"escapeAttribute",{enumerable:!0,get:function(){return c.escapeAttribute}}),Object.defineProperty(t,"escapeText",{enumerable:!0,get:function(){return c.escapeText}});var u=n(8149);Object.defineProperty(t,"encodeHTML",{enumerable:!0,get:function(){return u.encodeHTML}}),Object.defineProperty(t,"encodeNonAsciiHTML",{enumerable:!0,get:function(){return u.encodeNonAsciiHTML}}),Object.defineProperty(t,"encodeHTML4",{enumerable:!0,get:function(){return u.encodeHTML}}),Object.defineProperty(t,"encodeHTML5",{enumerable:!0,get:function(){return u.encodeHTML}});var h=n(2349);Object.defineProperty(t,"EntityDecoder",{enumerable:!0,get:function(){return h.EntityDecoder}}),Object.defineProperty(t,"DecodingMode",{enumerable:!0,get:function(){return h.DecodingMode}}),Object.defineProperty(t,"decodeXML",{enumerable:!0,get:function(){return h.decodeXML}}),Object.defineProperty(t,"decodeHTML",{enumerable:!0,get:function(){return h.decodeHTML}}),Object.defineProperty(t,"decodeHTMLStrict",{enumerable:!0,get:function(){return h.decodeHTMLStrict}}),Object.defineProperty(t,"decodeHTMLAttribute",{enumerable:!0,get:function(){return h.decodeHTMLAttribute}}),Object.defineProperty(t,"decodeHTML4",{enumerable:!0,get:function(){return h.decodeHTML}}),Object.defineProperty(t,"decodeHTML5",{enumerable:!0,get:function(){return h.decodeHTML}}),Object.defineProperty(t,"decodeHTML4Strict",{enumerable:!0,get:function(){return h.decodeHTMLStrict}}),Object.defineProperty(t,"decodeHTML5Strict",{enumerable:!0,get:function(){return h.decodeHTMLStrict}}),Object.defineProperty(t,"decodeXMLStrict",{enumerable:!0,get:function(){return h.decodeXML}})},6846:(e,t,n)=>{"use strict";let r=n(145),i=n(6966),o=n(4211),s=n(5644);class a{constructor(e=[]){this.version="8.5.3",this.plugins=this.normalize(e)}normalize(e){let t=[];for(let n of e)if(!0===n.postcss?n=n():n.postcss&&(n=n.postcss),"object"==typeof n&&Array.isArray(n.plugins))t=t.concat(n.plugins);else if("object"==typeof n&&n.postcssPlugin)t.push(n);else if("function"==typeof n)t.push(n);else{if("object"!=typeof n||!n.parse&&!n.stringify)throw new Error(n+" is not a PostCSS plugin")}return t}process(e,t={}){return this.plugins.length||t.parser||t.stringifier||t.syntax?new i(this,e,t):new o(this,e,t)}use(e){return this.plugins=this.plugins.concat(this.normalize([e])),this}}e.exports=a,a.default=a,s.registerProcessor(a),r.registerProcessor(a)},6966:(e,t,n)=>{"use strict";let r=n(7793),i=n(145),o=n(3604),s=n(9577),a=n(3717),l=n(5644),c=n(3303),{isClean:u,my:h}=n(4151);n(6156);const f={atrule:"AtRule",comment:"Comment",decl:"Declaration",document:"Document",root:"Root",rule:"Rule"},d={AtRule:!0,AtRuleExit:!0,Comment:!0,CommentExit:!0,Declaration:!0,DeclarationExit:!0,Document:!0,DocumentExit:!0,Once:!0,OnceExit:!0,postcssPlugin:!0,prepare:!0,Root:!0,RootExit:!0,Rule:!0,RuleExit:!0},p={Once:!0,postcssPlugin:!0,prepare:!0};function m(e){return"object"==typeof e&&"function"==typeof e.then}function g(e){let t=!1,n=f[e.type];return"decl"===e.type?t=e.prop.toLowerCase():"atrule"===e.type&&(t=e.name.toLowerCase()),t&&e.append?[n,n+"-"+t,0,n+"Exit",n+"Exit-"+t]:t?[n,n+"-"+t,n+"Exit",n+"Exit-"+t]:e.append?[n,0,n+"Exit"]:[n,n+"Exit"]}function y(e){let t;return t="document"===e.type?["Document",0,"DocumentExit"]:"root"===e.type?["Root",0,"RootExit"]:g(e),{eventIndex:0,events:t,iterator:0,node:e,visitorIndex:0,visitors:[]}}function v(e){return e[u]=!1,e.nodes&&e.nodes.forEach((e=>v(e))),e}let b={};class w{get content(){return this.stringify().content}get css(){return this.stringify().css}get map(){return this.stringify().map}get messages(){return this.sync().messages}get opts(){return this.result.opts}get processor(){return this.result.processor}get root(){return this.sync().root}get[Symbol.toStringTag](){return"LazyResult"}constructor(e,t,n){let i;if(this.stringified=!1,this.processed=!1,"object"!=typeof t||null===t||"root"!==t.type&&"document"!==t.type)if(t instanceof w||t instanceof a)i=v(t.root),t.map&&(void 0===n.map&&(n.map={}),n.map.inline||(n.map.inline=!1),n.map.prev=t.map);else{let e=s;n.syntax&&(e=n.syntax.parse),n.parser&&(e=n.parser),e.parse&&(e=e.parse);try{i=e(t,n)}catch(e){this.processed=!0,this.error=e}i&&!i[h]&&r.rebuild(i)}else i=v(t);this.result=new a(e,i,n),this.helpers={...b,postcss:b,result:this.result},this.plugins=this.processor.plugins.map((e=>"object"==typeof e&&e.prepare?{...e,...e.prepare(this.result)}:e))}async(){return this.error?Promise.reject(this.error):this.processed?Promise.resolve(this.result):(this.processing||(this.processing=this.runAsync()),this.processing)}catch(e){return this.async().catch(e)}finally(e){return this.async().then(e,e)}getAsyncError(){throw new Error("Use process(css).then(cb) to work with async plugins")}handleError(e,t){let n=this.result.lastPlugin;try{t&&t.addToError(e),this.error=e,"CssSyntaxError"!==e.name||e.plugin?n.postcssVersion:(e.plugin=n.postcssPlugin,e.setMessage())}catch(e){console&&console.error&&console.error(e)}return e}prepareVisitors(){this.listeners={};let e=(e,t,n)=>{this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push([e,n])};for(let t of this.plugins)if("object"==typeof t)for(let n in t){if(!d[n]&&/^[A-Z]/.test(n))throw new Error(`Unknown event ${n} in ${t.postcssPlugin}. Try to update PostCSS (${this.processor.version} now).`);if(!p[n])if("object"==typeof t[n])for(let r in t[n])e(t,"*"===r?n:n+"-"+r.toLowerCase(),t[n][r]);else"function"==typeof t[n]&&e(t,n,t[n])}this.hasListener=Object.keys(this.listeners).length>0}async runAsync(){this.plugin=0;for(let e=0;e<this.plugins.length;e++){let t=this.plugins[e],n=this.runOnRoot(t);if(m(n))try{await n}catch(e){throw this.handleError(e)}}if(this.prepareVisitors(),this.hasListener){let e=this.result.root;for(;!e[u];){e[u]=!0;let t=[y(e)];for(;t.length>0;){let e=this.visitTick(t);if(m(e))try{await e}catch(e){let n=t[t.length-1].node;throw this.handleError(e,n)}}}if(this.listeners.OnceExit)for(let[t,n]of this.listeners.OnceExit){this.result.lastPlugin=t;try{if("document"===e.type){let t=e.nodes.map((e=>n(e,this.helpers)));await Promise.all(t)}else await n(e,this.helpers)}catch(e){throw this.handleError(e)}}}return this.processed=!0,this.stringify()}runOnRoot(e){this.result.lastPlugin=e;try{if("object"==typeof e&&e.Once){if("document"===this.result.root.type){let t=this.result.root.nodes.map((t=>e.Once(t,this.helpers)));return m(t[0])?Promise.all(t):t}return e.Once(this.result.root,this.helpers)}if("function"==typeof e)return e(this.result.root,this.result)}catch(e){throw this.handleError(e)}}stringify(){if(this.error)throw this.error;if(this.stringified)return this.result;this.stringified=!0,this.sync();let e=this.result.opts,t=c;e.syntax&&(t=e.syntax.stringify),e.stringifier&&(t=e.stringifier),t.stringify&&(t=t.stringify);let n=new o(t,this.result.root,this.result.opts).generate();return this.result.css=n[0],this.result.map=n[1],this.result}sync(){if(this.error)throw this.error;if(this.processed)return this.result;if(this.processed=!0,this.processing)throw this.getAsyncError();for(let e of this.plugins){if(m(this.runOnRoot(e)))throw this.getAsyncError()}if(this.prepareVisitors(),this.hasListener){let e=this.result.root;for(;!e[u];)e[u]=!0,this.walkSync(e);if(this.listeners.OnceExit)if("document"===e.type)for(let t of e.nodes)this.visitSync(this.listeners.OnceExit,t);else this.visitSync(this.listeners.OnceExit,e)}return this.result}then(e,t){return this.async().then(e,t)}toString(){return this.css}visitSync(e,t){for(let[n,r]of e){let e;this.result.lastPlugin=n;try{e=r(t,this.helpers)}catch(e){throw this.handleError(e,t.proxyOf)}if("root"!==t.type&&"document"!==t.type&&!t.parent)return!0;if(m(e))throw this.getAsyncError()}}visitTick(e){let t=e[e.length-1],{node:n,visitors:r}=t;if("root"!==n.type&&"document"!==n.type&&!n.parent)return void e.pop();if(r.length>0&&t.visitorIndex<r.length){let[e,i]=r[t.visitorIndex];t.visitorIndex+=1,t.visitorIndex===r.length&&(t.visitors=[],t.visitorIndex=0),this.result.lastPlugin=e;try{return i(n.toProxy(),this.helpers)}catch(e){throw this.handleError(e,n)}}if(0!==t.iterator){let r,i=t.iterator;for(;r=n.nodes[n.indexes[i]];)if(n.indexes[i]+=1,!r[u])return r[u]=!0,void e.push(y(r));t.iterator=0,delete n.indexes[i]}let i=t.events;for(;t.eventIndex<i.length;){let e=i[t.eventIndex];if(t.eventIndex+=1,0===e)return void(n.nodes&&n.nodes.length&&(n[u]=!0,t.iterator=n.getIterator()));if(this.listeners[e])return void(t.visitors=this.listeners[e])}e.pop()}walkSync(e){e[u]=!0;let t=g(e);for(let n of t)if(0===n)e.nodes&&e.each((e=>{e[u]||this.walkSync(e)}));else{let t=this.listeners[n];if(t&&this.visitSync(t,e.toProxy()))return}}warnings(){return this.sync().warnings()}}w.registerPostcss=e=>{b=e},e.exports=w,w.default=w,l.registerLazyResult(w),i.registerLazyResult(w)},7526:(e,t)=>{"use strict";t.byteLength=function(e){var t=a(e),n=t[0],r=t[1];return 3*(n+r)/4-r},t.toByteArray=function(e){var t,n,o=a(e),s=o[0],l=o[1],c=new i(function(e,t,n){return 3*(t+n)/4-n}(0,s,l)),u=0,h=l>0?s-4:s;for(n=0;n<h;n+=4)t=r[e.charCodeAt(n)]<<18|r[e.charCodeAt(n+1)]<<12|r[e.charCodeAt(n+2)]<<6|r[e.charCodeAt(n+3)],c[u++]=t>>16&255,c[u++]=t>>8&255,c[u++]=255&t;2===l&&(t=r[e.charCodeAt(n)]<<2|r[e.charCodeAt(n+1)]>>4,c[u++]=255&t);1===l&&(t=r[e.charCodeAt(n)]<<10|r[e.charCodeAt(n+1)]<<4|r[e.charCodeAt(n+2)]>>2,c[u++]=t>>8&255,c[u++]=255&t);return c},t.fromByteArray=function(e){for(var t,r=e.length,i=r%3,o=[],s=16383,a=0,c=r-i;a<c;a+=s)o.push(l(e,a,a+s>c?c:a+s));1===i?(t=e[r-1],o.push(n[t>>2]+n[t<<4&63]+"==")):2===i&&(t=(e[r-2]<<8)+e[r-1],o.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+"="));return o.join("")};for(var n=[],r=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0;s<64;++s)n[s]=o[s],r[o.charCodeAt(s)]=s;function a(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");return-1===n&&(n=t),[n,n===t?0:4-n%4]}function l(e,t,r){for(var i,o,s=[],a=t;a<r;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),s.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return s.join("")}r["-".charCodeAt(0)]=62,r["_".charCodeAt(0)]=63},7668:e=>{"use strict";const t={after:"\n",beforeClose:"\n",beforeComment:"\n",beforeDecl:"\n",beforeOpen:" ",beforeRule:"\n",colon:": ",commentLeft:" ",commentRight:" ",emptyBody:"",indent:"    ",semicolon:!1};class n{constructor(e){this.builder=e}atrule(e,t){let n="@"+e.name,r=e.params?this.rawValue(e,"params"):"";if(void 0!==e.raws.afterName?n+=e.raws.afterName:r&&(n+=" "),e.nodes)this.block(e,n+r);else{let i=(e.raws.between||"")+(t?";":"");this.builder(n+r+i,e)}}beforeAfter(e,t){let n;n="decl"===e.type?this.raw(e,null,"beforeDecl"):"comment"===e.type?this.raw(e,null,"beforeComment"):"before"===t?this.raw(e,null,"beforeRule"):this.raw(e,null,"beforeClose");let r=e.parent,i=0;for(;r&&"root"!==r.type;)i+=1,r=r.parent;if(n.includes("\n")){let t=this.raw(e,null,"indent");if(t.length)for(let e=0;e<i;e++)n+=t}return n}block(e,t){let n,r=this.raw(e,"between","beforeOpen");this.builder(t+r+"{",e,"start"),e.nodes&&e.nodes.length?(this.body(e),n=this.raw(e,"after")):n=this.raw(e,"after","emptyBody"),n&&this.builder(n),this.builder("}",e,"end")}body(e){let t=e.nodes.length-1;for(;t>0&&"comment"===e.nodes[t].type;)t-=1;let n=this.raw(e,"semicolon");for(let r=0;r<e.nodes.length;r++){let i=e.nodes[r],o=this.raw(i,"before");o&&this.builder(o),this.stringify(i,t!==r||n)}}comment(e){let t=this.raw(e,"left","commentLeft"),n=this.raw(e,"right","commentRight");this.builder("/*"+t+e.text+n+"*/",e)}decl(e,t){let n=this.raw(e,"between","colon"),r=e.prop+n+this.rawValue(e,"value");e.important&&(r+=e.raws.important||" !important"),t&&(r+=";"),this.builder(r,e)}document(e){this.body(e)}raw(e,n,r){let i;if(r||(r=n),n&&(i=e.raws[n],void 0!==i))return i;let o=e.parent;if("before"===r){if(!o||"root"===o.type&&o.first===e)return"";if(o&&"document"===o.type)return""}if(!o)return t[r];let s=e.root();if(s.rawCache||(s.rawCache={}),void 0!==s.rawCache[r])return s.rawCache[r];if("before"===r||"after"===r)return this.beforeAfter(e,r);{let t="raw"+((a=r)[0].toUpperCase()+a.slice(1));this[t]?i=this[t](s,e):s.walk((e=>{if(i=e.raws[n],void 0!==i)return!1}))}var a;return void 0===i&&(i=t[r]),s.rawCache[r]=i,i}rawBeforeClose(e){let t;return e.walk((e=>{if(e.nodes&&e.nodes.length>0&&void 0!==e.raws.after)return t=e.raws.after,t.includes("\n")&&(t=t.replace(/[^\n]+$/,"")),!1})),t&&(t=t.replace(/\S/g,"")),t}rawBeforeComment(e,t){let n;return e.walkComments((e=>{if(void 0!==e.raws.before)return n=e.raws.before,n.includes("\n")&&(n=n.replace(/[^\n]+$/,"")),!1})),void 0===n?n=this.raw(t,null,"beforeDecl"):n&&(n=n.replace(/\S/g,"")),n}rawBeforeDecl(e,t){let n;return e.walkDecls((e=>{if(void 0!==e.raws.before)return n=e.raws.before,n.includes("\n")&&(n=n.replace(/[^\n]+$/,"")),!1})),void 0===n?n=this.raw(t,null,"beforeRule"):n&&(n=n.replace(/\S/g,"")),n}rawBeforeOpen(e){let t;return e.walk((e=>{if("decl"!==e.type&&(t=e.raws.between,void 0!==t))return!1})),t}rawBeforeRule(e){let t;return e.walk((n=>{if(n.nodes&&(n.parent!==e||e.first!==n)&&void 0!==n.raws.before)return t=n.raws.before,t.includes("\n")&&(t=t.replace(/[^\n]+$/,"")),!1})),t&&(t=t.replace(/\S/g,"")),t}rawColon(e){let t;return e.walkDecls((e=>{if(void 0!==e.raws.between)return t=e.raws.between.replace(/[^\s:]/g,""),!1})),t}rawEmptyBody(e){let t;return e.walk((e=>{if(e.nodes&&0===e.nodes.length&&(t=e.raws.after,void 0!==t))return!1})),t}rawIndent(e){if(e.raws.indent)return e.raws.indent;let t;return e.walk((n=>{let r=n.parent;if(r&&r!==e&&r.parent&&r.parent===e&&void 0!==n.raws.before){let e=n.raws.before.split("\n");return t=e[e.length-1],t=t.replace(/\S/g,""),!1}})),t}rawSemicolon(e){let t;return e.walk((e=>{if(e.nodes&&e.nodes.length&&"decl"===e.last.type&&(t=e.raws.semicolon,void 0!==t))return!1})),t}rawValue(e,t){let n=e[t],r=e.raws[t];return r&&r.value===n?r.raw:n}root(e){this.body(e),e.raws.after&&this.builder(e.raws.after)}rule(e){this.block(e,this.rawValue(e,"selector")),e.raws.ownSemicolon&&this.builder(e.raws.ownSemicolon,e,"end")}stringify(e,t){if(!this[e.type])throw new Error("Unknown AST node type "+e.type+". Maybe you need to change PostCSS stringifier.");this[e.type](e,t)}}e.exports=n,n.default=n},7793:(e,t,n)=>{"use strict";let r,i,o,s,a=n(9371),l=n(5238),c=n(3152),{isClean:u,my:h}=n(4151);function f(e){return e.map((e=>(e.nodes&&(e.nodes=f(e.nodes)),delete e.source,e)))}function d(e){if(e[u]=!1,e.proxyOf.nodes)for(let t of e.proxyOf.nodes)d(t)}class p extends c{get first(){if(this.proxyOf.nodes)return this.proxyOf.nodes[0]}get last(){if(this.proxyOf.nodes)return this.proxyOf.nodes[this.proxyOf.nodes.length-1]}append(...e){for(let t of e){let e=this.normalize(t,this.last);for(let t of e)this.proxyOf.nodes.push(t)}return this.markDirty(),this}cleanRaws(e){if(super.cleanRaws(e),this.nodes)for(let t of this.nodes)t.cleanRaws(e)}each(e){if(!this.proxyOf.nodes)return;let t,n,r=this.getIterator();for(;this.indexes[r]<this.proxyOf.nodes.length&&(t=this.indexes[r],n=e(this.proxyOf.nodes[t],t),!1!==n);)this.indexes[r]+=1;return delete this.indexes[r],n}every(e){return this.nodes.every(e)}getIterator(){this.lastEach||(this.lastEach=0),this.indexes||(this.indexes={}),this.lastEach+=1;let e=this.lastEach;return this.indexes[e]=0,e}getProxyProcessor(){return{get:(e,t)=>"proxyOf"===t?e:e[t]?"each"===t||"string"==typeof t&&t.startsWith("walk")?(...n)=>e[t](...n.map((e=>"function"==typeof e?(t,n)=>e(t.toProxy(),n):e))):"every"===t||"some"===t?n=>e[t](((e,...t)=>n(e.toProxy(),...t))):"root"===t?()=>e.root().toProxy():"nodes"===t?e.nodes.map((e=>e.toProxy())):"first"===t||"last"===t?e[t].toProxy():e[t]:e[t],set:(e,t,n)=>(e[t]===n||(e[t]=n,"name"!==t&&"params"!==t&&"selector"!==t||e.markDirty()),!0)}}index(e){return"number"==typeof e?e:(e.proxyOf&&(e=e.proxyOf),this.proxyOf.nodes.indexOf(e))}insertAfter(e,t){let n,r=this.index(e),i=this.normalize(t,this.proxyOf.nodes[r]).reverse();r=this.index(e);for(let e of i)this.proxyOf.nodes.splice(r+1,0,e);for(let e in this.indexes)n=this.indexes[e],r<n&&(this.indexes[e]=n+i.length);return this.markDirty(),this}insertBefore(e,t){let n,r=this.index(e),i=0===r&&"prepend",o=this.normalize(t,this.proxyOf.nodes[r],i).reverse();r=this.index(e);for(let e of o)this.proxyOf.nodes.splice(r,0,e);for(let e in this.indexes)n=this.indexes[e],r<=n&&(this.indexes[e]=n+o.length);return this.markDirty(),this}normalize(e,t){if("string"==typeof e)e=f(i(e).nodes);else if(void 0===e)e=[];else if(Array.isArray(e)){e=e.slice(0);for(let t of e)t.parent&&t.parent.removeChild(t,"ignore")}else if("root"===e.type&&"document"!==this.type){e=e.nodes.slice(0);for(let t of e)t.parent&&t.parent.removeChild(t,"ignore")}else if(e.type)e=[e];else if(e.prop){if(void 0===e.value)throw new Error("Value field is missed in node creation");"string"!=typeof e.value&&(e.value=String(e.value)),e=[new l(e)]}else if(e.selector||e.selectors)e=[new s(e)];else if(e.name)e=[new r(e)];else{if(!e.text)throw new Error("Unknown node type in node creation");e=[new a(e)]}return e.map((e=>(e[h]||p.rebuild(e),(e=e.proxyOf).parent&&e.parent.removeChild(e),e[u]&&d(e),e.raws||(e.raws={}),void 0===e.raws.before&&t&&void 0!==t.raws.before&&(e.raws.before=t.raws.before.replace(/\S/g,"")),e.parent=this.proxyOf,e)))}prepend(...e){e=e.reverse();for(let t of e){let e=this.normalize(t,this.first,"prepend").reverse();for(let t of e)this.proxyOf.nodes.unshift(t);for(let t in this.indexes)this.indexes[t]=this.indexes[t]+e.length}return this.markDirty(),this}push(e){return e.parent=this,this.proxyOf.nodes.push(e),this}removeAll(){for(let e of this.proxyOf.nodes)e.parent=void 0;return this.proxyOf.nodes=[],this.markDirty(),this}removeChild(e){let t;e=this.index(e),this.proxyOf.nodes[e].parent=void 0,this.proxyOf.nodes.splice(e,1);for(let n in this.indexes)t=this.indexes[n],t>=e&&(this.indexes[n]=t-1);return this.markDirty(),this}replaceValues(e,t,n){return n||(n=t,t={}),this.walkDecls((r=>{t.props&&!t.props.includes(r.prop)||t.fast&&!r.value.includes(t.fast)||(r.value=r.value.replace(e,n))})),this.markDirty(),this}some(e){return this.nodes.some(e)}walk(e){return this.each(((t,n)=>{let r;try{r=e(t,n)}catch(e){throw t.addToError(e)}return!1!==r&&t.walk&&(r=t.walk(e)),r}))}walkAtRules(e,t){return t?e instanceof RegExp?this.walk(((n,r)=>{if("atrule"===n.type&&e.test(n.name))return t(n,r)})):this.walk(((n,r)=>{if("atrule"===n.type&&n.name===e)return t(n,r)})):(t=e,this.walk(((e,n)=>{if("atrule"===e.type)return t(e,n)})))}walkComments(e){return this.walk(((t,n)=>{if("comment"===t.type)return e(t,n)}))}walkDecls(e,t){return t?e instanceof RegExp?this.walk(((n,r)=>{if("decl"===n.type&&e.test(n.prop))return t(n,r)})):this.walk(((n,r)=>{if("decl"===n.type&&n.prop===e)return t(n,r)})):(t=e,this.walk(((e,n)=>{if("decl"===e.type)return t(e,n)})))}walkRules(e,t){return t?e instanceof RegExp?this.walk(((n,r)=>{if("rule"===n.type&&e.test(n.selector))return t(n,r)})):this.walk(((n,r)=>{if("rule"===n.type&&n.selector===e)return t(n,r)})):(t=e,this.walk(((e,n)=>{if("rule"===e.type)return t(e,n)})))}}p.registerParse=e=>{i=e},p.registerRule=e=>{s=e},p.registerAtRule=e=>{r=e},p.registerRoot=e=>{o=e},e.exports=p,p.default=p,p.rebuild=e=>{"atrule"===e.type?Object.setPrototypeOf(e,r.prototype):"rule"===e.type?Object.setPrototypeOf(e,s.prototype):"decl"===e.type?Object.setPrototypeOf(e,l.prototype):"comment"===e.type?Object.setPrototypeOf(e,a.prototype):"root"===e.type&&Object.setPrototypeOf(e,o.prototype),e[h]=!0,e.nodes&&e.nodes.forEach((e=>{p.rebuild(e)}))}},8149:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.encodeNonAsciiHTML=t.encodeHTML=void 0;var i=r(n(2125)),o=n(1560),s=/[\t\n!-,./:-@[-`\f{-}$\x80-\uFFFF]/g;function a(e,t){for(var n,r="",s=0;null!==(n=e.exec(t));){var a=n.index;r+=t.substring(s,a);var l=t.charCodeAt(a),c=i.default.get(l);if("object"==typeof c){if(a+1<t.length){var u=t.charCodeAt(a+1),h="number"==typeof c.n?c.n===u?c.o:void 0:c.n.get(u);if(void 0!==h){r+=h,s=e.lastIndex+=1;continue}}c=c.v}if(void 0!==c)r+=c,s=a+1;else{var f=(0,o.getCodePoint)(t,a);r+="&#x".concat(f.toString(16),";"),s=e.lastIndex+=Number(f!==l)}}return r+t.substr(s)}t.encodeHTML=function(e){return a(s,e)},t.encodeNonAsciiHTML=function(e){return a(o.xmlReplacer,e)}},8287:(e,t,n)=>{"use strict";var r=n(7526),i=n(251),o=n(4634);
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */function s(){return l.TYPED_ARRAY_SUPPORT?**********:**********}function a(e,t){if(s()<t)throw new RangeError("Invalid typed array length");return l.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=l.prototype:(null===e&&(e=new l(t)),e.length=t),e}function l(e,t,n){if(!(l.TYPED_ARRAY_SUPPORT||this instanceof l))return new l(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return h(this,e)}return c(this,e,t,n)}function c(e,t,n,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,n,r){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");t=void 0===n&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,n):new Uint8Array(t,n,r);l.TYPED_ARRAY_SUPPORT?(e=t).__proto__=l.prototype:e=f(e,t);return e}(e,t,n,r):"string"==typeof t?function(e,t,n){"string"==typeof n&&""!==n||(n="utf8");if(!l.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|p(t,n);e=a(e,r);var i=e.write(t,n);i!==r&&(e=e.slice(0,i));return e}(e,t,n):function(e,t){if(l.isBuffer(t)){var n=0|d(t.length);return 0===(e=a(e,n)).length||t.copy(e,0,0,n),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(r=t.length)!=r?a(e,0):f(e,t);if("Buffer"===t.type&&o(t.data))return f(e,t.data)}var r;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function u(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function h(e,t){if(u(t),e=a(e,t<0?0:0|d(t)),!l.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function f(e,t){var n=t.length<0?0:0|d(t.length);e=a(e,n);for(var r=0;r<n;r+=1)e[r]=255&t[r];return e}function d(e){if(e>=s())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+s().toString(16)+" bytes");return 0|e}function p(e,t){if(l.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return H(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return U(e).length;default:if(r)return H(e).length;t=(""+t).toLowerCase(),r=!0}}function m(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return N(this,t,n);case"utf8":case"utf-8":return S(this,t,n);case"ascii":return k(this,t,n);case"latin1":case"binary":return O(this,t,n);case"base64":return A(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return D(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function g(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function y(e,t,n,r,i){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(i)return-1;n=e.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof t&&(t=l.from(t,r)),l.isBuffer(t))return 0===t.length?-1:v(e,t,n,r,i);if("number"==typeof t)return t&=255,l.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):v(e,[t],n,r,i);throw new TypeError("val must be string, number or Buffer")}function v(e,t,n,r,i){var o,s=1,a=e.length,l=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;s=2,a/=2,l/=2,n/=2}function c(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(i){var u=-1;for(o=n;o<a;o++)if(c(e,o)===c(t,-1===u?0:o-u)){if(-1===u&&(u=o),o-u+1===l)return u*s}else-1!==u&&(o-=o-u),u=-1}else for(n+l>a&&(n=a-l),o=n;o>=0;o--){for(var h=!0,f=0;f<l;f++)if(c(e,o+f)!==c(t,f)){h=!1;break}if(h)return o}return-1}function b(e,t,n,r){n=Number(n)||0;var i=e.length-n;r?(r=Number(r))>i&&(r=i):r=i;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");r>o/2&&(r=o/2);for(var s=0;s<r;++s){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))return s;e[n+s]=a}return s}function w(e,t,n,r){return F(H(t,e.length-n),e,n,r)}function x(e,t,n,r){return F(function(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(t),e,n,r)}function _(e,t,n,r){return x(e,t,n,r)}function T(e,t,n,r){return F(U(t),e,n,r)}function E(e,t,n,r){return F(function(e,t){for(var n,r,i,o=[],s=0;s<e.length&&!((t-=2)<0);++s)r=(n=e.charCodeAt(s))>>8,i=n%256,o.push(i),o.push(r);return o}(t,e.length-n),e,n,r)}function A(e,t,n){return 0===t&&n===e.length?r.fromByteArray(e):r.fromByteArray(e.slice(t,n))}function S(e,t,n){n=Math.min(e.length,n);for(var r=[],i=t;i<n;){var o,s,a,l,c=e[i],u=null,h=c>239?4:c>223?3:c>191?2:1;if(i+h<=n)switch(h){case 1:c<128&&(u=c);break;case 2:128==(192&(o=e[i+1]))&&(l=(31&c)<<6|63&o)>127&&(u=l);break;case 3:o=e[i+1],s=e[i+2],128==(192&o)&&128==(192&s)&&(l=(15&c)<<12|(63&o)<<6|63&s)>2047&&(l<55296||l>57343)&&(u=l);break;case 4:o=e[i+1],s=e[i+2],a=e[i+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&(l=(15&c)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&l<1114112&&(u=l)}null===u?(u=65533,h=1):u>65535&&(u-=65536,r.push(u>>>10&1023|55296),u=56320|1023&u),r.push(u),i+=h}return function(e){var t=e.length;if(t<=C)return String.fromCharCode.apply(String,e);var n="",r=0;for(;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=C));return n}(r)}t.hp=l,t.IS=50,l.TYPED_ARRAY_SUPPORT=void 0!==n.g.TYPED_ARRAY_SUPPORT?n.g.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),s(),l.poolSize=8192,l._augment=function(e){return e.__proto__=l.prototype,e},l.from=function(e,t,n){return c(null,e,t,n)},l.TYPED_ARRAY_SUPPORT&&(l.prototype.__proto__=Uint8Array.prototype,l.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&l[Symbol.species]===l&&Object.defineProperty(l,Symbol.species,{value:null,configurable:!0})),l.alloc=function(e,t,n){return function(e,t,n,r){return u(t),t<=0?a(e,t):void 0!==n?"string"==typeof r?a(e,t).fill(n,r):a(e,t).fill(n):a(e,t)}(null,e,t,n)},l.allocUnsafe=function(e){return h(null,e)},l.allocUnsafeSlow=function(e){return h(null,e)},l.isBuffer=function(e){return!(null==e||!e._isBuffer)},l.compare=function(e,t){if(!l.isBuffer(e)||!l.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,i=0,o=Math.min(n,r);i<o;++i)if(e[i]!==t[i]){n=e[i],r=t[i];break}return n<r?-1:r<n?1:0},l.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},l.concat=function(e,t){if(!o(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return l.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=l.allocUnsafe(t),i=0;for(n=0;n<e.length;++n){var s=e[n];if(!l.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(r,i),i+=s.length}return r},l.byteLength=p,l.prototype._isBuffer=!0,l.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},l.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},l.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},l.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?S(this,0,e):m.apply(this,arguments)},l.prototype.equals=function(e){if(!l.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===l.compare(this,e)},l.prototype.inspect=function(){var e="",n=t.IS;return this.length>0&&(e=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(e+=" ... ")),"<Buffer "+e+">"},l.prototype.compare=function(e,t,n,r,i){if(!l.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),t<0||n>e.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&t>=n)return 0;if(r>=i)return-1;if(t>=n)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(r>>>=0),s=(n>>>=0)-(t>>>=0),a=Math.min(o,s),c=this.slice(r,i),u=e.slice(t,n),h=0;h<a;++h)if(c[h]!==u[h]){o=c[h],s=u[h];break}return o<s?-1:s<o?1:0},l.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},l.prototype.indexOf=function(e,t,n){return y(this,e,t,n,!0)},l.prototype.lastIndexOf=function(e,t,n){return y(this,e,t,n,!1)},l.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-t;if((void 0===n||n>i)&&(n=i),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return b(this,e,t,n);case"utf8":case"utf-8":return w(this,e,t,n);case"ascii":return x(this,e,t,n);case"latin1":case"binary":return _(this,e,t,n);case"base64":return T(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return E(this,e,t,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},l.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var C=4096;function k(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(127&e[i]);return r}function O(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(e[i]);return r}function N(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var i="",o=t;o<n;++o)i+=$(e[o]);return i}function D(e,t,n){for(var r=e.slice(t,n),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function L(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function P(e,t,n,r,i,o){if(!l.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function I(e,t,n,r){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-n,2);i<o;++i)e[n+i]=(t&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function j(e,t,n,r){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-n,4);i<o;++i)e[n+i]=t>>>8*(r?i:3-i)&255}function M(e,t,n,r,i,o){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function q(e,t,n,r,o){return o||M(e,0,n,4),i.write(e,t,n,r,23,4),n+4}function R(e,t,n,r,o){return o||M(e,0,n,8),i.write(e,t,n,r,52,8),n+8}l.prototype.slice=function(e,t){var n,r=this.length;if((e=~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),(t=void 0===t?r:~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e),l.TYPED_ARRAY_SUPPORT)(n=this.subarray(e,t)).__proto__=l.prototype;else{var i=t-e;n=new l(i,void 0);for(var o=0;o<i;++o)n[o]=this[o+e]}return n},l.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||L(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return r},l.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||L(e,t,this.length);for(var r=this[e+--t],i=1;t>0&&(i*=256);)r+=this[e+--t]*i;return r},l.prototype.readUInt8=function(e,t){return t||L(e,1,this.length),this[e]},l.prototype.readUInt16LE=function(e,t){return t||L(e,2,this.length),this[e]|this[e+1]<<8},l.prototype.readUInt16BE=function(e,t){return t||L(e,2,this.length),this[e]<<8|this[e+1]},l.prototype.readUInt32LE=function(e,t){return t||L(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},l.prototype.readUInt32BE=function(e,t){return t||L(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},l.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||L(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return r>=(i*=128)&&(r-=Math.pow(2,8*t)),r},l.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||L(e,t,this.length);for(var r=t,i=1,o=this[e+--r];r>0&&(i*=256);)o+=this[e+--r]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},l.prototype.readInt8=function(e,t){return t||L(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},l.prototype.readInt16LE=function(e,t){t||L(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},l.prototype.readInt16BE=function(e,t){t||L(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},l.prototype.readInt32LE=function(e,t){return t||L(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},l.prototype.readInt32BE=function(e,t){return t||L(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},l.prototype.readFloatLE=function(e,t){return t||L(e,4,this.length),i.read(this,e,!0,23,4)},l.prototype.readFloatBE=function(e,t){return t||L(e,4,this.length),i.read(this,e,!1,23,4)},l.prototype.readDoubleLE=function(e,t){return t||L(e,8,this.length),i.read(this,e,!0,52,8)},l.prototype.readDoubleBE=function(e,t){return t||L(e,8,this.length),i.read(this,e,!1,52,8)},l.prototype.writeUIntLE=function(e,t,n,r){(e=+e,t|=0,n|=0,r)||P(this,e,t,n,Math.pow(2,8*n)-1,0);var i=1,o=0;for(this[t]=255&e;++o<n&&(i*=256);)this[t+o]=e/i&255;return t+n},l.prototype.writeUIntBE=function(e,t,n,r){(e=+e,t|=0,n|=0,r)||P(this,e,t,n,Math.pow(2,8*n)-1,0);var i=n-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+n},l.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,1,255,0),l.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},l.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,2,65535,0),l.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):I(this,e,t,!0),t+2},l.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,2,65535,0),l.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):I(this,e,t,!1),t+2},l.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,4,4294967295,0),l.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):j(this,e,t,!0),t+4},l.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,4,4294967295,0),l.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):j(this,e,t,!1),t+4},l.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t|=0,!r){var i=Math.pow(2,8*n-1);P(this,e,t,n,i-1,-i)}var o=0,s=1,a=0;for(this[t]=255&e;++o<n&&(s*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s|0)-a&255;return t+n},l.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t|=0,!r){var i=Math.pow(2,8*n-1);P(this,e,t,n,i-1,-i)}var o=n-1,s=1,a=0;for(this[t+o]=255&e;--o>=0&&(s*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s|0)-a&255;return t+n},l.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,1,127,-128),l.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},l.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,2,32767,-32768),l.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):I(this,e,t,!0),t+2},l.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,2,32767,-32768),l.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):I(this,e,t,!1),t+2},l.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,4,**********,-2147483648),l.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):j(this,e,t,!0),t+4},l.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||P(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),l.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):j(this,e,t,!1),t+4},l.prototype.writeFloatLE=function(e,t,n){return q(this,e,t,!0,n)},l.prototype.writeFloatBE=function(e,t,n){return q(this,e,t,!1,n)},l.prototype.writeDoubleLE=function(e,t,n){return R(this,e,t,!0,n)},l.prototype.writeDoubleBE=function(e,t,n){return R(this,e,t,!1,n)},l.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var i,o=r-n;if(this===e&&n<t&&t<r)for(i=o-1;i>=0;--i)e[i+t]=this[i+n];else if(o<1e3||!l.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+o),t);return o},l.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!l.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;var o;if(t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0),"number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{var s=l.isBuffer(e)?e:H(new l(e,r).toString()),a=s.length;for(o=0;o<n-t;++o)this[o+t]=s[o%a]}return this};var B=/[^+\/0-9A-Za-z-_]/g;function $(e){return e<16?"0"+e.toString(16):e.toString(16)}function H(e,t){var n;t=t||1/0;for(var r=e.length,i=null,o=[],s=0;s<r;++s){if((n=e.charCodeAt(s))>55295&&n<57344){if(!i){if(n>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===r){(t-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&o.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function U(e){return r.toByteArray(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(B,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function F(e,t,n,r){for(var i=0;i<r&&!(i+n>=t.length||i>=e.length);++i)t[i+n]=e[i];return i}},8339:(e,t,n)=>{"use strict";let r=n(396),i=n(9371),o=n(5238),s=n(5644),a=n(1534),l=n(5781);const c={empty:!0,space:!0};e.exports=class{constructor(e){this.input=e,this.root=new s,this.current=this.root,this.spaces="",this.semicolon=!1,this.createTokenizer(),this.root.source={input:e,start:{column:1,line:1,offset:0}}}atrule(e){let t,n,i,o=new r;o.name=e[1].slice(1),""===o.name&&this.unnamedAtrule(o,e),this.init(o,e[2]);let s=!1,a=!1,l=[],c=[];for(;!this.tokenizer.endOfFile();){if(t=(e=this.tokenizer.nextToken())[0],"("===t||"["===t?c.push("("===t?")":"]"):"{"===t&&c.length>0?c.push("}"):t===c[c.length-1]&&c.pop(),0===c.length){if(";"===t){o.source.end=this.getPosition(e[2]),o.source.end.offset++,this.semicolon=!0;break}if("{"===t){a=!0;break}if("}"===t){if(l.length>0){for(i=l.length-1,n=l[i];n&&"space"===n[0];)n=l[--i];n&&(o.source.end=this.getPosition(n[3]||n[2]),o.source.end.offset++)}this.end(e);break}l.push(e)}else l.push(e);if(this.tokenizer.endOfFile()){s=!0;break}}o.raws.between=this.spacesAndCommentsFromEnd(l),l.length?(o.raws.afterName=this.spacesAndCommentsFromStart(l),this.raw(o,"params",l),s&&(e=l[l.length-1],o.source.end=this.getPosition(e[3]||e[2]),o.source.end.offset++,this.spaces=o.raws.between,o.raws.between="")):(o.raws.afterName="",o.params=""),a&&(o.nodes=[],this.current=o)}checkMissedSemicolon(e){let t=this.colon(e);if(!1===t)return;let n,r=0;for(let i=t-1;i>=0&&(n=e[i],"space"===n[0]||(r+=1,2!==r));i--);throw this.input.error("Missed semicolon","word"===n[0]?n[3]+1:n[2])}colon(e){let t,n,r,i=0;for(let[o,s]of e.entries()){if(n=s,r=n[0],"("===r&&(i+=1),")"===r&&(i-=1),0===i&&":"===r){if(t){if("word"===t[0]&&"progid"===t[1])continue;return o}this.doubleColon(n)}t=n}return!1}comment(e){let t=new i;this.init(t,e[2]),t.source.end=this.getPosition(e[3]||e[2]),t.source.end.offset++;let n=e[1].slice(2,-2);if(/^\s*$/.test(n))t.text="",t.raws.left=n,t.raws.right="";else{let e=n.match(/^(\s*)([^]*\S)(\s*)$/);t.text=e[2],t.raws.left=e[1],t.raws.right=e[3]}}createTokenizer(){this.tokenizer=l(this.input)}decl(e,t){let n=new o;this.init(n,e[0][2]);let r,i=e[e.length-1];for(";"===i[0]&&(this.semicolon=!0,e.pop()),n.source.end=this.getPosition(i[3]||i[2]||function(e){for(let t=e.length-1;t>=0;t--){let n=e[t],r=n[3]||n[2];if(r)return r}}(e)),n.source.end.offset++;"word"!==e[0][0];)1===e.length&&this.unknownWord(e),n.raws.before+=e.shift()[1];for(n.source.start=this.getPosition(e[0][2]),n.prop="";e.length;){let t=e[0][0];if(":"===t||"space"===t||"comment"===t)break;n.prop+=e.shift()[1]}for(n.raws.between="";e.length;){if(r=e.shift(),":"===r[0]){n.raws.between+=r[1];break}"word"===r[0]&&/\w/.test(r[1])&&this.unknownWord([r]),n.raws.between+=r[1]}"_"!==n.prop[0]&&"*"!==n.prop[0]||(n.raws.before+=n.prop[0],n.prop=n.prop.slice(1));let s,a=[];for(;e.length&&(s=e[0][0],"space"===s||"comment"===s);)a.push(e.shift());this.precheckMissedSemicolon(e);for(let t=e.length-1;t>=0;t--){if(r=e[t],"!important"===r[1].toLowerCase()){n.important=!0;let r=this.stringFrom(e,t);r=this.spacesFromEnd(e)+r," !important"!==r&&(n.raws.important=r);break}if("important"===r[1].toLowerCase()){let r=e.slice(0),i="";for(let e=t;e>0;e--){let t=r[e][0];if(i.trim().startsWith("!")&&"space"!==t)break;i=r.pop()[1]+i}i.trim().startsWith("!")&&(n.important=!0,n.raws.important=i,e=r)}if("space"!==r[0]&&"comment"!==r[0])break}e.some((e=>"space"!==e[0]&&"comment"!==e[0]))&&(n.raws.between+=a.map((e=>e[1])).join(""),a=[]),this.raw(n,"value",a.concat(e),t),n.value.includes(":")&&!t&&this.checkMissedSemicolon(e)}doubleColon(e){throw this.input.error("Double colon",{offset:e[2]},{offset:e[2]+e[1].length})}emptyRule(e){let t=new a;this.init(t,e[2]),t.selector="",t.raws.between="",this.current=t}end(e){this.current.nodes&&this.current.nodes.length&&(this.current.raws.semicolon=this.semicolon),this.semicolon=!1,this.current.raws.after=(this.current.raws.after||"")+this.spaces,this.spaces="",this.current.parent?(this.current.source.end=this.getPosition(e[2]),this.current.source.end.offset++,this.current=this.current.parent):this.unexpectedClose(e)}endFile(){this.current.parent&&this.unclosedBlock(),this.current.nodes&&this.current.nodes.length&&(this.current.raws.semicolon=this.semicolon),this.current.raws.after=(this.current.raws.after||"")+this.spaces,this.root.source.end=this.getPosition(this.tokenizer.position())}freeSemicolon(e){if(this.spaces+=e[1],this.current.nodes){let t=this.current.nodes[this.current.nodes.length-1];t&&"rule"===t.type&&!t.raws.ownSemicolon&&(t.raws.ownSemicolon=this.spaces,this.spaces="",t.source.end=this.getPosition(e[2]),t.source.end.offset+=t.raws.ownSemicolon.length)}}getPosition(e){let t=this.input.fromOffset(e);return{column:t.col,line:t.line,offset:e}}init(e,t){this.current.push(e),e.source={input:this.input,start:this.getPosition(t)},e.raws.before=this.spaces,this.spaces="","comment"!==e.type&&(this.semicolon=!1)}other(e){let t=!1,n=null,r=!1,i=null,o=[],s=e[1].startsWith("--"),a=[],l=e;for(;l;){if(n=l[0],a.push(l),"("===n||"["===n)i||(i=l),o.push("("===n?")":"]");else if(s&&r&&"{"===n)i||(i=l),o.push("}");else if(0===o.length){if(";"===n){if(r)return void this.decl(a,s);break}if("{"===n)return void this.rule(a);if("}"===n){this.tokenizer.back(a.pop()),t=!0;break}":"===n&&(r=!0)}else n===o[o.length-1]&&(o.pop(),0===o.length&&(i=null));l=this.tokenizer.nextToken()}if(this.tokenizer.endOfFile()&&(t=!0),o.length>0&&this.unclosedBracket(i),t&&r){if(!s)for(;a.length&&(l=a[a.length-1][0],"space"===l||"comment"===l);)this.tokenizer.back(a.pop());this.decl(a,s)}else this.unknownWord(a)}parse(){let e;for(;!this.tokenizer.endOfFile();)switch(e=this.tokenizer.nextToken(),e[0]){case"space":this.spaces+=e[1];break;case";":this.freeSemicolon(e);break;case"}":this.end(e);break;case"comment":this.comment(e);break;case"at-word":this.atrule(e);break;case"{":this.emptyRule(e);break;default:this.other(e)}this.endFile()}precheckMissedSemicolon(){}raw(e,t,n,r){let i,o,s,a,l=n.length,u="",h=!0;for(let e=0;e<l;e+=1)i=n[e],o=i[0],"space"!==o||e!==l-1||r?"comment"===o?(a=n[e-1]?n[e-1][0]:"empty",s=n[e+1]?n[e+1][0]:"empty",c[a]||c[s]||","===u.slice(-1)?h=!1:u+=i[1]):u+=i[1]:h=!1;if(!h){let r=n.reduce(((e,t)=>e+t[1]),"");e.raws[t]={raw:r,value:u}}e[t]=u}rule(e){e.pop();let t=new a;this.init(t,e[0][2]),t.raws.between=this.spacesAndCommentsFromEnd(e),this.raw(t,"selector",e),this.current=t}spacesAndCommentsFromEnd(e){let t,n="";for(;e.length&&(t=e[e.length-1][0],"space"===t||"comment"===t);)n=e.pop()[1]+n;return n}spacesAndCommentsFromStart(e){let t,n="";for(;e.length&&(t=e[0][0],"space"===t||"comment"===t);)n+=e.shift()[1];return n}spacesFromEnd(e){let t,n="";for(;e.length&&(t=e[e.length-1][0],"space"===t);)n=e.pop()[1]+n;return n}stringFrom(e,t){let n="";for(let r=t;r<e.length;r++)n+=e[r][1];return e.splice(t,e.length-t),n}unclosedBlock(){let e=this.current.source.start;throw this.input.error("Unclosed block",e.line,e.column)}unclosedBracket(e){throw this.input.error("Unclosed bracket",{offset:e[2]},{offset:e[2]+1})}unexpectedClose(e){throw this.input.error("Unexpected }",{offset:e[2]},{offset:e[2]+1})}unknownWord(e){throw this.input.error("Unknown word "+e[0][1],{offset:e[0][2]},{offset:e[0][2]+e[0][1].length})}unnamedAtrule(e,t){throw this.input.error("At-rule without name",{offset:t[2]},{offset:t[2]+t[1].length})}}},8633:e=>{var t=String,n=function(){return{isColorSupported:!1,reset:t,bold:t,dim:t,italic:t,underline:t,inverse:t,hidden:t,strikethrough:t,black:t,red:t,green:t,yellow:t,blue:t,magenta:t,cyan:t,white:t,gray:t,bgBlack:t,bgRed:t,bgGreen:t,bgYellow:t,bgBlue:t,bgMagenta:t,bgCyan:t,bgWhite:t,blackBright:t,redBright:t,greenBright:t,yellowBright:t,blueBright:t,magentaBright:t,cyanBright:t,whiteBright:t,bgBlackBright:t,bgRedBright:t,bgGreenBright:t,bgYellowBright:t,bgBlueBright:t,bgMagentaBright:t,bgCyanBright:t,bgWhiteBright:t}};e.exports=n(),e.exports.createColors=n},8682:(e,t)=>{"use strict";
/*!
 * is-plain-object <https://github.com/jonschlinkert/is-plain-object>
 *
 * Copyright (c) 2014-2017, Jon Schlinkert.
 * Released under the MIT License.
 */
function n(e){return"[object Object]"===Object.prototype.toString.call(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.isPlainObject=function(e){var t,r;return!1!==n(e)&&(void 0===(t=e.constructor)||!1!==n(r=t.prototype)&&!1!==r.hasOwnProperty("isPrototypeOf"))}},9079:function(e,t,n){"use strict";var r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},r.apply(this,arguments)},i=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,i)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&i(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.render=void 0;var a=s(n(5413)),l=n(6835),c=n(1019),u=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function h(e){return e.replace(/"/g,"&quot;")}var f=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function d(e,t){void 0===t&&(t={});for(var n=("length"in e?e:[e]),r="",i=0;i<n.length;i++)r+=p(n[i],t);return r}function p(e,t){switch(e.type){case a.Root:return d(e.children,t);case a.Doctype:case a.Directive:return"<".concat(e.data,">");case a.Comment:return function(e){return"\x3c!--".concat(e.data,"--\x3e")}(e);case a.CDATA:return function(e){return"<![CDATA[".concat(e.children[0].data,"]]>")}(e);case a.Script:case a.Style:case a.Tag:return function(e,t){var n;"foreign"===t.xmlMode&&(e.name=null!==(n=c.elementNames.get(e.name))&&void 0!==n?n:e.name,e.parent&&m.has(e.parent.name)&&(t=r(r({},t),{xmlMode:!1})));!t.xmlMode&&g.has(e.name)&&(t=r(r({},t),{xmlMode:"foreign"}));var i="<".concat(e.name),o=function(e,t){var n;if(e){var r=!1===(null!==(n=t.encodeEntities)&&void 0!==n?n:t.decodeEntities)?h:t.xmlMode||"utf8"!==t.encodeEntities?l.encodeXML:l.escapeAttribute;return Object.keys(e).map((function(n){var i,o,s=null!==(i=e[n])&&void 0!==i?i:"";return"foreign"===t.xmlMode&&(n=null!==(o=c.attributeNames.get(n))&&void 0!==o?o:n),t.emptyAttrs||t.xmlMode||""!==s?"".concat(n,'="').concat(r(s),'"'):n})).join(" ")}}(e.attribs,t);o&&(i+=" ".concat(o));0===e.children.length&&(t.xmlMode?!1!==t.selfClosingTags:t.selfClosingTags&&f.has(e.name))?(t.xmlMode||(i+=" "),i+="/>"):(i+=">",e.children.length>0&&(i+=d(e.children,t)),!t.xmlMode&&f.has(e.name)||(i+="</".concat(e.name,">")));return i}(e,t);case a.Text:return function(e,t){var n,r=e.data||"";!1===(null!==(n=t.encodeEntities)&&void 0!==n?n:t.decodeEntities)||!t.xmlMode&&e.parent&&u.has(e.parent.name)||(r=t.xmlMode||"utf8"!==t.encodeEntities?(0,l.encodeXML)(r):(0,l.escapeText)(r));return r}(e,t)}}t.render=d,t.default=d;var m=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),g=new Set(["svg","math"])},9124:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.getOuterHTML=a,t.getInnerHTML=function(e,t){return(0,i.hasChildren)(e)?e.children.map((function(e){return a(e,t)})).join(""):""},t.getText=function e(t){return Array.isArray(t)?t.map(e).join(""):(0,i.isTag)(t)?"br"===t.name?"\n":e(t.children):(0,i.isCDATA)(t)?e(t.children):(0,i.isText)(t)?t.data:""},t.textContent=function e(t){if(Array.isArray(t))return t.map(e).join("");if((0,i.hasChildren)(t)&&!(0,i.isComment)(t))return e(t.children);return(0,i.isText)(t)?t.data:""},t.innerText=function e(t){if(Array.isArray(t))return t.map(e).join("");if((0,i.hasChildren)(t)&&(t.type===s.ElementType.Tag||(0,i.isCDATA)(t)))return e(t.children);return(0,i.isText)(t)?t.data:""};var i=n(4128),o=r(n(9079)),s=n(5413);function a(e,t){return(0,o.default)(e,t)}},9371:(e,t,n)=>{"use strict";let r=n(3152);class i extends r{constructor(e){super(e),this.type="comment"}}e.exports=i,i.default=i},9466:function(e,t){var n,r,i;r=[],void 0===(i="function"==typeof(n=function(){return function(e){function t(e){return" "===e||"\t"===e||"\n"===e||"\f"===e||"\r"===e}function n(t){var n,r=t.exec(e.substring(m));if(r)return n=r[0],m+=n.length,n}for(var r,i,o,s,a,l=e.length,c=/^[ \t\n\r\u000c]+/,u=/^[, \t\n\r\u000c]+/,h=/^[^ \t\n\r\u000c]+/,f=/[,]+$/,d=/^\d+$/,p=/^-?(?:[0-9]+|[0-9]*\.[0-9]+)(?:[eE][+-]?[0-9]+)?$/,m=0,g=[];;){if(n(u),m>=l)return g;r=n(h),i=[],","===r.slice(-1)?(r=r.replace(f,""),v()):y()}function y(){for(n(c),o="",s="in descriptor";;){if(a=e.charAt(m),"in descriptor"===s)if(t(a))o&&(i.push(o),o="",s="after descriptor");else{if(","===a)return m+=1,o&&i.push(o),void v();if("("===a)o+=a,s="in parens";else{if(""===a)return o&&i.push(o),void v();o+=a}}else if("in parens"===s)if(")"===a)o+=a,s="in descriptor";else{if(""===a)return i.push(o),void v();o+=a}else if("after descriptor"===s)if(t(a));else{if(""===a)return void v();s="in descriptor",m-=1}m+=1}}function v(){var t,n,o,s,a,l,c,u,h,f=!1,m={};for(s=0;s<i.length;s++)l=(a=i[s])[a.length-1],c=a.substring(0,a.length-1),u=parseInt(c,10),h=parseFloat(c),d.test(c)&&"w"===l?((t||n)&&(f=!0),0===u?f=!0:t=u):p.test(c)&&"x"===l?((t||n||o)&&(f=!0),h<0?f=!0:n=h):d.test(c)&&"h"===l?((o||n)&&(f=!0),0===u?f=!0:o=u):f=!0;f?console&&console.log&&console.log("Invalid srcset descriptor found in '"+e+"' at '"+a+"'."):(m.url=r,t&&(m.w=t),n&&(m.d=n),o&&(m.h=o),g.push(m))}}})?n.apply(t,r):n)||(e.exports=i)},9577:(e,t,n)=>{"use strict";let r=n(7793),i=n(1106),o=n(8339);function s(e,t){let n=new i(e,t),r=new o(n);try{r.parse()}catch(e){throw e}return r.root}e.exports=s,s.default=s,r.registerParse(s)},9746:()=>{},9977:()=>{}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r].call(o.exports,o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,r(i.key),i)}}function r(t){var n=function(t,n){if("object"!=e(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,n||"default");if("object"!=e(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(t)}(t,"string");return"symbol"==e(n)?n:n+""}var i=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},(n=[{key:"init",value:function(){$(document).on("change","#address_id",(function(e){"new"!==$(e.currentTarget).val()?($(".address-item-selected").removeClass("d-none").html($(".list-available-address .address-item-wrapper[data-id=".concat($(e.currentTarget).val(),"]")).html()),$(".address-form-wrapper").addClass("d-none")):($(".address-item-selected").addClass("d-none"),$(".address-form-wrapper").removeClass("d-none"))})),$(document).on("click","#create_account",(function(e){$(e.currentTarget).is(":checked")?$(".password-group").removeClass("d-none").fadeIn():$(".password-group").addClass("d-none").fadeOut()})),$(document).on("click","#billing_address_same_as_shipping_address",(function(e){$(e.currentTarget).is(":checked")?$(".billing-address-form-wrapper").addClass("d-none").fadeOut():$(".billing-address-form-wrapper").removeClass("d-none").fadeIn()}))}}])&&t(e.prototype,n),r&&t(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,r}(),o=n(4728),s=n.n(o);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function l(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */l=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,i=Object.defineProperty||function(e,t,n){e[t]=n.value},o="function"==typeof Symbol?Symbol:{},s=o.iterator||"@@iterator",c=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function h(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{h({},"")}catch(e){h=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var o=t&&t.prototype instanceof b?t:b,s=Object.create(o.prototype),a=new L(r||[]);return i(s,"_invoke",{value:k(e,n,a)}),s}function d(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var p="suspendedStart",m="suspendedYield",g="executing",y="completed",v={};function b(){}function w(){}function x(){}var _={};h(_,s,(function(){return this}));var T=Object.getPrototypeOf,E=T&&T(T(P([])));E&&E!==n&&r.call(E,s)&&(_=E);var A=x.prototype=b.prototype=Object.create(_);function S(e){["next","throw","return"].forEach((function(t){h(e,t,(function(e){return this._invoke(t,e)}))}))}function C(e,t){function n(i,o,s,l){var c=d(e[i],e,o);if("throw"!==c.type){var u=c.arg,h=u.value;return h&&"object"==a(h)&&r.call(h,"__await")?t.resolve(h.__await).then((function(e){n("next",e,s,l)}),(function(e){n("throw",e,s,l)})):t.resolve(h).then((function(e){u.value=e,s(u)}),(function(e){return n("throw",e,s,l)}))}l(c.arg)}var o;i(this,"_invoke",{value:function(e,r){function i(){return new t((function(t,i){n(e,r,t,i)}))}return o=o?o.then(i,i):i()}})}function k(t,n,r){var i=p;return function(o,s){if(i===g)throw Error("Generator is already running");if(i===y){if("throw"===o)throw s;return{value:e,done:!0}}for(r.method=o,r.arg=s;;){var a=r.delegate;if(a){var l=O(a,r);if(l){if(l===v)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(i===p)throw i=y,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i=g;var c=d(t,n,r);if("normal"===c.type){if(i=r.done?y:m,c.arg===v)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(i=y,r.method="throw",r.arg=c.arg)}}}function O(t,n){var r=n.method,i=t.iterator[r];if(i===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,O(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),v;var o=d(i,t.iterator,n.arg);if("throw"===o.type)return n.method="throw",n.arg=o.arg,n.delegate=null,v;var s=o.arg;return s?s.done?(n[t.resultName]=s.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,v):s:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,v)}function N(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function D(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function L(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(N,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[s];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var i=-1,o=function n(){for(;++i<t.length;)if(r.call(t,i))return n.value=t[i],n.done=!1,n;return n.value=e,n.done=!0,n};return o.next=o}}throw new TypeError(a(t)+" is not iterable")}return w.prototype=x,i(A,"constructor",{value:x,configurable:!0}),i(x,"constructor",{value:w,configurable:!0}),w.displayName=h(x,u,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===w||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,x):(e.__proto__=x,h(e,u,"GeneratorFunction")),e.prototype=Object.create(A),e},t.awrap=function(e){return{__await:e}},S(C.prototype),h(C.prototype,c,(function(){return this})),t.AsyncIterator=C,t.async=function(e,n,r,i,o){void 0===o&&(o=Promise);var s=new C(f(e,n,r,i),o);return t.isGeneratorFunction(n)?s:s.next().then((function(e){return e.done?e.value:s.next()}))},S(A),h(A,u,"Generator"),h(A,s,(function(){return this})),h(A,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,L.prototype={constructor:L,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(D),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function i(r,i){return a.type="throw",a.arg=t,n.next=r,i&&(n.method="next",n.arg=e),!!i}for(var o=this.tryEntries.length-1;o>=0;--o){var s=this.tryEntries[o],a=s.completion;if("root"===s.tryLoc)return i("end");if(s.tryLoc<=this.prev){var l=r.call(s,"catchLoc"),c=r.call(s,"finallyLoc");if(l&&c){if(this.prev<s.catchLoc)return i(s.catchLoc,!0);if(this.prev<s.finallyLoc)return i(s.finallyLoc)}else if(l){if(this.prev<s.catchLoc)return i(s.catchLoc,!0)}else{if(!c)throw Error("try statement without catch or finally");if(this.prev<s.finallyLoc)return i(s.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var i=this.tryEntries[n];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var o=i;break}}o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc&&(o=null);var s=o?o.completion:{};return s.type=e,s.arg=t,o?(this.method="next",this.next=o.finallyLoc,v):this.complete(s)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),v},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),D(n),v}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;D(n)}return i}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),v}},t}function c(e,t,n,r,i,o,s){try{var a=e[o](s),l=a.value}catch(e){return void n(e)}a.done?t(l):Promise.resolve(l).then(r,i)}function u(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,h(r.key),r)}}function h(e){var t=function(e,t){if("object"!=a(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=a(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==a(t)?t:t+""}var f=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)},t=[{key:"init",value:function(){$(document).on("click",".btn-open-coupon-form",(function(e){e.preventDefault(),$(document).find(".coupon-wrapper").toggle()})),$(".coupon-wrapper .coupon-code").keypress((function(e){if(13===e.keyCode)return $(".apply-coupon-code").trigger("click"),e.preventDefault(),e.stopPropagation(),!1}));var e="#main-checkout-product-info";$(document).on("click",".apply-coupon-code",(function(t){t.preventDefault();var n=$(t.currentTarget);$.ajax({url:n.data("url"),type:"POST",data:{coupon_code:n.closest(".coupon-wrapper").find(".coupon-code").val(),token:$("#checkout-token").val()},headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")},beforeSend:function(){n.find("i").remove(),n.html('<i class="fa fa-spin fa-spinner"></i> '.concat(n.html()))},success:function(t){var r=t.error,i=t.message;r?($(".coupon-error-msg .text-danger").html(s()(i)),n.find("i").remove()):$(e).load("".concat(window.location.href,"?applied_coupon=1 ").concat(e," > *"),(function(){return n.find("i").remove()})),$("html, body").animate({scrollTop:$(".coupon-wrapper").offset().top})},error:function(e){void 0!==e.responseJSON?"undefined"!==e.responseJSON.errors?$.each(e.responseJSON.errors,(function(e,t){$.each(t,(function(e,t){$(".coupon-error-msg .text-danger").text(t)}))})):void 0!==e.responseJSON.message&&$(".coupon-error-msg .text-danger").text(e.responseJSON.message):$(".coupon-error-msg .text-danger").text(e.status.text),n.find("i").remove()}})})),$(document).on("click",".remove-coupon-code",(function(t){t.preventDefault();var n=$(t.currentTarget);n.find("i").remove(),n.html('<i class="fa fa-spin fa-spinner"></i> '+n.html()),$.ajax({url:n.data("url"),type:"POST",data:{token:$("#checkout-token").val()},headers:{"X-CSRF-TOKEN":$('meta[name="csrf-token"]').attr("content")},success:function(t){t.error?($(".coupon-error-msg .text-danger").text(t.message),n.find("i").remove()):$(e).load(window.location.href+" "+e+" > *",(function(){n.find("i").remove()}))},error:function(e){void 0!==e.responseJSON?"undefined"!==e.responseJSON.errors?$.each(e.responseJSON.errors,(function(e,t){$.each(t,(function(e,t){$(".coupon-error-msg .text-danger").text(t)}))})):void 0!==e.responseJSON.message&&$(".coupon-error-msg .text-danger").text(e.responseJSON.message):$(".coupon-error-msg .text-danger").text(e.status.text),n.find("i").remove()}})})),$(document).on("click",'[data-bb-toggle="apply-coupon-code"]',function(){var e,t=(e=l().mark((function e(t){var n;return l().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.preventDefault(),$(this).find("i").remove(),$(this).html('<i class="fa fa-spin fa-spinner"></i> '.concat($(this).html())),$(document).find(".remove-coupon-code").length&&$(document).find(".remove-coupon-code").trigger("click"),n=$(this).data("discount-code"),$(document).find(".coupon-wrapper").show(),$(".coupon-wrapper .coupon-code").val(n),$(".apply-coupon-code").trigger("click"),$(this).find("i").remove();case 9:case"end":return e.stop()}}),e,this)})),function(){var t=this,n=arguments;return new Promise((function(r,i){var o=e.apply(t,n);function s(e){c(o,r,i,s,a,"next",e)}function a(e){c(o,r,i,s,a,"throw",e)}s(void 0)}))});return function(e){return t.apply(this,arguments)}}())}}],t&&u(e.prototype,t),n&&u(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t,n}();function d(e){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d(e)}function p(e,t,n){return(t=g(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,g(r.key),r)}}function g(e){var t=function(e,t){if("object"!=d(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=d(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==d(t)?t:t+""}try{window.$=window.jQuery=n(4692),n(454)}catch(e){}var y=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),(new i).init(),(new f).init()}return t=e,n=[{key:"init",value:function(){var t=$("form.checkout-form"),n=function(e){var n=new FormData(t.get(0));for(var r in e)n.set(r,e[r]);$.ajax({url:t.data("update-url"),method:"POST",processData:!1,contentType:!1,data:n,beforeSend:function(){$(".payment-info-loading").show(),$(".payment-checkout-btn").prop("disabled",!0),$(".shipping-info-loading").show()},success:function(e){var t=e.data;$(".cart-item-wrapper").html(t.amount),$('[data-bb-toggle="checkout-payment-methods-area"]').html(t.payment_methods),$('[data-bb-toggle="checkout-shipping-methods-area"]').html(t.shipping_methods)},complete:function(){$(".payment-info-loading").hide(),$(".payment-checkout-btn").prop("disabled",!1),document.dispatchEvent(new CustomEvent("payment-form-reloaded")),$(".shipping-info-loading").hide()}})};$(document).on("change","input.shipping_method_input",(function(e){var t={};if($(".checkout-products-marketplace").length){var r=$("#main-checkout-product-info").find("input.shipping_method_input");r.length&&r.map((function(e,n){var r=$(n).filter(":checked").val(),i=$(n).data("id");r&&(t["shipping_method[".concat(i,"]")]=r,t["shipping_option[".concat(i,"]")]=$(n).data("option"))}))}else{var i=$(e.currentTarget);$("input[name=shipping_option]").val(i.data("option")),$(".mobile-total").text("...");var o={shipping_method:i.val(),shipping_option:i.data("option"),payment_method:"",address:{address_id:$("#address_id").val()}},s=$(document).find("input[name=payment_method]:checked").first();s.length&&(o.payment_method=s.val())}n(t)})),$(document).on("change","input[name=payment_method]",(function(e){n({payment_method:$(e.target).val()})})),t.find(".list-customer-address").length&&n();var r=function(t){var r=$(t.currentTarget);r.closest(".form-group").find(".text-danger").remove();var i=r.closest("form");(function(){var e=$("#address_id").val();if(e&&"new"!==e)return!0;var t=!0;return $.each($(document).find(".form-control[required]"),(function(e,n){$(n).val()&&"null"!==$(n).val()||(t=!1)})),t})()&&i.valid&&i.valid()&&$.ajax({type:"POST",cache:!1,url:$("#save-shipping-information-url").val(),data:new FormData(i[0]),contentType:!1,processData:!1,success:function(e){!e.error&&/country|state|city|address/.test($(t.target).prop("name"))&&n()},error:function(t){e.handleError(t,i)}})};$(document).on("change","#address_country, #address_state, #address_city, #address_zip_code",(function(t){var n=$(t.currentTarget).closest("form");$.ajax({type:"POST",cache:!1,url:$("#update-checkout-tax-url").val(),data:new FormData(n[0]),contentType:!1,processData:!1,success:function(e){var t=e.data;$(".cart-item-wrapper").html(t.amount)},error:function(t){e.handleError(t,n)}})})),$(document).on("change","".concat(".customer-address-payment-form .address-form-wrapper"," .form-control"),(function(e){r(e)})),$(document).on("change",".list-customer-address .form-control",(function(e){r(e)})),$(document).on("change","".concat(".customer-billing-address-form"," #billing_address_same_as_shipping_address"),(function(e){$(e.currentTarget).find(":selected").val()?$(".billing-address-form-wrapper").hide():$(".billing-address-form-wrapper").show()})),$(document).on("change","".concat(".customer-tax-information-form"," #with_tax_information"),(function(e){var t=$(e.currentTarget);$(".tax-information-form-wrapper").toggle(t.is(":checked"))}));var i=$("#address_id");i.length&&i.val()&&"new"!==i.val()&&i.trigger("change"),$(document).on("click",'[data-bb-toggle="decrease-qty"]',(function(e){var t=$(e.currentTarget).parent().find("input"),n=parseInt(t.val())-1;n<1||(n=n<1?1:n,t.val(n),t.trigger("change"))})).on("click",'[data-bb-toggle="increase-qty"]',(function(e){var t=$(e.currentTarget).parent().find("input"),n=t.prop("max");n&&parseInt(t.val())>=parseInt(n)||(t.val(parseInt(t.val())+1),t.trigger("change"))})).on("change",'[data-bb-toggle="update-cart"]',(function(t){var r=$(t.currentTarget),i=r.parent(),o=r.prop("name"),s=r.val(),a=i.data("row-id");$.ajax({type:"POST",url:i.data("url"),data:p(p({_token:$('meta[name="csrf-token"]').prop("content")},o,s),"items[".concat(a,"][rowId]"),a),success:function(t){var r=t.error,i=t.message;t.data,r&&e.showError(i),n()},error:function(t){e.handleError(t)}})}))}}],r=[{key:"showNotice",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";if(toastr.clear(),toastr.options={closeButton:!0,positionClass:"toast-bottom-right",onclick:null,showDuration:1e3,hideDuration:1e3,timeOut:1e4,extendedTimeOut:1e3,showEasing:"swing",hideEasing:"linear",showMethod:"fadeIn",hideMethod:"fadeOut"},!n)switch(e){case"error":n=window.messages.error_header;break;case"success":n=window.messages.success_header}toastr[e](t,n)}},{key:"handleError",value:function(t,n){void 0===t.errors||_.isArray(t.errors)?void 0!==t.responseJSON?void 0!==t.responseJSON.errors?422===t.status&&e.handleValidationError(t.responseJSON.errors,n):void 0!==t.responseJSON.message?e.showError(t.responseJSON.message):$.each(t.responseJSON,(function(t,n){$.each(n,(function(t,n){e.showError(n)}))})):e.showError(t.statusText):e.handleValidationError(t.errors,n)}},{key:"dotArrayToJs",value:function(e){var t=e.split(".");return 1===t.length?e:t[0]+"["+t.splice(1).join("][")+"]"}},{key:"handleValidationError",value:function(t,n){$.each(t,(function(t,r){var i=e.dotArrayToJs(t),o=$('*[name="'.concat(i,'"]'));n&&(o=n.find('[name="'.concat(i,'"]'))),o.closest(".form-group").length?o.closest(".form-group").addClass("field-is-invalid"):o.addClass("field-is-invalid"),o.hasClass("form-control")&&(o.addClass("is-invalid"),o.is("select")&&o.closest(".select--arrow").length?(o.closest(".select--arrow").addClass("is-invalid"),o.closest(".select--arrow").after('<div class="invalid-feedback">'.concat(r,"</div>"))):o.after('<div class="invalid-feedback">'.concat(r,"</div>")))})),t[0]&&e.showError(t[0])}},{key:"showError",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";this.showNotice("error",e,t)}},{key:"showSuccess",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";this.showNotice("success",e,t)}}],n&&m(t.prototype,n),r&&m(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,r}();$((function(){(new y).init(),window.MainCheckout=y}))})()})();
//# sourceMappingURL=checkout.js.map