{"version": 3, "file": "/vendor/core/plugins/sale-popup/js/sale-popup.js", "mappings": "AA4GAA,EAAEC,UAAUC,OAAM,WACd,IAAMC,EAAkBH,EAAE,mCAEtBG,EAAgBC,QAChBC,YAAW,WACPF,EAAgBG,YAAY,UAG5B,IAAMC,EAAMJ,EAAgBK,KAAK,WAEjCC,MAAMF,EAAK,CACPG,OAAQ,MACRC,QAAS,CACL,eAAgB,mBAChB,OAAU,sBAGbC,MAAK,SAAAC,GAEF,IAAKA,EAASC,GACV,MAAM,IAAIC,MAAM,+BAEpB,OAAOF,EAASG,MACpB,IACCJ,MAAK,SAAAK,GAAc,IAAXT,EAAIS,EAAJT,KAELL,EAAgBe,KAAKV,QAEiB,IAA3BW,MAAMC,kBACbD,MAAMC,iBAAiBC,SAzI9B,SAAUlB,GACvB,KAAIH,EAAEsB,QAAQC,QAAU,KAAxB,CAIA,IAAMC,EAAMrB,EAAgBK,KAAK,OAEjC,QAAYiB,IAARD,EAAJ,CAIA,IAUIE,EACAC,EAXEC,EAAQJ,EAAII,MAAQ,EACpBC,EAAYL,EAAIM,QAChBC,EAAWC,KAAKC,MAAMjC,EAAE,qBAAqBkB,QAC7CgB,EAASV,EAAIjB,IACb4B,EAAWX,EAAIY,MACfC,EAAQb,EAAIc,GACZC,EAAcP,KAAKC,MAAMjC,EAAE,wBAAwBkB,QACnDsB,EAAUR,KAAKC,MAAMjC,EAAE,oBAAoBkB,QAC3CuB,EAAUjB,EAAIiB,QACdC,EAAYlB,EAAIkB,UAAUD,GAI1BE,EAAe3C,EAAE,sBACjB4C,EAAgB5C,EAAE,oBAClB6C,EAAiB7C,EAAE,qBACnB8C,EAAoB9C,EAAE,2BACtB+C,EAAmB/C,EAAE,sBACrBgD,EAAqBhD,EAAE,0BAEzBiD,EAAQ,EAENC,EAAMhB,EAAO9B,OAAS,EACtB+C,EAAOZ,EAAYnC,OAAS,EAC5BgD,EAAOZ,EAAQpC,OAAS,EACxBiD,EAAW7B,EAAI6B,SAAW7B,EAAI8B,aAC9BC,EAAW/B,EAAI+B,SAAW/B,EAAIgC,aAEhCC,EAAe,SAAUC,EAAKR,GAC9B,OAAOS,KAAKC,MAAMD,KAAKE,UAAYX,EAAMQ,EAAM,IAAMA,CACzD,EAEII,EAAa,SAAUb,GACvB,IAAIc,EAAM5B,EAASc,GAEnBN,EAAaqB,KAAK,MAAOD,GAAKC,KAAK,SAAUD,GAE7ClB,EAAeoB,KAAKlC,EAASkB,IAE7BL,EAAcoB,KAAK,OAAQ9B,EAAOe,IAElC,IAAMiB,EAAelB,EAAmBgB,KAAK,iBAAmB,oBAAsB3B,EAAMY,GAE5FD,EAAmBgB,KAAK,OAAQE,GAAcF,KAAK,WAAYE,GAE/DpB,EAAkBmB,KAAK1B,EAAYkB,EAxB3B,EAwB6CN,KAErDJ,EAAiBkB,KAAKzB,EAAQiB,EA1BtB,EA0BwCL,KAEhDe,GACJ,EAEIC,EAAiB,WACA,KAAbvC,GACAiC,EAAWb,MACTA,EACUrB,GAASqB,EAAQC,KACzBD,EAAQ,IAGZa,EAAWL,EAvCP,EAuCyBP,IAGjCvB,EAActB,YAAW,WACrBgE,GACJ,GAAGd,EACP,EAEIc,EAAmB,WACnBC,IACA5C,EAAcrB,YAAW,WACrB+D,GACJ,GAAGf,EACP,EAEIc,EAAiB,WACjBhE,EAAgBG,YAAY,UAAUiE,SAAS9B,GAASnC,YAAYoC,EACxE,EAEI4B,EAAiB,WACjBnE,EAAgBG,YAAYmC,GAAS8B,SAAS7B,EAClD,EAEA1C,EAAEC,UAAUuE,GAAG,QAAS,qBAAqB,SAAUC,GACnDA,EAAEC,iBACFJ,IACAK,aAAahD,GACbgD,aAAajD,EACjB,IAEAvB,EAAgBqE,GAAG,mBAAmB,WAClCH,GACJ,IAEAA,GAhGA,CANA,CAuGJ,CAmCoBO,CAAWzE,EAAgB0E,KAAK,8BACpC,IAAE,OACK,SAAAC,GACHC,QAAQD,MAAM,eAAgBA,EAClC,GACR,GAAG,IAEX", "sources": ["webpack:///./platform/plugins/sale-popup/resources/js/sale-popup.js"], "sourcesContent": ["let salesPopup = function ($popupContainer) {\n    if ($(window).width() < 768) {\n        return\n    }\n\n    const stt = $popupContainer.data('stt')\n\n    if (stt === undefined) {\n        return\n    }\n\n    const limit = stt.limit - 1\n    const popupType = stt.pp_type\n    const arrTitle = JSON.parse($('#title-sale-popup').html())\n    const arrUrl = stt.url\n    const arrImage = stt.image\n    const arrID = stt.id\n    const arrLocation = JSON.parse($('#location-sale-popup').html())\n    const arrTime = JSON.parse($('#time-sale-popup').html())\n    const classUp = stt.classUp\n    const classDown = stt.classDown[classUp]\n    let starTimeout\n    let stayTimeout\n\n    const salePopupImg = $('.js-sale-popup-img')\n    const salePopupLink = $('.js-sale-popup-a')\n    const salePopupTitle = $('.js-sale-popup-tt')\n    const salePopupLocation = $('.js-sale-popup-location')\n    const salePopupTimeAgo = $('.js-sale-popup-ago')\n    const salePopupQuickView = $('.sale-popup-quick-view')\n\n    let index = 0\n    const min = 0\n    const max = arrUrl.length - 1\n    const max2 = arrLocation.length - 1\n    const max3 = arrTime.length - 1\n    const starTime = stt.starTime * stt.starTimeUnit\n    const stayTime = stt.stayTime * stt.stayTimeUnit\n\n    let getRandomInt = function (min, max) {\n        return Math.floor(Math.random() * (max - min + 1)) + min\n    }\n\n    let updateData = function (index) {\n        let img = arrImage[index]\n\n        salePopupImg.attr('src', img).attr('srcset', img)\n\n        salePopupTitle.text(arrTitle[index])\n\n        salePopupLink.attr('href', arrUrl[index])\n\n        const quickViewUrl = salePopupQuickView.attr('data-base-url') + '/ajax/quick-view/' + arrID[index]\n\n        salePopupQuickView.attr('href', quickViewUrl).attr('data-url', quickViewUrl)\n\n        salePopupLocation.text(arrLocation[getRandomInt(min, max2)])\n\n        salePopupTimeAgo.text(arrTime[getRandomInt(min, max3)])\n\n        showSalesPopUp()\n    }\n\n    let loadSalesPopup = function () {\n        if (popupType == '1') {\n            updateData(index)\n            ++index\n            if (index > limit || index > max) {\n                index = 0\n            }\n        } else {\n            updateData(getRandomInt(min, max))\n        }\n\n        stayTimeout = setTimeout(function () {\n            unloadSalesPopup()\n        }, stayTime)\n    }\n\n    let unloadSalesPopup = function () {\n        hideSalesPopUp()\n        starTimeout = setTimeout(function () {\n            loadSalesPopup()\n        }, starTime)\n    }\n\n    let showSalesPopUp = function () {\n        $popupContainer.removeClass('hidden').addClass(classUp).removeClass(classDown)\n    }\n\n    let hideSalesPopUp = function () {\n        $popupContainer.removeClass(classUp).addClass(classDown)\n    }\n\n    $(document).on('click', '.sale-popup-close', function (e) {\n        e.preventDefault()\n        hideSalesPopUp()\n        clearTimeout(stayTimeout)\n        clearTimeout(starTimeout)\n    })\n\n    $popupContainer.on('open-sale-popup', function () {\n        unloadSalesPopup()\n    })\n\n    unloadSalesPopup()\n}\n\n$(document).ready(function () {\n    const $popupContainer = $('.js-sale-popup-container.hidden')\n\n    if ($popupContainer.length) {\n        setTimeout(() => {\n            $popupContainer.removeClass('hidden')\n\n            // Assuming $popupContainer is a jQuery object\n            const url = $popupContainer.data('include');\n\n            fetch(url, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json', // Sending JSON\n                    'Accept': 'application/json' // Requesting JSON response\n                }\n            })\n                .then(response => {\n                    // Check if the response is okay and parse it as JSON\n                    if (!response.ok) {\n                        throw new Error('Network response was not ok');\n                    }\n                    return response.json();\n                })\n                .then(({ data }) => {\n                    // Insert the fetched HTML into the $popupContainer\n                    $popupContainer.html(data);\n\n                    if (typeof Theme.lazyLoadInstance !== 'undefined') {\n                        Theme.lazyLoadInstance.update()\n                    }\n\n                    // Call salesPopup with the newly added content\n                    salesPopup($popupContainer.find('.sale-popup-container-wrap'));\n                })\n                .catch(error => {\n                    console.error('Fetch error:', error);\n                });\n        }, 3000)\n    }\n})\n"], "names": ["$", "document", "ready", "$popupContainer", "length", "setTimeout", "removeClass", "url", "data", "fetch", "method", "headers", "then", "response", "ok", "Error", "json", "_ref", "html", "Theme", "lazyLoadInstance", "update", "window", "width", "stt", "undefined", "starTimeout", "stayTimeout", "limit", "popupType", "pp_type", "arr<PERSON>itle", "JSON", "parse", "arrUrl", "arrImage", "image", "arrID", "id", "arrLocation", "arrTime", "classUp", "classDown", "salePopupImg", "salePopupLink", "salePopupTitle", "salePopupLocation", "salePopupTimeAgo", "salePopupQuickView", "index", "max", "max2", "max3", "starTime", "starTimeUnit", "stayTime", "stayTimeUnit", "getRandomInt", "min", "Math", "floor", "random", "updateData", "img", "attr", "text", "quickViewUrl", "showSalesPopUp", "loadSalesPopup", "unloadSalesPopup", "hideSalesPopUp", "addClass", "on", "e", "preventDefault", "clearTimeout", "salesPopup", "find", "error", "console"], "sourceRoot": ""}