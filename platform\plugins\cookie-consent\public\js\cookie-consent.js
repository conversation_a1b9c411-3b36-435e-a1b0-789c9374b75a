(()=>{"use strict";$((function(){window.shaqiCookieConsent=function(){var e,i=$("div[data-site-cookie-name]").data("site-cookie-name"),o=$("div[data-site-cookie-domain]").data("site-cookie-domain"),t=$("div[data-site-cookie-lifetime]").data("site-cookie-lifetime"),n=$("div[data-site-session-secure]").data("site-session-secure"),s=$(".js-cookie-consent");function a(){var e,s,a,d;e=i,s=1,a=t,(d=new Date).setTime(d.getTime()+24*a*60*60*1e3),document.cookie=e+"="+s+";expires="+d.toUTCString()+";domain="+o+";path=/"+n,c()}function c(){s.hide()}return s.addClass("cookie-consent--visible"),e=i,-1!==document.cookie.split("; ").indexOf(e+"=1")&&c(),$(document).on("click",".js-cookie-consent-agree",(function(){a()})),{consentWithCookies:a,hideCookieDialog:c}}()}))})();
//# sourceMappingURL=cookie-consent.js.map