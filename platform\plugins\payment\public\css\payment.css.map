{"version": 3, "file": "vendor/core/plugins/payment/css/payment.css", "mappings": "AAAA,uBACI,GACI,oBAEJ,GACI,sBAIR,kBAEI,cADA,eACA,CAGJ,6BAGI,aADA,iBACA,CAGJ,iDAEI,mBAGJ,yCAKI,eAFA,cACA,kBAFA,kBAIA,sBAGJ,mEAEI,uBACA,yBACA,2BAGJ,uDASI,wBADA,WAHA,qBAEA,YAHA,OAFA,kBACA,MAGA,UAGA,CAGJ,qDAII,WADA,aADA,iBAEA,CAGJ,6DAGI,cADA,kBACA,CAGJ,6NAMI,mBAGJ,uFAGI,oBADA,wBACA,CAGJ,2EAEI,qBAGJ,uEAEI,oBAGJ,qEAEI,cAGJ,0BACI,kBAGJ,yBAMI,mBADA,kBADA,WAFA,SADA,QAEA,SAGA,CAGJ,kCACI,yBAGJ,4CACI,yBAGJ,2CACI,mBAGJ,6BACI,kBAGJ,4BASI,sBAEA,cADA,aAPA,sBAEA,YAHA,SADA,QAKA,wBAFA,SAOA,CAGJ,qCAEI,mBADA,cACA,CAGJ,+CAEI,mBADA,cACA,CAGJ,uBACI,aAIA,gBAEA,yBAEI,gBADA,iBACA,CAQR,6CACI,cAIA,8CACI,qBAGJ,6BACI,mBAEA,wCACI,yCAKZ,oCAGY,qCACI,mBAGJ,gCACI,0BAEA,4CACI,6BAGJ,8CACI,oBAOpB,wBACI,qBAGI,iBAFA,eAGA,kBAFA,UAEA,CAEA,wCACI,sBACA,6BAEA,iDACI,gBASZ,qBAEI,eADA,iBACA,CAEA,0CAGI,uBAFA,kBAIA,eAHA,UAEA,UACA,CAEA,8CAEI,YACA,gBAFA,cAEA,C", "sources": ["webpack:///./platform/plugins/payment/resources/sass/payment.scss"], "sourcesContent": ["@keyframes hover-color {\n    from {\n        border-color: #c0c0c0;\n    }\n    to {\n        border-color: #3e97eb;\n    }\n}\n\n.checkout-wrapper {\n    max-width: 500px;\n    margin: 0 auto;\n}\n\n.magic-radio,\n.magic-checkbox {\n    position: absolute;\n    display: none;\n}\n\n.magic-radio[disabled],\n.magic-checkbox[disabled] {\n    cursor: not-allowed;\n}\n\n.magic-radio + label,\n.magic-checkbox + label {\n    position: relative;\n    display: block;\n    padding-left: 30px;\n    cursor: pointer;\n    vertical-align: middle;\n}\n\n.magic-radio + label:hover:before,\n.magic-checkbox + label:hover:before {\n    animation-duration: 0.4s;\n    animation-fill-mode: both;\n    animation-name: hover-color;\n}\n\n.magic-radio + label:before,\n.magic-checkbox + label:before {\n    position: absolute;\n    top: 0;\n    left: 0;\n    display: inline-block;\n    width: 20px;\n    height: 20px;\n    content: '';\n    border: 1px solid #c0c0c0;\n}\n\n.magic-radio + label:after,\n.magic-checkbox + label:after {\n    position: absolute;\n    display: none;\n    content: '';\n}\n\n.magic-radio[disabled] + label,\n.magic-checkbox[disabled] + label {\n    cursor: not-allowed;\n    color: #e4e4e4;\n}\n\n.magic-radio[disabled] + label:hover,\n.magic-radio[disabled] + label:before,\n.magic-radio[disabled] + label:after,\n.magic-checkbox[disabled] + label:hover,\n.magic-checkbox[disabled] + label:before,\n.magic-checkbox[disabled] + label:after {\n    cursor: not-allowed;\n}\n\n.magic-radio[disabled] + label:hover:before,\n.magic-checkbox[disabled] + label:hover:before {\n    border: 1px solid #e4e4e4;\n    animation-name: none;\n}\n\n.magic-radio[disabled] + label:before,\n.magic-checkbox[disabled] + label:before {\n    border-color: #e4e4e4;\n}\n\n.magic-radio:checked + label:before,\n.magic-checkbox:checked + label:before {\n    animation-name: none;\n}\n\n.magic-radio:checked + label:after,\n.magic-checkbox:checked + label:after {\n    display: block;\n}\n\n.magic-radio + label:before {\n    border-radius: 50%;\n}\n\n.magic-radio + label:after {\n    top: 6px;\n    left: 6px;\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    background: #3e97eb;\n}\n\n.magic-radio:checked + label:before {\n    border: 1px solid #3e97eb;\n}\n\n.magic-radio:checked[disabled] + label:before {\n    border: 1px solid #c9e2f9;\n}\n\n.magic-radio:checked[disabled] + label:after {\n    background: #c9e2f9;\n}\n\n.magic-checkbox + label:before {\n    border-radius: 3px;\n}\n\n.magic-checkbox + label:after {\n    top: 2px;\n    left: 7px;\n    box-sizing: border-box;\n    width: 6px;\n    height: 12px;\n    transform: rotate(45deg);\n    border-width: 2px;\n    border-style: solid;\n    border-color: #ffffff;\n    border-top: 0;\n    border-left: 0;\n}\n\n.magic-checkbox:checked + label:before {\n    border: #3e97eb;\n    background: #3e97eb;\n}\n\n.magic-checkbox:checked[disabled] + label:before {\n    border: #c9e2f9;\n    background: #c9e2f9;\n}\n\n.payment_collapse_wrap {\n    display: none;\n    -webkit-transition: 0.25s;\n    -moz-transition: 0.25s;\n    -o-transition: 0.25s;\n    transition: 0.25s;\n\n    > p {\n        padding-left: 30px;\n        margin-bottom: 0;\n    }\n}\n\n.payment_collapse_wrap.active {\n    display: block;\n}\n\n.collapse.show {\n    display: block;\n}\n\n.list-group {\n    .list-group-item + .list-group-item {\n        border-top-width: 1px;\n    }\n\n    .list-group-item {\n        border-bottom: none;\n\n        &:last-child {\n            border-bottom: 1px solid rgba(0, 0, 0, 0.125);\n        }\n    }\n}\n\n@media screen and (max-width: 768px) {\n    .card-checkout {\n        .form-group {\n            .col-sm-9 {\n                margin-bottom: 10px;\n            }\n\n            &.mb-3 {\n                margin-bottom: 0 !important;\n\n                &:first-child {\n                    margin-bottom: 1rem !important;\n                }\n\n                .form-control {\n                    margin-bottom: 15px;\n                }\n            }\n        }\n    }\n}\n\n@media (max-width: 450px) {\n    .stripe-card-wrapper {\n        max-width: 80vw;\n        width: 100%;\n        margin: 20px auto;\n        overflow-x: hidden;\n\n        & > .jp-card-container {\n            transform: scale(0.625);\n            transform-origin: left center;\n\n            .jp-card {\n                min-width: 100%;\n            }\n        }\n    }\n}\n\n.payment-method {\n    $prefix: &;\n\n    &-item {\n        position: relative;\n        padding: 0.75rem;\n\n        #{$prefix}-logo {\n            position: absolute;\n            top: 0.5rem;\n            inset-inline-end: 0.5rem;\n            width: 4rem;\n            text-align: end;\n\n            img {\n                max-width: 100%;\n                height: auto;\n                max-height: 38px;\n            }\n        }\n    }\n}\n"], "names": [], "sourceRoot": ""}