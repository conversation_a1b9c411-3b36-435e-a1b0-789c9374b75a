{"version": 3, "file": "vendor/core/plugins/newsletter/css/newsletter.css", "mappings": "AACI,gCACI,8BACA,yCACA,cAEA,qDACI,yBACA,UAEA,yDACI,YACA,qCACA,WAGJ,gDAVJ,qDAYQ,aADA,UACA,EAKJ,yEAEI,gBADA,UACA,CAEA,wBAJJ,yEAMQ,4BADA,yBAEA,qBAIR,mEACI,2BAGJ,0DAEI,sBADA,QACA,CAGJ,+DAEI,eADA,kBACA,CAGJ,2DACI,eAIA,0DACI,eACA,gBAEA,yEAEI,UADA,YACA,CAIR,sEACI,YACA,eAIA,gEACI,gBAIR,2DACI,2BAGA,gDAFA,cAGA,mBAFA,UAEA,CAGJ,gEAGI,mBADA,aAEA,UAHA,kBAGA,CAEA,sEAEI,eACA,aAFA,aAEA,CAKZ,+DACI,yBAGA,4BAFA,eAGA,kBAFA,iBAIA,qCAEA,sEAaI,oDAEA,oDAVA,kBAJA,WAUA,YAPA,SAKA,iBADA,gBANA,kBACA,QAOA,UAKA,CAGJ,gNAGI,kBAIR,mEAGI,2BACA,gBACA,aAEA,cANA,gBACA,4BAIA,UACA,CAEA,8FACI,yBACA,qBACA,WAGJ,4FACI,yBACA,qBACA,WAkBpB,kCACI,GAEI,uBAEJ,GAEI,yB", "sources": ["webpack:///./platform/plugins/newsletter/resources/sass/newsletter.scss"], "sourcesContent": [".newsletter-popup {\n    .modal-dialog {\n        background: var(--bs-modal-bg);\n        border-radius: var(--bs-border-radius-lg);\n        overflow: auto;\n\n        .newsletter-popup-bg {\n            background-color: #f7f7f7;\n            padding: 0;\n\n            img {\n                height: 100%;\n                object-fit: cover;\n                width: 100%;\n            }\n\n            @media (min-width: 768px) and (max-width: 991.98px) {\n                width: 100%;\n                height: 12rem;\n            }\n        }\n\n        .modal-content {\n            .newsletter-popup-content {\n                width: 100%;\n                padding: 1.25rem;\n\n                @media (min-width: 768px) {\n                    border-top-left-radius: 0;\n                    border-bottom-left-radius: 0;\n                    padding: 4.5rem 3rem;\n                }\n            }\n\n            .captcha-disclaimer {\n                padding: 5px 10px !important;\n            }\n\n            .btn-close {\n                top: 1rem;\n                inset-inline-end: 1rem;\n            }\n\n            .modal-subtitle {\n                letter-spacing: 1px;\n                font-size: 15px;\n            }\n\n            .modal-text {\n                font-size: 16px;\n            }\n\n            form {\n                label {\n                    font-size: 14px;\n                    font-weight: 500;\n\n                    &.required:after {\n                        content: ' *';\n                        color: red;\n                    }\n                }\n\n                input[type=email] {\n                    height: 50px;\n                    padding: 0 16px;\n                }\n\n                input {\n                    &:focus {\n                        box-shadow: none;\n                    }\n                }\n\n                button {\n                    --bs-btn-padding-y: 0.70rem;\n                    display: block;\n                    width: 100%;\n                    border-radius: var(--bs-border-radius) !important;\n                    margin-bottom: 1rem;\n                }\n\n                .form-check {\n                    margin-bottom: 1rem;\n                    display: flex;\n                    align-items: center;\n                    gap: 0.5rem;\n\n                    input {\n                        width: 1.25rem;\n                        height: 1.25rem;\n                        margin-top: 0;\n                    }\n                }\n            }\n\n            .button-loading {\n                border: 1px solid #c4cdd5;\n                cursor: default;\n                text-shadow: none;\n                color: transparent !important;\n                position: relative;\n                -webkit-transition: border-color 0.2s ease-out;\n                transition: border-color 0.2s ease-out;\n\n                &:before {\n                    content: '';\n                    position: absolute;\n                    top: 50%;\n                    left: 50%;\n                    border-radius: 50%;\n                    border-width: 3px;\n                    border-style: solid;\n                    margin-top: -9px;\n                    margin-left: -9px;\n                    width: 18px;\n                    height: 18px;\n                    -webkit-animation: button-loading-spinner 0.7s linear infinite;\n                    animation: button-loading-spinner 1s linear infinite;\n                    border-color: #ffffff;\n                    border-bottom-color: transparent;\n                }\n\n                &:hover,\n                &:focus,\n                &:active {\n                    color: transparent;\n                }\n            }\n\n            .newsletter-message {\n                margin: 0 0 20px 0;\n                padding: 15px 30px 15px 15px;\n                border-left: 5px solid #eeeeee;\n                border-radius: 0;\n                display: none;\n                width: 100%;\n                font-size: 85%;\n\n                &.newsletter-success-message {\n                    background-color: #c0edf1;\n                    border-color: #58d0da;\n                    color: #000;\n                }\n\n                &.newsletter-error-message {\n                    background-color: #faeaa9;\n                    border-color: #f3cc31;\n                    color: #000;\n                }\n            }\n        }\n    }\n}\n\n@-webkit-keyframes button-loading-spinner {\n    from {\n        -webkit-transform: rotate(0deg);\n        transform: rotate(0deg);\n    }\n    to {\n        -webkit-transform: rotate(360deg);\n        transform: rotate(360deg);\n    }\n}\n\n@keyframes button-loading-spinner {\n    from {\n        -webkit-transform: rotate(0deg);\n        transform: rotate(0deg);\n    }\n    to {\n        -webkit-transform: rotate(360deg);\n        transform: rotate(360deg);\n    }\n}\n"], "names": [], "sourceRoot": ""}