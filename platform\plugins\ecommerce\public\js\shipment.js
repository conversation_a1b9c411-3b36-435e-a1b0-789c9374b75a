(()=>{function t(o){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(o)}function o(t,o){for(var e=0;e<o.length;e++){var i=o[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,n(i.key),i)}}function n(o){var n=function(o,n){if("object"!=t(o)||!o)return o;var e=o[Symbol.toPrimitive];if(void 0!==e){var i=e.call(o,n||"default");if("object"!=t(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(o)}(o,"string");return"symbol"==t(n)?n:n+""}var e=function(){return t=function t(){!function(t,o){if(!(t instanceof o))throw new TypeError("Cannot call a class as a function")}(this,t)},(n=[{key:"init",value:function(){$(document).on("click",'[data-bb-toggle="update-shipping-status"]',(function(){$("#update-shipping-status-modal").modal("show")})),$(document).on("click",'[data-bb-toggle="update-shipping-cod-status"]',(function(){$("#update-shipping-cod-status-modal").modal("show")})),$(document).on("click","#confirm-update-shipping-status-button",(function(t){t.preventDefault();var o=$(t.currentTarget),n=o.closest(".modal-dialog").find("form");$httpClient.make().withButtonLoading(o).post(n.prop("action"),n.serialize()).then((function(t){var n=t.data;n.error?Shaqi.showError(n.message):($(".page-body").load("".concat(window.location.href," .page-body > *")),Shaqi.showSuccess(n.message),o.closest(".modal").modal("hide"))}))}))}}])&&o(t.prototype,n),e&&o(t,e),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,e}();$((function(){(new e).init()}))})();
//# sourceMappingURL=shipment.js.map