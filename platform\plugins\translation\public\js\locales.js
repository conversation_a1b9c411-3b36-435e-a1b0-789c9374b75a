({4736:function(){var t=this;$((function(){var a=$(".table-language");a.on("click",".delete-locale-button",(function(t){t.preventDefault(),$(".delete-crud-entry").data("url",$(t.currentTarget).data("url")),$(".modal-confirm-delete").modal("show")})),$(document).on("click",".delete-crud-entry",(function(e){e.preventDefault(),$(".modal-confirm-delete").modal("hide");var n=$(e.currentTarget).data("url");Shaqi.showButtonLoading($(t)),$httpClient.make().delete(n).then((function(t){var e=t.data;e.data&&(a.find("i[data-locale=".concat(e.data,"]")).unwrap(),$(".tooltip").remove()),a.find('.delete-locale-button[data-url="'.concat(n,'"]')).closest("tr").remove(),Shaqi.showSuccess(e.message)})).finally((function(){Shaqi.hideButtonLoading($(t))}))})),$(document).on("submit",".add-locale-form",(function(t){t.preventDefault(),t.stopPropagation();var e=$(this),n=e.find('button[type="submit"]');Shaqi.showButtonLoading(n),$httpClient.make().postForm(e.prop("action"),new FormData(e[0])).then((function(t){var e=t.data;Shaqi.showSuccess(e.message),a.load("".concat(window.location.href," .table-language > *"))})).finally((function(){Shaqi.hideButtonLoading(n)}))}))}))}})[4736]();
//# sourceMappingURL=locales.js.map