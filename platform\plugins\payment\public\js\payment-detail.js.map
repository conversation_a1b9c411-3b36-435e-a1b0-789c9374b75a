{"version": 3, "file": "/vendor/core/plugins/payment/js/payment-detail.js", "mappings": "mBAEAA,GAAE,WACEA,EAAEC,UAAUC,GAAG,QAAS,sBAAsB,SAAUC,GACpDA,EAAEC,iBACF,IAAMC,EAAQL,EAAEG,EAAEG,eAClBN,EAAEO,KAAK,CACHC,KAAM,MACNC,OAAO,EACPC,IAAKL,EAAMM,KAAK,OAChBC,WAAY,WACRP,EAAMQ,KAAK,KAAKC,SAAS,UAC7B,EACAC,QAAS,SAACC,GACDA,EAAIC,MAGLC,MAAMC,UAAUH,EAAII,SAFpBpB,EAAEK,EAAMM,KAAK,YAAYU,KAAKL,EAAIL,KAI1C,EACAM,MAAO,SAACD,GACJE,MAAMI,YAAYN,EACtB,EACAO,SAAU,WACNlB,EAAMQ,KAAK,KAAKW,YAAY,UAChC,GAER,GACJ,G", "sources": ["webpack:///./platform/plugins/payment/resources/js/payment-detail.js"], "sourcesContent": ["'use strict'\n\n$(() => {\n    $(document).on('click', '.get-refund-detail', function (e) {\n        e.preventDefault()\n        const $this = $(e.currentTarget)\n        $.ajax({\n            type: 'GET',\n            cache: false,\n            url: $this.data('url'),\n            beforeSend: () => {\n                $this.find('i').addClass('fa-spin')\n            },\n            success: (res) => {\n                if (!res.error) {\n                    $($this.data('element')).html(res.data)\n                } else {\n                    Shaqi.showError(res.message)\n                }\n            },\n            error: (res) => {\n                Shaqi.handleError(res)\n            },\n            complete: () => {\n                $this.find('i').removeClass('fa-spin')\n            },\n        })\n    })\n})\n"], "names": ["$", "document", "on", "e", "preventDefault", "$this", "currentTarget", "ajax", "type", "cache", "url", "data", "beforeSend", "find", "addClass", "success", "res", "error", "<PERSON><PERSON><PERSON>", "showError", "message", "html", "handleError", "complete", "removeClass"], "sourceRoot": ""}