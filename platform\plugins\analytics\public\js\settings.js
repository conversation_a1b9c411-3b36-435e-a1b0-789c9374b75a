$((function(){var e,t,n;e=document.getElementById("google-analytics-settings"),t=document.createElement("input"),n=null,t.type="file",t.accept="application/json",t.classList.add("d-none"),t.addEventListener("change",(function(t){var a=t.currentTarget,o=e.getElementsByClassName("CodeMirror"),l=null;if(o.length>0&&(l=o[0].CodeMirror,null==a||!a.files||0!==a.files.length)){var i=new FormData;i.set("json",a.files[0]),Shaqi.showLoading(e),$httpClient.make().postForm(n,i).then((function(e){var t=e.data;l.setValue(t.data.content)})).catch((function(e){e.response&&e.response.data&&l.setValue(e.response.data.data.content)})).finally((function(){Shaqi.hideLoading(e),a.value=""}))}})),document.body.appendChild(t),$(document).on("click",'[data-bb-toggle="analytics-trigger-upload-json"]',(function(e){e.preventDefault(),n=e.currentTarget.dataset.url,t.click()}))}));
//# sourceMappingURL=settings.js.map