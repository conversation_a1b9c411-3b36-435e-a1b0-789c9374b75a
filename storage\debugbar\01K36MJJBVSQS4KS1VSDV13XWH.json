{"__meta": {"id": "01K36MJJBVSQS4KS1VSDV13XWH", "datetime": "2025-08-21 15:39:48", "utime": **********.988654, "method": "GET", "uri": "/themes/ninico/plugins/bootstrap/bootstrap.bundle.min.js.map", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1755790784.931799, "end": **********.988682, "duration": 4.056883096694946, "duration_str": "4.06s", "measures": [{"label": "Booting", "start": 1755790784.931799, "relative_start": 0, "end": **********.796944, "relative_end": **********.796944, "duration": 1.***************, "duration_str": "1.87s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.796979, "relative_start": 1.****************, "end": **********.988686, "relative_end": 4.0531158447265625e-06, "duration": 2.***************, "duration_str": "2.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.850705, "relative_start": 1.****************, "end": **********.988702, "relative_end": 2.002716064453125e-05, "duration": 2.****************, "duration_str": "2.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: theme.ninico::views.404", "start": **********.908641, "relative_start": 1.****************, "end": **********.908641, "relative_end": **********.908641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.headers.default", "start": **********.930052, "relative_start": 1.****************, "end": **********.930052, "relative_end": **********.930052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-top", "start": **********.93082, "relative_start": 1.*************09, "end": **********.93082, "relative_end": **********.93082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.currency-switcher", "start": **********.958647, "relative_start": 2.026848077774048, "end": **********.958647, "relative_end": **********.958647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.language-switcher", "start": **********.959559, "relative_start": 2.0277600288391113, "end": **********.959559, "relative_end": **********.959559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0226480071dd0e74b211507465ab2265", "start": **********.967145, "relative_start": 2.035346031188965, "end": **********.967145, "relative_end": **********.967145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6112de9dcda50c4e5e8edc1897f5a562", "start": **********.968278, "relative_start": 2.0364789962768555, "end": **********.968278, "relative_end": **********.968278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::08a04217c3e416224df63d8e9dd782a7", "start": **********.969344, "relative_start": 2.0375449657440186, "end": **********.969344, "relative_end": **********.969344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "start": **********.970243, "relative_start": 2.0384440422058105, "end": **********.970243, "relative_end": **********.970243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::19d2357ba0f51b29e340109a50dc8d90", "start": **********.971411, "relative_start": 2.039612054824829, "end": **********.971411, "relative_end": **********.971411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.logo", "start": **********.971912, "relative_start": 2.0401129722595215, "end": **********.971912, "relative_end": **********.971912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.234278, "relative_start": 2.3024790287017822, "end": 1755790787.234278, "relative_end": 1755790787.234278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.250283, "relative_start": 2.31848406791687, "end": 1755790787.250283, "relative_end": 1755790787.250283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.262243, "relative_start": 2.330444097518921, "end": 1755790787.262243, "relative_end": 1755790787.262243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.283399, "relative_start": 2.351600170135498, "end": 1755790787.283399, "relative_end": 1755790787.283399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.308528, "relative_start": 2.3767290115356445, "end": 1755790787.308528, "relative_end": 1755790787.308528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.349889, "relative_start": 2.4180901050567627, "end": 1755790787.349889, "relative_end": 1755790787.349889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.36847, "relative_start": 2.436671018600464, "end": 1755790787.36847, "relative_end": 1755790787.36847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.385338, "relative_start": 2.4535391330718994, "end": 1755790787.385338, "relative_end": 1755790787.385338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.39767, "relative_start": 2.4658710956573486, "end": 1755790787.39767, "relative_end": 1755790787.39767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.417595, "relative_start": 2.4857959747314453, "end": 1755790787.417595, "relative_end": 1755790787.417595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.457134, "relative_start": 2.5253350734710693, "end": 1755790787.457134, "relative_end": 1755790787.457134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.499602, "relative_start": 2.567803144454956, "end": 1755790787.499602, "relative_end": 1755790787.499602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.557614, "relative_start": 2.6258151531219482, "end": 1755790787.557614, "relative_end": 1755790787.557614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.600287, "relative_start": 2.668488025665283, "end": 1755790787.600287, "relative_end": 1755790787.600287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.647828, "relative_start": 2.716029167175293, "end": 1755790787.647828, "relative_end": 1755790787.647828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.724357, "relative_start": 2.792557954788208, "end": 1755790787.724357, "relative_end": 1755790787.724357, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.736264, "relative_start": 2.8044650554656982, "end": 1755790787.736264, "relative_end": 1755790787.736264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.757402, "relative_start": 2.8256030082702637, "end": 1755790787.757402, "relative_end": 1755790787.757402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.781148, "relative_start": 2.849349021911621, "end": 1755790787.781148, "relative_end": 1755790787.781148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.80918, "relative_start": 2.8773810863494873, "end": 1755790787.80918, "relative_end": 1755790787.80918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.835054, "relative_start": 2.903254985809326, "end": 1755790787.835054, "relative_end": 1755790787.835054, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.848216, "relative_start": 2.916417121887207, "end": 1755790787.848216, "relative_end": 1755790787.848216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.869886, "relative_start": 2.938086986541748, "end": 1755790787.869886, "relative_end": 1755790787.869886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.905708, "relative_start": 2.9739091396331787, "end": 1755790787.905708, "relative_end": 1755790787.905708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": 1755790787.967112, "relative_start": 3.035313129425049, "end": 1755790787.967112, "relative_end": 1755790787.967112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.091147, "relative_start": 3.1593480110168457, "end": **********.091147, "relative_end": **********.091147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.149279, "relative_start": 3.217480182647705, "end": **********.149279, "relative_end": **********.149279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.170861, "relative_start": 3.2390620708465576, "end": **********.170861, "relative_end": **********.170861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.21263, "relative_start": 3.2808310985565186, "end": **********.21263, "relative_end": **********.21263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-meta", "start": **********.277943, "relative_start": 3.346143960952759, "end": **********.277943, "relative_end": **********.277943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.navbar", "start": **********.28975, "relative_start": 3.3579511642456055, "end": **********.28975, "relative_end": **********.28975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-middle", "start": **********.291475, "relative_start": 3.3596761226654053, "end": **********.291475, "relative_end": **********.291475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.logo", "start": **********.291899, "relative_start": 3.360100030899048, "end": **********.291899, "relative_end": **********.291899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.header-meta", "start": **********.29732, "relative_start": 3.36552095413208, "end": **********.29732, "relative_end": **********.29732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.logo", "start": **********.298554, "relative_start": 3.3667550086975098, "end": **********.298554, "relative_end": **********.298554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.logo", "start": **********.301959, "relative_start": 3.3701601028442383, "end": **********.301959, "relative_end": **********.301959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.mobile.menu-tab-content", "start": **********.304632, "relative_start": 3.372833013534546, "end": **********.304632, "relative_end": **********.304632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.mobile-menu", "start": **********.319301, "relative_start": 3.3875019550323486, "end": **********.319301, "relative_end": **********.319301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.323528, "relative_start": 3.3917291164398193, "end": **********.323528, "relative_end": **********.323528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.330907, "relative_start": 3.3991081714630127, "end": **********.330907, "relative_end": **********.330907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.336201, "relative_start": 3.404402017593384, "end": **********.336201, "relative_end": **********.336201, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.347969, "relative_start": 3.416170120239258, "end": **********.347969, "relative_end": **********.347969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.359385, "relative_start": 3.427586078643799, "end": **********.359385, "relative_end": **********.359385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.382242, "relative_start": 3.4504430294036865, "end": **********.382242, "relative_end": **********.382242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.388728, "relative_start": 3.4569289684295654, "end": **********.388728, "relative_end": **********.388728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.397659, "relative_start": 3.46586012840271, "end": **********.397659, "relative_end": **********.397659, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.403896, "relative_start": 3.472097158432007, "end": **********.403896, "relative_end": **********.403896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.413415, "relative_start": 3.4816160202026367, "end": **********.413415, "relative_end": **********.413415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.423413, "relative_start": 3.4916141033172607, "end": **********.423413, "relative_end": **********.423413, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.452191, "relative_start": 3.5203921794891357, "end": **********.452191, "relative_end": **********.452191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.458863, "relative_start": 3.527064085006714, "end": **********.458863, "relative_end": **********.458863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.473348, "relative_start": 3.54154896736145, "end": **********.473348, "relative_end": **********.473348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.51028, "relative_start": 3.5784809589385986, "end": **********.51028, "relative_end": **********.51028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.51471, "relative_start": 3.5829110145568848, "end": **********.51471, "relative_end": **********.51471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.521452, "relative_start": 3.5896530151367188, "end": **********.521452, "relative_end": **********.521452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.53148, "relative_start": 3.5996811389923096, "end": **********.53148, "relative_end": **********.53148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.537243, "relative_start": 3.6054439544677734, "end": **********.537243, "relative_end": **********.537243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.55292, "relative_start": 3.6211211681365967, "end": **********.55292, "relative_end": **********.55292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.566824, "relative_start": 3.6350250244140625, "end": **********.566824, "relative_end": **********.566824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.572255, "relative_start": 3.640455961227417, "end": **********.572255, "relative_end": **********.572255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.585129, "relative_start": 3.653330087661743, "end": **********.585129, "relative_end": **********.585129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.605178, "relative_start": 3.6733791828155518, "end": **********.605178, "relative_end": **********.605178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.651139, "relative_start": 3.7193400859832764, "end": **********.651139, "relative_end": **********.651139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.682369, "relative_start": 3.750570058822632, "end": **********.682369, "relative_end": **********.682369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.69117, "relative_start": 3.759371042251587, "end": **********.69117, "relative_end": **********.69117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.menu", "start": **********.712876, "relative_start": 3.7810771465301514, "end": **********.712876, "relative_end": **********.712876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.language-switcher", "start": **********.740137, "relative_start": 3.808338165283203, "end": **********.740137, "relative_end": **********.740137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.currency-switcher", "start": **********.740559, "relative_start": 3.808760166168213, "end": **********.740559, "relative_end": **********.740559, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::views.ecommerce.includes.mini-cart", "start": **********.741072, "relative_start": 3.8092730045318604, "end": **********.741072, "relative_end": **********.741072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.mobile.categories-tab-content", "start": **********.743012, "relative_start": 3.8112130165100098, "end": **********.743012, "relative_end": **********.743012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.categories-dropdown-mobile", "start": **********.743658, "relative_start": 3.811859130859375, "end": **********.743658, "relative_end": **********.743658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.footer", "start": **********.746928, "relative_start": 3.815129041671753, "end": **********.746928, "relative_end": **********.746928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/site-info/templates.frontend", "start": **********.754908, "relative_start": 3.8231091499328613, "end": **********.754908, "relative_end": **********.754908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/custom-menu/templates.frontend", "start": **********.75941, "relative_start": 3.827610969543457, "end": **********.75941, "relative_end": **********.75941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.footer-menu", "start": **********.76649, "relative_start": 3.834691047668457, "end": **********.76649, "relative_end": **********.76649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/custom-menu/templates.frontend", "start": **********.782973, "relative_start": 3.8511741161346436, "end": **********.782973, "relative_end": **********.782973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.footer-menu", "start": **********.78561, "relative_start": 3.853811025619507, "end": **********.78561, "relative_end": **********.78561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/custom-menu/templates.frontend", "start": **********.802887, "relative_start": 3.8710880279541016, "end": **********.802887, "relative_end": **********.802887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.footer-menu", "start": **********.805109, "relative_start": 3.873310089111328, "end": **********.805109, "relative_end": **********.805109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::31ae887b48bc7956b164b04a31b206f3", "start": **********.808089, "relative_start": 3.8762900829315186, "end": **********.808089, "relative_end": **********.808089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1001eded5b0608646935941ff416e315", "start": **********.815699, "relative_start": 3.8839001655578613, "end": **********.815699, "relative_end": **********.815699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::950abe7b40c10237079259e7602acfec", "start": **********.821523, "relative_start": 3.889724016189575, "end": **********.821523, "relative_end": **********.821523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7e87b9b6cf2d20df0f49b400032abd2c", "start": **********.8267, "relative_start": 3.8949010372161865, "end": **********.8267, "relative_end": **********.8267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f418e702bf973d5917d32a22532847e3", "start": **********.830601, "relative_start": 3.8988020420074463, "end": **********.830601, "relative_end": **********.830601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/newsletter/templates.frontend", "start": **********.851402, "relative_start": 3.919603109359741, "end": **********.851402, "relative_end": **********.851402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.891298, "relative_start": 3.9594991207122803, "end": **********.891298, "relative_end": **********.891298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": **********.894338, "relative_start": 3.962538957595825, "end": **********.894338, "relative_end": **********.894338, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.895629, "relative_start": 3.96382999420166, "end": **********.895629, "relative_end": **********.895629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.898172, "relative_start": 3.9663729667663574, "end": **********.898172, "relative_end": **********.898172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.89966, "relative_start": 3.9678611755371094, "end": **********.89966, "relative_end": **********.89966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.900415, "relative_start": 3.968616008758545, "end": **********.900415, "relative_end": **********.900415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.901213, "relative_start": 3.969413995742798, "end": **********.901213, "relative_end": **********.901213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.901853, "relative_start": 3.9700541496276855, "end": **********.901853, "relative_end": **********.901853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.902438, "relative_start": 3.9706389904022217, "end": **********.902438, "relative_end": **********.902438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.903193, "relative_start": 3.9713940620422363, "end": **********.903193, "relative_end": **********.903193, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.903541, "relative_start": 3.9717421531677246, "end": **********.903541, "relative_end": **********.903541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.903931, "relative_start": 3.9721319675445557, "end": **********.903931, "relative_end": **********.903931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": **********.904628, "relative_start": 3.9728291034698486, "end": **********.904628, "relative_end": **********.904628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.905443, "relative_start": 3.9736440181732178, "end": **********.905443, "relative_end": **********.905443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.906186, "relative_start": 3.9743871688842773, "end": **********.906186, "relative_end": **********.906186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.906521, "relative_start": 3.974722146987915, "end": **********.906521, "relative_end": **********.906521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.906941, "relative_start": 3.975142002105713, "end": **********.906941, "relative_end": **********.906941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.907432, "relative_start": 3.975633144378662, "end": **********.907432, "relative_end": **********.907432, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.907951, "relative_start": 3.976152181625366, "end": **********.907951, "relative_end": **********.907951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.908794, "relative_start": 3.9769949913024902, "end": **********.908794, "relative_end": **********.908794, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.90917, "relative_start": 3.9773709774017334, "end": **********.90917, "relative_end": **********.90917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.90958, "relative_start": 3.977781057357788, "end": **********.90958, "relative_end": **********.90958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.910103, "relative_start": 3.978304147720337, "end": **********.910103, "relative_end": **********.910103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.910579, "relative_start": 3.9787800312042236, "end": **********.910579, "relative_end": **********.910579, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.911104, "relative_start": 3.9793050289154053, "end": **********.911104, "relative_end": **********.911104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.911359, "relative_start": 3.979560136795044, "end": **********.911359, "relative_end": **********.911359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.911667, "relative_start": 3.979868173599243, "end": **********.911667, "relative_end": **********.911667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.912007, "relative_start": 3.980208158493042, "end": **********.912007, "relative_end": **********.912007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.912394, "relative_start": 3.980595111846924, "end": **********.912394, "relative_end": **********.912394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.912962, "relative_start": 3.9811630249023438, "end": **********.912962, "relative_end": **********.912962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.913242, "relative_start": 3.981443166732788, "end": **********.913242, "relative_end": **********.913242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.913542, "relative_start": 3.981743097305298, "end": **********.913542, "relative_end": **********.913542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.913908, "relative_start": 3.9821090698242188, "end": **********.913908, "relative_end": **********.913908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::form.field", "start": **********.914282, "relative_start": 3.982483148574829, "end": **********.914282, "relative_end": **********.914282, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.914802, "relative_start": 3.9830031394958496, "end": **********.914802, "relative_end": **********.914802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.915086, "relative_start": 3.9832870960235596, "end": **********.915086, "relative_end": **********.915086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.91537, "relative_start": 3.9835710525512695, "end": **********.91537, "relative_end": **********.91537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::button", "start": **********.916215, "relative_start": 3.9844160079956055, "end": **********.916215, "relative_end": **********.916215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.91696, "relative_start": 3.985161066055298, "end": **********.91696, "relative_end": **********.91696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.919204, "relative_start": 3.9874050617218018, "end": **********.919204, "relative_end": **********.919204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/cta-contact/templates.frontend", "start": **********.923731, "relative_start": 3.9919321537017822, "end": **********.923731, "relative_end": **********.923731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::/../widgets/reviews-logos/templates.frontend", "start": **********.932927, "relative_start": 4.0011279582977295, "end": **********.932927, "relative_end": **********.932927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::layouts.base", "start": **********.935667, "relative_start": 4.003868103027344, "end": **********.935667, "relative_end": **********.935667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.header", "start": **********.94654, "relative_start": 4.0147411823272705, "end": **********.94654, "relative_end": **********.94654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.scroll-top", "start": **********.970606, "relative_start": 4.038807153701782, "end": **********.970606, "relative_end": **********.970606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: theme.ninico::partials.navigation-bar", "start": **********.972344, "relative_start": 4.0405449867248535, "end": **********.972344, "relative_end": **********.972344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::partials.footer", "start": **********.975687, "relative_start": 4.043888092041016, "end": **********.975687, "relative_end": **********.975687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: packages/theme::fronts.toast-notification", "start": **********.979973, "relative_start": 4.048174142837524, "end": **********.979973, "relative_end": **********.979973, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "53MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException", "message": "The route themes/ninico/plugins/bootstrap/bootstrap.bundle.min.js.map could not be found.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php", "line": 45, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Routing/RouteCollection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>162</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">handleMatchedRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Routing\\AbstractRouteCollection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>763</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">match</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Routing\\RouteCollection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">findRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">platform/core/js-validation/src/RemoteValidationMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>43</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"45 characters\">Shaqi\\JsValidation\\RemoteValidationMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>23</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            return $this->getRouteForMethods($request, $others);\n", "        }\n", "\n", "        throw new NotFoundHttpException(sprintf(\n", "            'The route %s could not be found.',\n", "            $request->path()\n", "        ));\n"], "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FAbstractRouteCollection.php&line=45", "ajax": false, "filename": "AbstractRouteCollection.php", "line": "45"}}]}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "lcoal", "Debug Mode": "Enabled", "URL": "tesmods.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 144, "nb_templates": 144, "templates": [{"name": "1x theme.ninico::views.404", "param_count": null, "params": [], "start": **********.908579, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/views/404.blade.phptheme.ninico::views.404", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fviews%2F404.blade.php&line=1", "ajax": false, "filename": "404.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::views.404"}, {"name": "1x theme.ninico::partials.headers.default", "param_count": null, "params": [], "start": **********.930013, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.phptheme.ninico::partials.headers.default", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheaders%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.headers.default"}, {"name": "1x theme.ninico::partials.header-top", "param_count": null, "params": [], "start": **********.930782, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-top.blade.phptheme.ninico::partials.header-top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheader-top.blade.php&line=1", "ajax": false, "filename": "header-top.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.header-top"}, {"name": "2x theme.ninico::partials.currency-switcher", "param_count": null, "params": [], "start": **********.958607, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/currency-switcher.blade.phptheme.ninico::partials.currency-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fcurrency-switcher.blade.php&line=1", "ajax": false, "filename": "currency-switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.ninico::partials.currency-switcher"}, {"name": "2x theme.ninico::partials.language-switcher", "param_count": null, "params": [], "start": **********.959519, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/language-switcher.blade.phptheme.ninico::partials.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.ninico::partials.language-switcher"}, {"name": "1x __components::0226480071dd0e74b211507465ab2265", "param_count": null, "params": [], "start": **********.967112, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/0226480071dd0e74b211507465ab2265.blade.php__components::0226480071dd0e74b211507465ab2265", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F0226480071dd0e74b211507465ab2265.blade.php&line=1", "ajax": false, "filename": "0226480071dd0e74b211507465ab2265.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0226480071dd0e74b211507465ab2265"}, {"name": "1x __components::6112de9dcda50c4e5e8edc1897f5a562", "param_count": null, "params": [], "start": **********.968244, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/6112de9dcda50c4e5e8edc1897f5a562.blade.php__components::6112de9dcda50c4e5e8edc1897f5a562", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F6112de9dcda50c4e5e8edc1897f5a562.blade.php&line=1", "ajax": false, "filename": "6112de9dcda50c4e5e8edc1897f5a562.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::6112de9dcda50c4e5e8edc1897f5a562"}, {"name": "1x __components::08a04217c3e416224df63d8e9dd782a7", "param_count": null, "params": [], "start": **********.969321, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/08a04217c3e416224df63d8e9dd782a7.blade.php__components::08a04217c3e416224df63d8e9dd782a7", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F08a04217c3e416224df63d8e9dd782a7.blade.php&line=1", "ajax": false, "filename": "08a04217c3e416224df63d8e9dd782a7.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::08a04217c3e416224df63d8e9dd782a7"}, {"name": "1x __components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "param_count": null, "params": [], "start": **********.97021, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php__components::3fb0a03fd317b61c1c5c6c5fdfca9fed", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php&line=1", "ajax": false, "filename": "3fb0a03fd317b61c1c5c6c5fdfca9fed.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3fb0a03fd317b61c1c5c6c5fdfca9fed"}, {"name": "1x __components::19d2357ba0f51b29e340109a50dc8d90", "param_count": null, "params": [], "start": **********.971375, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/19d2357ba0f51b29e340109a50dc8d90.blade.php__components::19d2357ba0f51b29e340109a50dc8d90", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F19d2357ba0f51b29e340109a50dc8d90.blade.php&line=1", "ajax": false, "filename": "19d2357ba0f51b29e340109a50dc8d90.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::19d2357ba0f51b29e340109a50dc8d90"}, {"name": "4x theme.ninico::partials.logo", "param_count": null, "params": [], "start": **********.971892, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/logo.blade.phptheme.ninico::partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 4, "name_original": "theme.ninico::partials.logo"}, {"name": "57x theme.ninico::partials.menu", "param_count": null, "params": [], "start": 1755790787.234224, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.phptheme.ninico::partials.menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 57, "name_original": "theme.ninico::partials.menu"}, {"name": "2x theme.ninico::partials.header-meta", "param_count": null, "params": [], "start": **********.277905, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-meta.blade.phptheme.ninico::partials.header-meta", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheader-meta.blade.php&line=1", "ajax": false, "filename": "header-meta.blade.php", "line": "?"}, "render_count": 2, "name_original": "theme.ninico::partials.header-meta"}, {"name": "1x theme.ninico::partials.navbar", "param_count": null, "params": [], "start": **********.289715, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/navbar.blade.phptheme.ninico::partials.navbar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fnavbar.blade.php&line=1", "ajax": false, "filename": "navbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.navbar"}, {"name": "1x theme.ninico::partials.header-middle", "param_count": null, "params": [], "start": **********.291441, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-middle.blade.phptheme.ninico::partials.header-middle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fheader-middle.blade.php&line=1", "ajax": false, "filename": "header-middle.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.header-middle"}, {"name": "1x theme.ninico::partials.mobile.menu-tab-content", "param_count": null, "params": [], "start": **********.30461, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/mobile/menu-tab-content.blade.phptheme.ninico::partials.mobile.menu-tab-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fmobile%2Fmenu-tab-content.blade.php&line=1", "ajax": false, "filename": "menu-tab-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.mobile.menu-tab-content"}, {"name": "1x theme.ninico::partials.mobile-menu", "param_count": null, "params": [], "start": **********.319267, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/mobile-menu.blade.phptheme.ninico::partials.mobile-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fmobile-menu.blade.php&line=1", "ajax": false, "filename": "mobile-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.mobile-menu"}, {"name": "1x theme.ninico::views.ecommerce.includes.mini-cart", "param_count": null, "params": [], "start": **********.741052, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/views/ecommerce/includes/mini-cart.blade.phptheme.ninico::views.ecommerce.includes.mini-cart", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fviews%2Fecommerce%2Fincludes%2Fmini-cart.blade.php&line=1", "ajax": false, "filename": "mini-cart.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::views.ecommerce.includes.mini-cart"}, {"name": "1x theme.ninico::partials.mobile.categories-tab-content", "param_count": null, "params": [], "start": **********.742989, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/mobile/categories-tab-content.blade.phptheme.ninico::partials.mobile.categories-tab-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fmobile%2Fcategories-tab-content.blade.php&line=1", "ajax": false, "filename": "categories-tab-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.mobile.categories-tab-content"}, {"name": "1x theme.ninico::partials.categories-dropdown-mobile", "param_count": null, "params": [], "start": **********.743618, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/categories-dropdown-mobile.blade.phptheme.ninico::partials.categories-dropdown-mobile", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fcategories-dropdown-mobile.blade.php&line=1", "ajax": false, "filename": "categories-dropdown-mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.categories-dropdown-mobile"}, {"name": "1x theme.ninico::partials.footer", "param_count": null, "params": [], "start": **********.746904, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/footer.blade.phptheme.ninico::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.footer"}, {"name": "1x theme.ninico::/../widgets/site-info/templates.frontend", "param_count": null, "params": [], "start": **********.754887, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/site-info/templates/frontend.blade.phptheme.ninico::/../widgets/site-info/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Fsite-info%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::/../widgets/site-info/templates.frontend"}, {"name": "3x theme.ninico::/../widgets/custom-menu/templates.frontend", "param_count": null, "params": [], "start": **********.759372, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/custom-menu/templates/frontend.blade.phptheme.ninico::/../widgets/custom-menu/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Fcustom-menu%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 3, "name_original": "theme.ninico::/../widgets/custom-menu/templates.frontend"}, {"name": "3x theme.ninico::partials.footer-menu", "param_count": null, "params": [], "start": **********.766467, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/footer-menu.blade.phptheme.ninico::partials.footer-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Ffooter-menu.blade.php&line=1", "ajax": false, "filename": "footer-menu.blade.php", "line": "?"}, "render_count": 3, "name_original": "theme.ninico::partials.footer-menu"}, {"name": "1x __components::31ae887b48bc7956b164b04a31b206f3", "param_count": null, "params": [], "start": **********.808048, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/31ae887b48bc7956b164b04a31b206f3.blade.php__components::31ae887b48bc7956b164b04a31b206f3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F31ae887b48bc7956b164b04a31b206f3.blade.php&line=1", "ajax": false, "filename": "31ae887b48bc7956b164b04a31b206f3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::31ae887b48bc7956b164b04a31b206f3"}, {"name": "1x __components::1001eded5b0608646935941ff416e315", "param_count": null, "params": [], "start": **********.815646, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/1001eded5b0608646935941ff416e315.blade.php__components::1001eded5b0608646935941ff416e315", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F1001eded5b0608646935941ff416e315.blade.php&line=1", "ajax": false, "filename": "1001eded5b0608646935941ff416e315.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1001eded5b0608646935941ff416e315"}, {"name": "1x __components::950abe7b40c10237079259e7602acfec", "param_count": null, "params": [], "start": **********.821486, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/950abe7b40c10237079259e7602acfec.blade.php__components::950abe7b40c10237079259e7602acfec", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F950abe7b40c10237079259e7602acfec.blade.php&line=1", "ajax": false, "filename": "950abe7b40c10237079259e7602acfec.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::950abe7b40c10237079259e7602acfec"}, {"name": "1x __components::7e87b9b6cf2d20df0f49b400032abd2c", "param_count": null, "params": [], "start": **********.826661, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/7e87b9b6cf2d20df0f49b400032abd2c.blade.php__components::7e87b9b6cf2d20df0f49b400032abd2c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2F7e87b9b6cf2d20df0f49b400032abd2c.blade.php&line=1", "ajax": false, "filename": "7e87b9b6cf2d20df0f49b400032abd2c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7e87b9b6cf2d20df0f49b400032abd2c"}, {"name": "1x __components::f418e702bf973d5917d32a22532847e3", "param_count": null, "params": [], "start": **********.830574, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\storage\\framework\\views/f418e702bf973d5917d32a22532847e3.blade.php__components::f418e702bf973d5917d32a22532847e3", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fstorage%2Fframework%2Fviews%2Ff418e702bf973d5917d32a22532847e3.blade.php&line=1", "ajax": false, "filename": "f418e702bf973d5917d32a22532847e3.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::f418e702bf973d5917d32a22532847e3"}, {"name": "1x theme.ninico::/../widgets/newsletter/templates.frontend", "param_count": null, "params": [], "start": **********.851364, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/newsletter/templates/frontend.blade.phptheme.ninico::/../widgets/newsletter/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Fnewsletter%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::/../widgets/newsletter/templates.frontend"}, {"name": "2x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.89126, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/js-validation::bootstrap"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": **********.894289, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php&line=1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "6x core/base::forms.fields.html", "param_count": null, "params": [], "start": **********.89559, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php&line=1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 6, "name_original": "core/base::forms.fields.html"}, {"name": "7x a74ad8dfacd4f985eb3977517615ce25::form.field", "param_count": null, "params": [], "start": **********.898126, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/components/form/field.blade.phpa74ad8dfacd4f985eb3977517615ce25::form.field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 7, "name_original": "a74ad8dfacd4f985eb3977517615ce25::form.field"}, {"name": "7x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.899616, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 7, "name_original": "core/base::forms.partials.help-block"}, {"name": "7x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.900381, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php&line=1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 7, "name_original": "core/base::forms.partials.errors"}, {"name": "8x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.901179, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php&line=1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 8, "name_original": "core/base::forms.columns.column-span"}, {"name": "1x core/base::forms.fields.email", "param_count": null, "params": [], "start": **********.904595, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/core/base/resources/views/forms/fields/email.blade.phpcore/base::forms.fields.email", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Femail.blade.php&line=1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.email"}, {"name": "1x laravel-form-builder::button", "param_count": null, "params": [], "start": **********.916192, "type": "php", "hash": "phpD:\\laragon\\www\\tesmods\\platform\\packages\\form-builder\\src/../resources/views/button.phplaravel-form-builder::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fform-builder%2Fresources%2Fviews%2Fbutton.php&line=1", "ajax": false, "filename": "button.php", "line": "?"}, "render_count": 1, "name_original": "laravel-form-builder::button"}, {"name": "1x theme.ninico::/../widgets/cta-contact/templates.frontend", "param_count": null, "params": [], "start": **********.923545, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/cta-contact/templates/frontend.blade.phptheme.ninico::/../widgets/cta-contact/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Fcta-contact%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::/../widgets/cta-contact/templates.frontend"}, {"name": "1x theme.ninico::/../widgets/reviews-logos/templates.frontend", "param_count": null, "params": [], "start": **********.932876, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/////widgets/reviews-logos/templates/frontend.blade.phptheme.ninico::/../widgets/reviews-logos/templates.frontend", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fwidgets%2Freviews-logos%2Ftemplates%2Ffrontend.blade.php&line=1", "ajax": false, "filename": "frontend.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::/../widgets/reviews-logos/templates.frontend"}, {"name": "1x theme.ninico::layouts.base", "param_count": null, "params": [], "start": **********.935614, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/layouts/base.blade.phptheme.ninico::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::layouts.base"}, {"name": "1x packages/theme::partials.header", "param_count": null, "params": [], "start": **********.946497, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.header"}, {"name": "1x theme.ninico::partials.scroll-top", "param_count": null, "params": [], "start": **********.970552, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/scroll-top.blade.phptheme.ninico::partials.scroll-top", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fscroll-top.blade.php&line=1", "ajax": false, "filename": "scroll-top.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.scroll-top"}, {"name": "1x theme.ninico::partials.navigation-bar", "param_count": null, "params": [], "start": **********.972306, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/navigation-bar.blade.phptheme.ninico::partials.navigation-bar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fthemes%2Fninico%2Fpartials%2Fnavigation-bar.blade.php&line=1", "ajax": false, "filename": "navigation-bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "theme.ninico::partials.navigation-bar"}, {"name": "1x packages/theme::partials.footer", "param_count": null, "params": [], "start": **********.975653, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::partials.footer"}, {"name": "1x packages/theme::fronts.toast-notification", "param_count": null, "params": [], "start": **********.979935, "type": "blade", "hash": "bladeD:\\laragon\\www\\tesmods\\platform/packages/theme/resources/views/fronts/toast-notification.blade.phppackages/theme::fronts.toast-notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Ffronts%2Ftoast-notification.blade.php&line=1", "ajax": false, "filename": "toast-notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "packages/theme::fronts.toast-notification"}]}, "queries": {"count": 168, "nb_statements": 168, "nb_visible_statements": 168, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.23479000000000003, "accumulated_duration_str": "235ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 68 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 134}, {"index": 19, "namespace": null, "name": "platform/packages/theme/src/Theme.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\theme\\src\\Theme.php", "line": 346}, {"index": 20, "namespace": null, "name": "platform/packages/theme/src/Theme.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\theme\\src\\Theme.php", "line": 895}], "start": **********.918491, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 0, "width_percent": 1.67}, {"sql": "select * from `announcements` where 1 = 1 and `is_active` = 1 and (`start_date` is null or `start_date` <= '2025-08-21 15:39:46') and (`end_date` is null or `end_date` >= '2025-08-21 15:39:46') order by RAND()", "type": "query", "params": [], "bindings": [1, "2025-08-21 15:39:46", "2025-08-21 15:39:46"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/announcement/src/AnnouncementHelper.php", "file": "D:\\laragon\\www\\tesmods\\platform\\plugins\\announcement\\src\\AnnouncementHelper.php", "line": 59}, {"index": 17, "namespace": "view", "name": "theme.ninico::partials.header-top", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/header-top.blade.php", "line": 5}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.933856, "duration": 0.02169, "duration_str": "21.69ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 1.67, "width_percent": 9.238}, {"sql": "select * from `menus` where `status` = 'published' and exists (select * from `language_meta` where `menus`.`id` = `language_meta`.`reference_id` and `language_meta`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\Menu' and `lang_meta_code` = 'en_US' and `lang_meta_code` = 'en_US')", "type": "query", "params": [], "bindings": ["published", "Shaqi\\Menu\\Models\\Menu", "en_US", "en_US"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 17, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}, {"index": 18, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 170}, {"index": 20, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}], "start": 1755790787.009971, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 10.908, "width_percent": 0.486}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`menu_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 170}], "start": 1755790787.0147321, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 11.393, "width_percent": 0.562}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` in (25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 44, 51, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 66, 93, 95, 128, 129, 131, 132, 133, 135, 136, 137, 138, 139, 141, 142, 143, 144, 145, 146, 147, 148, 149, 151, 152, 153, 154, 155, 156, 158, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 201, 202, 203, 204, 205, 206, 209, 210, 211, 212, 213, 214, 215, 216) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}], "start": 1755790787.03903, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 11.955, "width_percent": 0.596}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (42, 44, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 66, 93, 95, 129, 131, 132, 133, 135, 136, 137, 138, 139, 141, 142, 143, 144, 145, 146, 147, 148, 149, 151, 152, 153, 154, 155, 156, 158, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 201, 202, 203, 204, 205, 206, 209, 210, 211, 212, 213, 214, 215, 216) and `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode'", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 33, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 34, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}], "start": 1755790787.064384, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 12.552, "width_percent": 0.256}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 44, 51, 53, 54, 56, 57, 58, 60, 62, 63, 64, 65, 66, 93, 95, 128, 129, 131, 132, 133, 135, 136, 137, 138, 139, 141, 142, 143, 144, 145, 146, 147, 148, 149, 151, 152, 153, 154, 155, 156, 158, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 201, 202, 203, 204, 205, 206, 209, 210, 211, 212, 213, 214, 215, 216) and `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode'", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}], "start": 1755790787.154354, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 12.807, "width_percent": 0.264}, {"sql": "select * from `menu_locations` where `menu_locations`.`menu_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 219}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 202}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 170}], "start": 1755790787.195657, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 13.071, "width_percent": 0.273}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (19, 21, 213)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 180}, {"index": 26, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.217943, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 13.344, "width_percent": 0.328}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (19, 21, 213) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 30, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 180}, {"index": 32, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}], "start": 1755790787.221518, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 13.672, "width_percent": 0.23}, {"sql": "select * from `pages` where `pages`.`id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 24, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 180}, {"index": 26, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.226848, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 13.902, "width_percent": 0.239}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (12) and `slugs`.`reference_type` = 'Shaqi\\\\Page\\\\Models\\\\Page'", "type": "query", "params": [], "bindings": ["Shaqi\\Page\\Models\\Page"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 30, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 180}, {"index": 32, "namespace": "view", "name": "theme.ninico::partials.headers.default", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/headers/default.blade.php", "line": 14}], "start": 1755790787.229131, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 14.14, "width_percent": 0.281}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (24, 26)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.241744, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 14.421, "width_percent": 0.243}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (24, 26) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.244929, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 14.664, "width_percent": 0.285}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 201 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [201], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.252486, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 14.95, "width_percent": 0.358}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 202 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [202], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.264019, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 15.307, "width_percent": 0.379}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 202 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 202], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.270494, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 15.686, "width_percent": 0.221}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (41)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.276162, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 15.908, "width_percent": 0.451}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (41) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.279116, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 16.359, "width_percent": 0.319}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 209 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 209], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.2869658, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 16.679, "width_percent": 0.175}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 203 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [203], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.291007, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 16.853, "width_percent": 0.285}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 203 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 203], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.297553, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 17.139, "width_percent": 0.273}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (97)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.302838, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 17.411, "width_percent": 0.234}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (97) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.304934, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 17.646, "width_percent": 0.166}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 206 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 206], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.324737, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 17.812, "width_percent": 1.201}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 204 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [204], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.333117, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 19.013, "width_percent": 0.187}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 204 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 204], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.3384192, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 19.2, "width_percent": 0.192}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (105)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.344161, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 19.392, "width_percent": 0.324}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (105) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.34688, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 19.715, "width_percent": 0.23}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 205 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 205], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.353314, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 19.945, "width_percent": 0.145}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.363006, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 20.09, "width_percent": 0.281}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (23) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.365273, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 20.371, "width_percent": 0.23}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 185 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [185], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.369719, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 20.601, "width_percent": 0.813}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (376)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.379623, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 21.415, "width_percent": 0.256}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (376) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.382017, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 21.67, "width_percent": 0.251}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 165 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [165], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.386517, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 21.922, "width_percent": 0.221}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 165 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 165], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.391885, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 22.143, "width_percent": 0.209}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 186 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [186], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.398941, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 22.352, "width_percent": 0.311}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 186 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 186], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.405282, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 22.663, "width_percent": 0.192}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (34, 41)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.410472, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 22.854, "width_percent": 0.311}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (34, 41) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.413605, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 23.165, "width_percent": 0.217}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 42 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 42], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.428458, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 23.383, "width_percent": 0.179}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 53 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 53], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.435506, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 23.561, "width_percent": 0.187}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 187 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [187], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.4386, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 23.749, "width_percent": 0.162}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 187 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 187], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.4435718, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 23.911, "width_percent": 0.251}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (47, 48, 49)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.45045, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 24.162, "width_percent": 0.294}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (47, 48, 49) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.453275, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 24.456, "width_percent": 0.2}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 54 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 54], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.460804, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 24.656, "width_percent": 0.273}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 56 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 56], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.466187, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 24.929, "width_percent": 0.217}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 57 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 57], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.471543, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 25.146, "width_percent": 0.23}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 188 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [188], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.4759362, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 25.376, "width_percent": 0.652}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 188 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 188], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.4836972, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 26.028, "width_percent": 0.277}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (60, 62, 65, 67)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.489615, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 26.304, "width_percent": 0.29}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (60, 62, 65, 67) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.493123, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 26.594, "width_percent": 0.281}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 60 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 60], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.503832, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 26.875, "width_percent": 0.209}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 62 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 62], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.511318, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 27.084, "width_percent": 0.294}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 63 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 63], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.51744, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 27.378, "width_percent": 0.2}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 65 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 65], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.522918, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 27.578, "width_percent": 0.226}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 189 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [189], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.5278978, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 27.804, "width_percent": 1.751}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 189 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 189], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.5371292, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 29.554, "width_percent": 0.358}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (70)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.543319, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 29.912, "width_percent": 0.754}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (70) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.547938, "duration": 0.0068200000000000005, "duration_str": "6.82ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 30.666, "width_percent": 2.905}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 58 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 58], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.5623581, "duration": 0.01098, "duration_str": "10.98ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 33.57, "width_percent": 4.677}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 190 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [190], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.579098, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 38.247, "width_percent": 0.775}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 190 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 190], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.5868618, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 39.022, "width_percent": 0.405}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (74, 78)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.5939188, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 39.427, "width_percent": 0.251}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (74, 78) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.596784, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 39.678, "width_percent": 0.345}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 66 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 66], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.6033762, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 40.023, "width_percent": 0.341}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 64 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 64], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.611834, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 40.364, "width_percent": 0.366}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 166 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 166], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.62136, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 40.73, "width_percent": 0.234}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 44 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.6253572, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 40.964, "width_percent": 0.213}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (380, 381, 383, 382, 384, 385, 386, 387)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.634917, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 41.177, "width_percent": 0.226}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (380, 381, 382, 383, 384, 385, 386, 387) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.638027, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 41.403, "width_percent": 0.23}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 170 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 170], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.651664, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 41.633, "width_percent": 0.251}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 169 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 169], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.65871, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 41.884, "width_percent": 0.239}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 168 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 168], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.666283, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 42.123, "width_percent": 0.162}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 167 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 167], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.673799, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 42.285, "width_percent": 0.469}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 172 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 172], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.683178, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 42.753, "width_percent": 1.227}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 171 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 171], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.693203, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 43.98, "width_percent": 1.026}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 174 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 174], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.703049, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 45.006, "width_percent": 0.239}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 173 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 173], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.71051, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 45.245, "width_percent": 0.226}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (28, 27)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.72011, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 45.47, "width_percent": 0.217}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (27, 28) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.721801, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 45.688, "width_percent": 0.158}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 212 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [212], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.726012, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 45.845, "width_percent": 0.281}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 216 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [216], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.737997, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 46.126, "width_percent": 0.285}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 216 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 216], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.745071, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 46.412, "width_percent": 0.856}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (145)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.751401, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 47.268, "width_percent": 0.298}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (145) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.754024, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 47.566, "width_percent": 0.281}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 213 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 213], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.761915, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 47.847, "width_percent": 0.204}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 215 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [215], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.76569, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 48.051, "width_percent": 0.196}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 215 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 215], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.769669, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 48.247, "width_percent": 0.239}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (149)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.775356, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 48.486, "width_percent": 0.213}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (149) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.7776, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 48.699, "width_percent": 0.277}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 211 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 211], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.78494, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 48.976, "width_percent": 0.23}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = 214 and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [214], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 3}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.7887518, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 49.206, "width_percent": 1.827}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 214 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 214], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.7986271, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 51.033, "width_percent": 0.273}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (168)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.803924, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 51.305, "width_percent": 0.204}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (168) and `slugs`.`reference_type` = 'Shaqi\\\\Ecommerce\\\\Models\\\\ProductCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\Ecommerce\\Models\\ProductCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 29, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 31, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": 1755790787.805867, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 51.51, "width_percent": 0.162}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = 'Shaqi\\\\Menu\\\\Models\\\\MenuNode' and `meta_boxes`.`reference_id` = 210 and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": ["Shaqi\\Menu\\Models\\MenuNode", 210], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/core/base/src/Models/Concerns/HasMetadata.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\Concerns\\HasMetadata.php", "line": 28}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Models/MenuNode.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Models\\MenuNode.php", "line": 99}, {"index": 30, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": 1755790787.81375, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 51.672, "width_percent": 0.273}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (264, 271)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/packages/menu/src/Menu.php", "file": "D:\\laragon\\www\\tesmods\\platform\\packages\\menu\\src\\Menu.php", "line": 270}, {"index": 25, "namespace": "view", "name": "theme.ninico::partials.menu", "file": "D:\\laragon\\www\\tesmods\\platform\\themes/ninico/partials/menu.blade.php", "line": 27}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\tesmods\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1755790787.8295028, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\tesmods\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "tesmods", "explain": null, "start_percent": 51.944, "width_percent": 0.256}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (264, 271) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.832576, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 52.2, "width_percent": 0.209}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.836481, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 52.409, "width_percent": 0.958}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.8499088, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 53.367, "width_percent": 0.324}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.854254, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 53.691, "width_percent": 0.221}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (272, 273, 274)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.859055, "duration": 0.00666, "duration_str": "6.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 53.912, "width_percent": 2.837}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (272, 273, 274) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.866894, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 56.749, "width_percent": 0.302}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.87232, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 57.051, "width_percent": 0.213}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.877908, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 57.264, "width_percent": 0.311}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.883092, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 57.575, "width_percent": 0.302}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.8864272, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 57.877, "width_percent": 0.264}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.8906028, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 58.141, "width_percent": 0.92}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (275, 276, 277, 278, 279)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.899095, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 59.061, "width_percent": 0.341}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (275, 276, 277, 278, 279) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.901688, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 59.402, "width_percent": 0.23}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.9096022, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 59.632, "width_percent": 0.43}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.9183738, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 60.062, "width_percent": 0.558}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.925704, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 60.62, "width_percent": 0.716}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.933795, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 61.336, "width_percent": 0.26}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.940449, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 61.595, "width_percent": 0.426}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.94513, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 62.021, "width_percent": 0.447}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.951245, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 62.469, "width_percent": 0.221}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (280, 281, 282, 284, 283, 285, 286, 287, 288)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.956022, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 62.69, "width_percent": 0.23}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (280, 281, 282, 283, 284, 285, 286, 287, 288) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.958739, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 62.92, "width_percent": 0.396}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.9700859, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 63.316, "width_percent": 0.239}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.9761808, "duration": 0.00851, "duration_str": "8.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 63.555, "width_percent": 3.625}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1755790787.99102, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 67.179, "width_percent": 1.712}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.004806, "duration": 0.01102, "duration_str": "11.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 68.891, "width_percent": 4.694}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0244222, "duration": 0.01011, "duration_str": "10.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 73.585, "width_percent": 4.306}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.041554, "duration": 0.00461, "duration_str": "4.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 77.891, "width_percent": 1.963}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.052818, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 79.854, "width_percent": 0.596}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.060592, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 80.451, "width_percent": 0.473}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.068842, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 80.923, "width_percent": 0.29}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.072136, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 81.213, "width_percent": 0.247}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0768518, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 81.46, "width_percent": 0.69}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (289, 290, 291, 292, 293, 294, 295)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0840032, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 82.15, "width_percent": 0.264}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (289, 290, 291, 292, 293, 294, 295) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0864549, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 82.414, "width_percent": 0.319}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.096488, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 82.734, "width_percent": 0.413}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.102632, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 83.147, "width_percent": 0.209}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1060011, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 83.355, "width_percent": 0.192}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.112401, "duration": 0.00609, "duration_str": "6.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 83.547, "width_percent": 2.594}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.121997, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 86.141, "width_percent": 0.209}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.126765, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 86.35, "width_percent": 0.315}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1342201, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 86.665, "width_percent": 0.264}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1366642, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 86.929, "width_percent": 0.196}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.139376, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 87.125, "width_percent": 0.149}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (296)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1433918, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 87.274, "width_percent": 0.984}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (296) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.146819, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 88.258, "width_percent": 0.426}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.152396, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 88.684, "width_percent": 0.375}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1549158, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 89.058, "width_percent": 0.183}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.158417, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 89.241, "width_percent": 0.69}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (297, 298, 299)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.16599, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 89.931, "width_percent": 0.341}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (297, 298, 299) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.168042, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 90.272, "width_percent": 0.328}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1743011, "duration": 0.00787, "duration_str": "7.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 90.6, "width_percent": 3.352}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1879659, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 93.952, "width_percent": 0.234}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.194304, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 94.186, "width_percent": 0.366}, {"sql": "select * from `menu_nodes` where `menu_nodes`.`parent_id` = ? and `menu_nodes`.`parent_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1975622, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 94.553, "width_percent": 0.264}, {"sql": "select * from `ec_product_categories` where `ec_product_categories`.`id` in (388, 390, 391, 392, 389, 393, 394, 395)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.202937, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 94.817, "width_percent": 0.243}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (388, 389, 390, 391, 392, 393, 394, 395) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2054648, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 95.059, "width_percent": 0.183}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2165818, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 95.243, "width_percent": 0.247}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2219799, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 95.49, "width_percent": 0.179}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.22849, "duration": 0.00613, "duration_str": "6.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 95.668, "width_percent": 2.611}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.237896, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 98.279, "width_percent": 0.2}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.244469, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 98.479, "width_percent": 0.183}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2505958, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 98.663, "width_percent": 0.26}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.255516, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 98.922, "width_percent": 0.213}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_type` = ? and `meta_boxes`.`reference_id` = ? and `meta_boxes`.`reference_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.2624252, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 99.135, "width_percent": 0.341}, {"sql": "select * from `widgets` where (`theme` = ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7497332, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 99.476, "width_percent": 0.196}, {"sql": "select * from `pages` where `pages`.`id` in (11, 8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7630868, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 99.672, "width_percent": 0.183}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (8, 11) and `slugs`.`reference_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.764111, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "tesmods", "explain": null, "start_percent": 99.855, "width_percent": 0.145}]}, "models": {"data": {"Shaqi\\Menu\\Models\\MenuNode": {"value": 280, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenuNode.php&line=1", "ajax": false, "filename": "MenuNode.php", "line": "?"}}, "Shaqi\\Slug\\Models\\Slug": {"value": 76, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Shaqi\\Ecommerce\\Models\\ProductCategory": {"value": 73, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductCategory.php&line=1", "ajax": false, "filename": "ProductCategory.php", "line": "?"}}, "Shaqi\\Widget\\Models\\Widget": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fwidget%2Fsrc%2FModels%2FWidget.php&line=1", "ajax": false, "filename": "Widget.php", "line": "?"}}, "Shaqi\\Menu\\Models\\Menu": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "Shaqi\\Menu\\Models\\MenuLocation": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fmenu%2Fsrc%2FModels%2FMenuLocation.php&line=1", "ajax": false, "filename": "MenuLocation.php", "line": "?"}}, "Shaqi\\Page\\Models\\Page": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fpackages%2Fpage%2Fsrc%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Shaqi\\Ecommerce\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Ftesmods%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 454, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "404 Not Found", "full_url": "https://tesmods.gc/themes/ninico/plugins/bootstrap/bootstrap.bundle.min.js.map", "action_name": null, "controller_action": null, "duration": "4.12s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2100001792 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2100001792\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-52604036 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-52604036\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2130951394 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">tesmods.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2576 characters\">shaqi_footprints_cookie=eyJpdiI6ImxHZGRqajhxQkFDcUMxb2VsdW16d1E9PSIsInZhbHVlIjoiajFBNUdGa2xjRmJPdHdMWFpTRiswdk14REJmbFh3TGp4V0JsUjZpSEhsSENiRWhSRlgwMEl3MTByNEs4UG5qanlaUEc2UlpiOFZVY0tad0g4NnRxc25hZFMvREs4K3U2NEkwcFZBQW9NK3FSdXBpZHpCYnkvUVVRU0xWMEEzMzUiLCJtYWMiOiI2OTk3Mzk3NTRlMzM2ZTdiMDExZWJmODJhZGExOTY4MmZhNmMzNjM3MDM1MWRmMTllMjY5NTUwYjBiODZjZjg1IiwidGFnIjoiIn0%3D; shaqi_footprints_cookie_data=eyJpdiI6ImF4TmlBNFJ3RTVxNFBhWUFLZkNIQWc9PSIsInZhbHVlIjoiVmxhcXR5bU4wOXU5dlhCTjVpK0JWZ2xrT3pZYXkwSllnQ3V6QklLcDRYZVpBeFhmWmJ3WGJwRTA1ZlZmcGkydGdRSHB3ZGJwdUJ3Uk9TYWYvcWtmalNoWi9mWW5SN2hzZTMvanN5cTB5TzBCaGs5NkhWVjljTFRzWWozK0pxN0p4Wjk1ckREWVhleTVjQmJZQ2ttOTlwQ1RDbHhtN3ZOMldQSUhrMExhOHFIc2VCRE9mLy83UmhCSEdkc00rckR1Y3N4NkxML1FNeGZyWitHUldZUkR0eHYyZTdBbEtMRUxqbUtGWithaGJjcVBIV2ZnaElSYllTbE96aTQ5MWNMSkNBa2wwOHM3RFQ0RW83aUpxZmVtYjU4aDhjbmo1clFSWTRHa2VzbEJLOWE3d1NoeVIvQnhrYUM1ejZUUy9qTkY0UDdsb1Q5Z3dybjVuLzZIaXRET1NadjJNSTJsbzNYd2U2aTFMOTMxYTJQejIzTU1IRFFpVm5Nd0xLdUk5Yjh2YXE1S01sRUQrMDB6OXI4U2pZeS9zQTAyaVI5TTN6bWVyV25vWGVtRWlHWkhLb1crRnVXVDJRbjJkNUY0OXM1MUUxMVpiRE9jd0wyQkg0Z0FpcDlzakRlT1NNWnVvSTI4MVNZaC9GZmhuTGNLWXBZRnNTNG9nRnY5Q3RxeTBxWUVRVjNoMDUvTkV1ajRpN096ZW4wOHlBPT0iLCJtYWMiOiJkYmI3NTYwZDUxZWJjY2I0ZmUxMmQ2YmU2YzUxYmQ3MjYzMzJlZmQ1MjMyZTBhNGNlOTUyMTgxMDhjMzhlNjU1IiwidGFnIjoiIn0%3D; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IlVHb3RYN0M5L2FRa3ovaExFNWVXQ3c9PSIsInZhbHVlIjoiMFU4V0hvaGRDVXI5K3RzdzdkMjZ1eDZ3cnhXQy9nRUpxekg1bFIxa0VoUTBTY2x6TTB4SlBGL1lkbVpOV2IyRitMcGsyMHFOS2ZMalRDQWZzM1g0MThCcWpheHNMSE5QT2g4ZUhhNFRxTnNUL0ZuKzhyNnpUbXI1V3JMSEhlT0laT2hXN0JUWWd4MEhXMXBhTzhaZkh2VkR4TXF4NXlDNnpUbzcxMjdnL1RwUzduZ05BMy9iTnZDNTlEdlBWaW5RNDkraERQRXI3RmgzNzEzclFrTkQ5S21kRmNWK1I0OVhGUGx2cXpzK0tJdz0iLCJtYWMiOiJhZDg0MDlkNzAxNWI5MjBiN2NlZmE5ZDRkNDExNDg3MjdiMTY1MWY0NDdjNjZkNmI5ZDA1ZTE0MWIwNzc4M2E1IiwidGFnIjoiIn0%3D; perf_dv6Tr4n=1; XSRF-TOKEN=eyJpdiI6IkhCdDUrM3o1VFRrZWpLbGdvUEJWb2c9PSIsInZhbHVlIjoiLzRHL3ZVZFh1SW9JVjM5aXI4RzBTNlV6VTdGbzJJZStNWTZiVWxkSkd1akdQQ2NrOE84OWdPbGxuTUZrekRycVFXUXdFSWdHaC85d09FbE44bHBodU1sdFNQSEt6cUN1SEpYWkZyTU1JQnZxbEFHZDQybHord2cwWlg4RWJDRUciLCJtYWMiOiIyZmQ1ZWY5ZDhmNTkwNzFkZGU2YjljYmQ5MTM3ZDNhNmEyYTA3YjQyY2U1NmIzOTEyMzVjNWJlYzJjOTZmZGI3IiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6Im5hYUI4RTAyTlM3cDNoanlvaTYza3c9PSIsInZhbHVlIjoib2Fud3ZKRkZWb3hJWmlFS1loN28yeDVFRmJlRFQvUERjUStUbnlwcXVSV2RPQ1huL0FBbEt3dVhqRUVpRWpWQ3FXdCtOWkFQQWxBRS9sRXB4WVlqVHdpaTVUTStjQjIyNUc0MVc0aHNZK1R3QmE3QWI1WnNHZExuN2xXZ1paNWIiLCJtYWMiOiI4ZTMyYjAzZGVmMWFmYzQ5NDZiNDVlYWUyYTQ0ZGEzYzFjOThlMzkyZjBlY2I0ZWJlMTllZTkxODQwY2ZmMTA3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130951394\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1159403628 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shaqi_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImxHZGRqajhxQkFDcUMxb2VsdW16d1E9PSIsInZhbHVlIjoiajFBNUdGa2xjRmJPdHdMWFpTRiswdk14REJmbFh3TGp4V0JsUjZpSEhsSENiRWhSRlgwMEl3MTByNEs4UG5qanlaUEc2UlpiOFZVY0tad0g4NnRxc25hZFMvREs4K3U2NEkwcFZBQW9NK3FSdXBpZHpCYnkvUVVRU0xWMEEzMzUiLCJtYWMiOiI2OTk3Mzk3NTRlMzM2ZTdiMDExZWJmODJhZGExOTY4MmZhNmMzNjM3MDM1MWRmMTllMjY5NTUwYjBiODZjZjg1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shaqi_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"884 characters\">eyJpdiI6ImF4TmlBNFJ3RTVxNFBhWUFLZkNIQWc9PSIsInZhbHVlIjoiVmxhcXR5bU4wOXU5dlhCTjVpK0JWZ2xrT3pZYXkwSllnQ3V6QklLcDRYZVpBeFhmWmJ3WGJwRTA1ZlZmcGkydGdRSHB3ZGJwdUJ3Uk9TYWYvcWtmalNoWi9mWW5SN2hzZTMvanN5cTB5TzBCaGs5NkhWVjljTFRzWWozK0pxN0p4Wjk1ckREWVhleTVjQmJZQ2ttOTlwQ1RDbHhtN3ZOMldQSUhrMExhOHFIc2VCRE9mLy83UmhCSEdkc00rckR1Y3N4NkxML1FNeGZyWitHUldZUkR0eHYyZTdBbEtMRUxqbUtGWithaGJjcVBIV2ZnaElSYllTbE96aTQ5MWNMSkNBa2wwOHM3RFQ0RW83aUpxZmVtYjU4aDhjbmo1clFSWTRHa2VzbEJLOWE3d1NoeVIvQnhrYUM1ejZUUy9qTkY0UDdsb1Q5Z3dybjVuLzZIaXRET1NadjJNSTJsbzNYd2U2aTFMOTMxYTJQejIzTU1IRFFpVm5Nd0xLdUk5Yjh2YXE1S01sRUQrMDB6OXI4U2pZeS9zQTAyaVI5TTN6bWVyV25vWGVtRWlHWkhLb1crRnVXVDJRbjJkNUY0OXM1MUUxMVpiRE9jd0wyQkg0Z0FpcDlzakRlT1NNWnVvSTI4MVNZaC9GZmhuTGNLWXBZRnNTNG9nRnY5Q3RxeTBxWUVRVjNoMDUvTkV1ajRpN096ZW4wOHlBPT0iLCJtYWMiOiJkYmI3NTYwZDUxZWJjY2I0ZmUxMmQ2YmU2YzUxYmQ3MjYzMzJlZmQ1MjMyZTBhNGNlOTUyMTgxMDhjMzhlNjU1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"484 characters\">eyJpdiI6IlVHb3RYN0M5L2FRa3ovaExFNWVXQ3c9PSIsInZhbHVlIjoiMFU4V0hvaGRDVXI5K3RzdzdkMjZ1eDZ3cnhXQy9nRUpxekg1bFIxa0VoUTBTY2x6TTB4SlBGL1lkbVpOV2IyRitMcGsyMHFOS2ZMalRDQWZzM1g0MThCcWpheHNMSE5QT2g4ZUhhNFRxTnNUL0ZuKzhyNnpUbXI1V3JMSEhlT0laT2hXN0JUWWd4MEhXMXBhTzhaZkh2VkR4TXF4NXlDNnpUbzcxMjdnL1RwUzduZ05BMy9iTnZDNTlEdlBWaW5RNDkraERQRXI3RmgzNzEzclFrTkQ5S21kRmNWK1I0OVhGUGx2cXpzK0tJdz0iLCJtYWMiOiJhZDg0MDlkNzAxNWI5MjBiN2NlZmE5ZDRkNDExNDg3MjdiMTY1MWY0NDdjNjZkNmI5ZDA1ZTE0MWIwNzc4M2E1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>perf_dv6Tr4n</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkhCdDUrM3o1VFRrZWpLbGdvUEJWb2c9PSIsInZhbHVlIjoiLzRHL3ZVZFh1SW9JVjM5aXI4RzBTNlV6VTdGbzJJZStNWTZiVWxkSkd1akdQQ2NrOE84OWdPbGxuTUZrekRycVFXUXdFSWdHaC85d09FbE44bHBodU1sdFNQSEt6cUN1SEpYWkZyTU1JQnZxbEFHZDQybHord2cwWlg4RWJDRUciLCJtYWMiOiIyZmQ1ZWY5ZDhmNTkwNzFkZGU2YjljYmQ5MTM3ZDNhNmEyYTA3YjQyY2U1NmIzOTEyMzVjNWJlYzJjOTZmZGI3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im5hYUI4RTAyTlM3cDNoanlvaTYza3c9PSIsInZhbHVlIjoib2Fud3ZKRkZWb3hJWmlFS1loN28yeDVFRmJlRFQvUERjUStUbnlwcXVSV2RPQ1huL0FBbEt3dVhqRUVpRWpWQ3FXdCtOWkFQQWxBRS9sRXB4WVlqVHdpaTVUTStjQjIyNUc0MVc0aHNZK1R3QmE3QWI1WnNHZExuN2xXZ1paNWIiLCJtYWMiOiI4ZTMyYjAzZGVmMWFmYzQ5NDZiNDVlYWUyYTQ0ZGEzYzFjOThlMzkyZjBlY2I0ZWJlMTllZTkxODQwY2ZmMTA3IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1159403628\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-326722046 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 21 Aug 2025 15:39:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-326722046\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1978624374 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1978624374\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "404 Not Found", "full_url": "https://tesmods.gc/themes/ninico/plugins/bootstrap/bootstrap.bundle.min.js.map"}, "badge": "404 Not Found"}}