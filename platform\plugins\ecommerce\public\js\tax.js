$((function(){var e="ecommerce-tax-rule-table",r="#"+e,a=$(".create-tax-rule-form-modal"),t=a.find(".modal-body"),o=a.find(".modal-title strong"),n=function(e){t.html(e.data.html),o.text(e.message||"...")};a.on("show.bs.modal",(function(){t.html('<div class=\'w-100 text-center py-3\'><div class="spinner-border" role="status">\n        <span class="visually-hidden">Loading...</span>\n    </div></div>'),o.text("...")})),$(document).off("click",".create-tax-rule-item").on("click",".create-tax-rule-item",(function(e){e.preventDefault();var r=$(e.currentTarget);a.modal("show"),$.ajax({url:r.find("[data-action=create]").data("href"),success:function(e){0==e.error?(n(e),Shaqi.initResources()):Shaqi.showError(e.message)},error:function(e){Shaqi.handleError(e)}})})),$(document).on("click",r+" .btn-edit-item",(function(e){e.preventDefault();var r=$(e.currentTarget);a.modal("show"),$.ajax({url:r.prop("href"),success:function(e){0==e.error?(n(e),Shaqi.initResources()):Shaqi.showError(e.message)},error:function(e){Shaqi.handleError(e)}})})),$(document).on("submit","#ecommerce-tax-rule-form",(function(r){r.preventDefault();var t=$(r.currentTarget);$.ajax({url:t.prop("action"),method:"POST",data:t.serializeArray(),success:function(r){0==r.error?(window.LaravelDataTables&&window.LaravelDataTables[e]&&LaravelDataTables[e].draw(),a.modal("hide")):Shaqi.showError(r.message)},error:function(e){Shaqi.handleError(e)}})}))}));
//# sourceMappingURL=tax.js.map