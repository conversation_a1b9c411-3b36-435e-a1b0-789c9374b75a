(()=>{"use strict";var e={6262:(e,t)=>{t.A=(e,t)=>{const o=e.__vccOpts||e;for(const[e,n]of t)o[e]=n;return o}}},t={};const o=Vue;var n={class:"row row-cards"},r={class:"col-md-9"},a={class:"card"},l={class:"card-header"},i={class:"card-title"},c={class:"card-body"},s={class:"mb-3"},d={class:"table table-bordered table-vcenter"},u={width:"90"},m=["src","alt"],p=["href"],h={key:0},g={key:1},v={class:"text-center"},f=["value","onInput"],k={class:"text-center"},y=["onClick"],E={class:"position-relative box-search-advance product mt-3"},b=["placeholder"],V={key:0,class:"loading-spinner"},N={key:1,class:"list-group list-group-flush overflow-auto",style:{"max-height":"25rem"}},w={href:"javascript:void(0)",class:"list-group-item list-group-item-action"},B={class:"row align-items-start"},S={class:"col-auto"},D={class:"col text-truncate"},x={key:0,class:"list-group list-group-flush"},C={key:0,class:"p-3"},M={class:"text-muted text-center mb-0"},O={key:2,class:"card-footer"},q={class:"pagination my-0 d-flex justify-content-end"},z=["aria-disabled"],j=["aria-disabled"],T={class:"row"},P={class:"col-sm-6"},A={class:"mb-3 position-relative"},U={class:"form-label",for:"txt-note"},F=["placeholder"],L={class:"col-sm-6"},I={class:"table table-borderless text-end table-vcenter"},R={key:0,class:"spinner-grow spinner-grow-sm",role:"status","aria-hidden":"true"},H={class:"fw-bold"},Q={key:0,class:"spinner-grow spinner-grow-sm",role:"status","aria-hidden":"true"},K={class:"fw-bold"},G={class:"spinner-grow spinner-grow-sm",role:"status","aria-hidden":"true"},J={type:"button",class:"btn btn-outline-primary btn-sm mb-1"},W={key:0,class:"d-block small fw-bold"},X={class:"spinner-grow spinner-grow-sm",role:"status","aria-hidden":"true"},Y={key:0},Z={type:"button",class:"btn btn-outline-primary btn-sm mb-1"},ee={key:0,class:"d-block small fw-bold"},te={class:"spinner-grow spinner-grow-sm",role:"status","aria-hidden":"true"},oe={class:"spinner-grow spinner-grow-sm",role:"status","aria-hidden":"true"},ne={class:"d-inline-block"},re={colspan:"2"},ae={for:"payment-method",class:"form-label"},le=["value"],ie={colspan:"2"},ce={for:"payment-status",class:"form-label"},se=["value"],de={colspan:"2"},ue={for:"payment-status",class:"form-label"},me={class:"form-hint"},pe={class:"card-footer"},_e={class:"d-flex justify-content-between align-items-center flex-wrap gap-2"},he={class:"mb-0 text-uppercase"},ge=["disabled"],ve={class:"col-md-3"},fe={class:"card"},ke={key:0},ye={class:"card-header"},Ee={class:"card-title"},be={class:"card-body"},Ve={class:"position-relative box-search-advance customer"},Ne=["placeholder"],we={key:0,class:"loading-spinner"},Be={key:1,class:"list-group list-group-flush overflow-auto",style:{"max-height":"25rem"}},Se={class:"list-group-item cursor-pointer"},De={class:"row align-items-center"},xe={class:"col"},Ce=["onClick"],Me={class:"flexbox-grid-default flexbox-align-items-center"},Oe={class:"row align-items-center"},qe={class:"col-auto"},ze={class:"col text-truncate"},je={class:"text-body d-block"},Te={key:0,class:"text-secondary text-truncate mt-n1"},Pe={key:0,class:"list-group-item"},$e={key:2,class:"card-footer"},Ae={class:"pagination my-0 d-flex justify-content-end"},Ue=["aria-disabled"],Fe=["aria-disabled"],Le={key:1},Ie={class:"card-header"},Re={class:"card-title"},He={class:"card-actions"},Qe={class:"card-body p-0"},Ke={class:"p-3"},Ge={class:"mb-3"},Je={class:"mb-1"},We={class:"mb-n1"},Xe={key:0,class:"d-flex justify-content-between align-items-center"},Ye=["data-bs-original-title"],Ze={class:"p-3"},et={class:"d-flex justify-content-between align-items-center mb-2"},tt={class:"mb-0"},ot={type:"button",class:"btn-action","data-bs-toggle":"tooltip","data-bs-title":"Update address"},nt={key:0,class:"mb-3"},rt=["value","selected"],at={class:"row mb-0"},lt={key:0},it={key:1},ct=["href"],st={class:"next-form-section"},dt={class:"next-form-grid"},ut={class:"mb-3 position-relative"},mt={class:"form-label"},pt={class:"row"},_t={class:"col-auto"},ht={class:"col"},gt={class:"input-group input-group-flat"},vt={class:"input-group-text"},ft={class:"next-form-grid"},kt={class:"mb-3 position-relative"},yt={class:"form-label"},Et={class:"position-relative"},bt={class:"form-label"},Vt=["placeholder"],Nt={key:0},wt={class:"alert alert-success",role:"alert"},Bt={class:"d-flex"},St={class:"alert-title"},Dt={class:"text-muted"},xt={class:"position-relative"},Ct={class:"form-check form-check-inline"},Mt={key:1},Ot={class:"mb-3 position-relative"},qt={class:"form-check form-check-inline"},zt=["disabled"],jt={class:"form-check-label"},Tt={key:0,class:"text-warning"},Pt={class:"form-select"},$t=["value","selected","data-shipping-method","data-shipping-option"],At={class:"alert alert-warning",role:"alert"},Ut={class:"d-inline-block ms-2 mb-0"};var Ft={class:"row align-items-center gap-2"},Lt={key:0,class:"text-success"},It={key:1},Rt={key:0,class:"w-100 w-sm-auto col-auto"};var Ht={key:0,class:"text-danger"},Qt={key:0},Kt={key:1,class:"text-warning"},Gt={class:"text-info ps-1"};const Jt={props:{item:{type:Object,default:function(){},required:!0}}};var Wt=function o(n){var r=t[n];if(void 0!==r)return r.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,o),a.exports}(6262);var Xt=["for"],Yt={key:0},Zt=["onInput","id"],eo={value:""},to=["value"],oo={key:1},no=["name","onInput","value","id"],ro=["for"],ao={key:2},lo=["name","onInput","value","id"],io=["for"],co={key:3},so=["onInput","name","id"],uo=["for"];const mo={props:{options:{type:Array,default:[],required:!0},product:{type:Object,default:{},required:!1}},data:function(){return{values:[]}},methods:{changeInput:function(e,t,o){this.values[t.id]||(this.values[t.id]={}),this.values[t.id]=e.target.value}}},po={props:{product:{type:Object,default:{},required:!1}},components:{ProductAvailable:(0,Wt.A)(Jt,[["render",function(e,t,n,r,a,l){return(0,o.openBlock)(),(0,o.createElementBlock)("div",null,[n.item.is_out_of_stock?((0,o.openBlock)(),(0,o.createElementBlock)("div",Ht,[(0,o.createElementVNode)("small",null," ("+(0,o.toDisplayString)(e.__("order.out_of_stock"))+")",1)])):n.item.with_storehouse_management?((0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,{key:1},[n.item.quantity>0?((0,o.openBlock)(),(0,o.createElementBlock)("small",Qt," ("+(0,o.toDisplayString)(n.item.quantity)+" "+(0,o.toDisplayString)(e.__("order.products_available"))+")",1)):((0,o.openBlock)(),(0,o.createElementBlock)("small",Kt," ("+(0,o.toDisplayString)(n.item.quantity)+" "+(0,o.toDisplayString)(e.__("order.products_available"))+")",1))],64)):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("span",Gt,"("+(0,o.toDisplayString)(n.item.formatted_price)+")",1)])}]]),ProductOption:(0,Wt.A)(mo,[["render",function(e,t,n,r,a,l){return(0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(n.options,(function(t){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{key:t.id},[(0,o.createElementVNode)("label",{class:(0,o.normalizeClass)(["form-label",{required:t.required}]),for:"form-select-"+n.product.id+"-"+t.id},(0,o.toDisplayString)(t.name),11,Xt),"dropdown"===t.option_type?((0,o.openBlock)(),(0,o.createElementBlock)("div",Yt,[(0,o.createElementVNode)("select",{class:"form-select",onInput:function(o){return l.changeInput(o,t,e.value)},id:"form-select-"+n.product.id+"-"+t.id},[(0,o.createElementVNode)("option",eo,(0,o.toDisplayString)(e.__("order.select_one")),1),((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(t.values,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("option",{key:e.option_value,value:e.option_value},(0,o.toDisplayString)(e.title),9,to)})),128))],40,Zt)])):(0,o.createCommentVNode)("",!0),"checkbox"===t.option_type?((0,o.openBlock)(),(0,o.createElementBlock)("div",oo,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(t.values,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{class:"form-check",key:e.id},[(0,o.createElementVNode)("input",{class:"form-check-input",type:"checkbox",name:"option-"+t.id,onInput:function(o){return l.changeInput(o,t,e)},value:e.option_value,id:"form-check-"+n.product.id+"-"+e.id},null,40,no),(0,o.createElementVNode)("label",{class:"form-check-label",for:"form-check-"+n.product.id+"-"+e.id},(0,o.toDisplayString)(e.title),9,ro)])})),128))])):(0,o.createCommentVNode)("",!0),"radio"===t.option_type?((0,o.openBlock)(),(0,o.createElementBlock)("div",ao,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(t.values,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{class:"form-check",key:e.id},[(0,o.createElementVNode)("input",{class:"form-check-input",type:"radio",name:"option-"+t.id,onInput:function(o){return l.changeInput(o,t,e)},value:e.option_value,id:"form-check-"+n.product.id+"-"+e.id},null,40,lo),(0,o.createElementVNode)("label",{class:"form-check-label",for:"form-check-"+n.product.id+"-"+e.id},(0,o.toDisplayString)(e.title),9,io)])})),128))])):(0,o.createCommentVNode)("",!0),"field"===t.option_type?((0,o.openBlock)(),(0,o.createElementBlock)("div",co,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(t.values,(function(r){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{class:"form-floating mb-3",key:r.id},[(0,o.createElementVNode)("input",{type:"text",class:"form-control",onInput:function(e){return l.changeInput(e,t,r)},name:"option-"+t.id,id:"form-input-"+n.product.id+"-"+r.id,placeholder:"..."},null,40,so),(0,o.createElementVNode)("label",{for:"form-input-"+n.product.id+"-"+r.id},(0,o.toDisplayString)(r.title||e.__("order.enter_free_text")),9,uo)])})),128))])):(0,o.createCommentVNode)("",!0)])})),128)}]])}};var _o={class:"row"},ho={class:"col-md-6 mb-3 position-relative"},go={class:"form-label"},vo={class:"col-md-6 mb-3 position-relative"},fo={class:"form-label"},ko={class:"col-md-6 mb-3 position-relative"},yo={class:"form-label"},Eo={class:"col-md-6 mb-3 position-relative"},bo={class:"form-label"},Vo={class:"col-12 mb-3 position-relative"},No={class:"form-label"},wo=["value"],Bo={class:"col-md-6 mb-3 position-relative"},So={class:"form-label"},Do=["value"],xo={class:"col-md-6 mb-3 position-relative"},Co={class:"form-label"},Mo=["value"],Oo={key:0,class:"col-md-6 mb-3 position-relative"},qo={class:"form-label"},zo={class:"mb-3 position-relative"},jo={class:"form-label"},To={class:"row"},Po={class:"col-md-6 mb-3 position-relative"},$o={class:"form-label"},Ao={class:"col-md-6 mb-3 position-relative"},Uo={class:"form-label"},Fo={class:"col-md-6 mb-3 position-relative"},Lo={class:"form-label"},Io={class:"col-md-6 mb-3 position-relative"},Ro={class:"form-label"},Ho={class:"col-12 mb-3 position-relative"},Qo={class:"form-label"},Ko=["selected","value"],Go={class:"col-md-6 mb-3 position-relative"},Jo={class:"form-label"},Wo=["selected","value"],Xo={class:"col-md-6 mb-3 position-relative"},Yo={class:"form-label"},Zo=["value"],en={key:0,class:"col-md-6 mb-3 position-relative"},tn={class:"form-label"};const on={props:{customer:{type:Object,default:{}},address:{type:Object,default:{}},zip_code_enabled:{type:Number,default:0},use_location_data:{type:Number,default:0}},data:function(){return{countries:[],states:[],cities:[]}},methods:{shownEditAddress:function(e){this.loadCountries(e),this.address.country&&this.loadStates(e,this.address.country),this.address.state&&this.loadCities(e,this.address.state)},loadCountries:function(){var e=this;_.isEmpty(e.countries)&&axios.get(route("ajax.countries.list")).then((function(t){e.countries=t.data.data})).catch((function(e){Shaqi.handleError(e.response.data)}))},loadStates:function(e,t){if(!this.use_location_data)return!1;var o=this;axios.get(route("ajax.states-by-country",{country_id:t||e.target.value})).then((function(e){o.states=e.data.data})).catch((function(e){Shaqi.handleError(e.response.data)}))},loadCities:function(e,t){if(!this.use_location_data)return!1;var o=this;axios.get(route("ajax.cities-by-state",{state_id:t||e.target.value})).then((function(e){o.cities=e.data.data})).catch((function(e){Shaqi.handleError(e.response.data)}))}},watch:{address:function(e){this.address.country&&this.loadStates(e,this.address.country),this.address.state&&this.loadCities(e,this.address.state)}}};var nn={class:"mb-3 position-relative"},rn={class:"form-label"},an={class:"row"},ln={class:"col-6 mb-3 position-relative"},cn={class:"form-label"},sn={class:"col-6 mb-3 position-relative"},dn={class:"form-label"},un={class:"mb-3 position-relative"},mn={class:"form-label"},pn={value:"published"},_n={value:"draft"},hn={value:"pending"},gn={class:"form-check"},vn={class:"form-check-label"},fn={class:"mb-3 position-relative"},kn={class:"form-label"},yn={class:"form-check"},En={class:"form-check-label"},bn={key:1,class:"position-relative"},Vn={class:"form-check-label"},Nn={class:"text-primary"};const wn={props:{store:{type:Object,default:function(){return{}}}},data:function(){return{product:{}}},methods:{resetProductData:function(){this.product={name:null,price:0,sku:null,status:"published",with_storehouse_management:!1,allow_checkout_when_out_of_stock:!1,quantity:0,tax_price:0}}},mounted:function(){var e=this;this.resetProductData(),$event.on("ec-modal:open",(function(t){"add-product-item"===t&&e.resetProductData()}))}};function Bn(e){return Bn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Bn(e)}function Sn(e,t){var o=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),o.push.apply(o,n)}return o}function Dn(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?Sn(Object(o),!0).forEach((function(t){xn(e,t,o[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):Sn(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function xn(e,t,o){return(t=function(e){var t=function(e,t){if("object"!=Bn(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,t||"default");if("object"!=Bn(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Bn(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}const Cn={props:{products:{type:Array,default:function(){return[]}},product_ids:{type:Array,default:function(){return[]}},customer_id:{type:Number,default:function(){return null}},customer:{type:Object,default:function(){return{email:"<EMAIL>"}}},customer_addresses:{type:Array,default:function(){return[]}},customer_address:{type:Object,default:function(){return{name:null,email:null,address:null,phone:null,country:null,state:null,city:null,zip_code:null}}},customer_order_numbers:{type:Number,default:function(){return 0}},sub_amount:{type:Number,default:function(){return 0}},sub_amount_label:{type:String,default:function(){return""}},tax_amount:{type:Number,default:function(){return 0}},tax_amount_label:{type:String,default:function(){return""}},total_amount:{type:Number,default:function(){return 0}},total_amount_label:{type:String,default:function(){return""}},coupon_code:{type:String,default:function(){return""}},promotion_amount:{type:Number,default:function(){return 0}},promotion_amount_label:{type:String,default:function(){return""}},discount_amount:{type:Number,default:function(){return 0}},discount_amount_label:{type:String,default:function(){return""}},discount_description:{type:String,default:function(){return null}},shipping_amount:{type:Number,default:function(){return 0}},shipping_amount_label:{type:String,default:function(){return""}},shipping_method:{type:String,default:function(){return"default"}},shipping_option:{type:String,default:function(){return""}},is_selected_shipping:{type:Boolean,default:function(){return!1}},shipping_method_name:{type:String,default:function(){return"order.free_shipping"}},payment_method:{type:String,default:function(){return"cod"}},currency:{type:String,default:function(){return null},required:!0},zip_code_enabled:{type:Number,default:function(){return 0},required:!0},use_location_data:{type:Number,default:function(){return 0}},is_tax_enabled:{type:Number,default:function(){return!0}},paymentMethods:{type:Object,default:function(){return{}}},paymentStatuses:{type:Object,default:function(){return{}}}},data:function(){return{list_products:{data:[]},hidden_product_search_panel:!0,loading:!1,checking:!1,note:null,customers:{data:[]},hidden_customer_search_panel:!0,customer_keyword:null,shipping_type:"free-shipping",shipping_methods:{},discount_type_unit:this.currency,discount_type:"amount",child_discount_description:this.discount_description,has_invalid_coupon:!1,has_applied_discount:this.discount_amount>0,discount_custom_value:0,child_coupon_code:this.coupon_code,child_customer:this.customer,child_customer_id:this.customer_id,child_customer_order_numbers:this.customer_order_numbers,child_customer_addresses:this.customer_addresses,child_customer_address:this.customer_address,child_products:this.products,child_product_ids:this.product_ids,child_sub_amount:this.sub_amount,child_sub_amount_label:this.sub_amount_label,child_tax_amount:this.tax_amount,child_tax_amount_label:this.tax_amount_label,child_total_amount:this.total_amount,child_total_amount_label:this.total_amount_label,child_promotion_amount:this.promotion_amount,child_promotion_amount_label:this.promotion_amount_label,child_discount_amount:this.discount_amount,child_discount_amount_label:this.discount_amount_label,child_shipping_amount:this.shipping_amount,child_shipping_amount_label:this.shipping_amount_label,child_shipping_method:this.shipping_method,child_shipping_option:this.shipping_option,child_shipping_method_name:this.shipping_method_name,child_is_selected_shipping:this.is_selected_shipping,child_payment_method:this.payment_method,child_transaction_id:null,child_payment_status:"pending",productSearchRequest:null,timeoutProductRequest:null,customerSearchRequest:null,checkDataOrderRequest:null,store:{id:0,name:null},is_available_shipping:!1}},components:{ProductAction:(0,Wt.A)(po,[["render",function(e,t,n,r,a,l){var i=(0,o.resolveComponent)("ProductAvailable"),c=(0,o.resolveComponent)("ProductOption");return(0,o.openBlock)(),(0,o.createElementBlock)("div",Ft,[(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["col d-flex align-content-center",{"overflow-auto":n.product.variation_attributes}])},[n.product.variation_attributes?((0,o.openBlock)(),(0,o.createElementBlock)("span",Lt,(0,o.toDisplayString)(n.product.variation_attributes),1)):((0,o.openBlock)(),(0,o.createElementBlock)("span",It,(0,o.toDisplayString)(n.product.name),1)),n.product.is_variation||!n.product.variations.length?((0,o.openBlock)(),(0,o.createBlock)(i,{key:2,item:n.product},null,8,["item"])):(0,o.createCommentVNode)("",!0)],2),(0,o.withDirectives)((0,o.createVNode)(c,{ref:"product_options_"+n.product.id,product:n.product,options:n.product.product_options},null,8,["product","options"]),[[o.vShow,!n.product.is_variation]]),!n.product.is_variation&&n.product.variations.length||n.product.is_out_of_stock?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createElementBlock)("div",Rt,[(0,o.createElementVNode)("button",{class:"btn btn-outline-primary btn-sm",type:"button",onClick:t[0]||(t[0]=function(t){return e.$emit("select-product",n.product,e.$refs["product_options_"+n.product.id]||[])})},[t[1]||(t[1]=(0,o.createElementVNode)("i",{class:"icon-sm ti ti-plus"},null,-1)),(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.__("order.add")),1)])]))])}]]),OrderCustomerAddress:(0,Wt.A)(on,[["render",function(e,t,n,r,a,l){var i=(0,o.resolveComponent)("ec-modal");return(0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,null,[(0,o.createVNode)(i,{id:"add-customer",title:e.__("order.create_new_customer"),"ok-title":e.__("order.save"),"cancel-title":e.__("order.cancel"),onShown:t[12]||(t[12]=function(e){return l.loadCountries(e)}),onOk:t[13]||(t[13]=function(t){return e.$emit("create-new-customer",t)})},{default:(0,o.withCtx)((function(){return[(0,o.createElementVNode)("div",_o,[(0,o.createElementVNode)("div",ho,[(0,o.createElementVNode)("label",go,(0,o.toDisplayString)(e.__("order.name")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control","onUpdate:modelValue":t[0]||(t[0]=function(e){return n.address.name=e})},null,512),[[o.vModelText,n.address.name]])]),(0,o.createElementVNode)("div",vo,[(0,o.createElementVNode)("label",fo,(0,o.toDisplayString)(e.__("order.phone")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control","onUpdate:modelValue":t[1]||(t[1]=function(e){return n.address.phone=e})},null,512),[[o.vModelText,n.address.phone]])]),(0,o.createElementVNode)("div",ko,[(0,o.createElementVNode)("label",yo,(0,o.toDisplayString)(e.__("order.address")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control","onUpdate:modelValue":t[2]||(t[2]=function(e){return n.address.address=e})},null,512),[[o.vModelText,n.address.address]])]),(0,o.createElementVNode)("div",Eo,[(0,o.createElementVNode)("label",bo,(0,o.toDisplayString)(e.__("order.email")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"email",class:"form-control","onUpdate:modelValue":t[3]||(t[3]=function(e){return n.address.email=e})},null,512),[[o.vModelText,n.address.email]])]),(0,o.createElementVNode)("div",Vo,[(0,o.createElementVNode)("label",No,(0,o.toDisplayString)(e.__("order.country")),1),(0,o.withDirectives)((0,o.createElementVNode)("select",{class:"form-select","onUpdate:modelValue":t[4]||(t[4]=function(e){return n.address.country=e}),onChange:t[5]||(t[5]=function(e){return l.loadStates(e)})},[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.countries,(function(e,t){return(0,o.openBlock)(),(0,o.createElementBlock)("option",{value:t,key:t},(0,o.toDisplayString)(e),9,wo)})),128))],544),[[o.vModelSelect,n.address.country]])]),(0,o.createElementVNode)("div",Bo,[(0,o.createElementVNode)("label",So,(0,o.toDisplayString)(e.__("order.state")),1),n.use_location_data?(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("select",{key:0,"onUpdate:modelValue":t[6]||(t[6]=function(e){return n.address.state=e}),onChange:t[7]||(t[7]=function(e){return l.loadCities(e)}),class:"form-select customer-address-state"},[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.states,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("option",{value:e.id,key:e.id},(0,o.toDisplayString)(e.name),9,Do)})),128))],544)),[[o.vModelSelect,n.address.state]]):(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("input",{key:1,type:"text",class:"form-control customer-address-state","onUpdate:modelValue":t[8]||(t[8]=function(e){return n.address.state=e})},null,512)),[[o.vModelText,n.address.state]])]),(0,o.createElementVNode)("div",xo,[(0,o.createElementVNode)("label",Co,(0,o.toDisplayString)(e.__("order.city")),1),n.use_location_data?(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("select",{key:0,"onUpdate:modelValue":t[9]||(t[9]=function(e){return n.address.city=e}),class:"form-select customer-address-city"},[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.cities,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("option",{value:e.id,key:e.id},(0,o.toDisplayString)(e.name),9,Mo)})),128))],512)),[[o.vModelSelect,n.address.city]]):(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("input",{key:1,type:"text",class:"form-control customer-address-city","onUpdate:modelValue":t[10]||(t[10]=function(e){return n.address.city=e})},null,512)),[[o.vModelText,n.address.city]])]),n.zip_code_enabled?((0,o.openBlock)(),(0,o.createElementBlock)("div",Oo,[(0,o.createElementVNode)("label",qo,(0,o.toDisplayString)(e.__("order.zip_code")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control","onUpdate:modelValue":t[11]||(t[11]=function(e){return n.address.zip_code=e})},null,512),[[o.vModelText,n.address.zip_code]])])):(0,o.createCommentVNode)("",!0)])]})),_:1},8,["title","ok-title","cancel-title"]),(0,o.createVNode)(i,{id:"edit-email",title:e.__("order.update_email"),"ok-title":e.__("order.update"),"cancel-title":e.__("order.close"),onOk:t[15]||(t[15]=function(t){return e.$emit("update-customer-email",t)})},{default:(0,o.withCtx)((function(){return[(0,o.createElementVNode)("div",zo,[(0,o.createElementVNode)("label",jo,(0,o.toDisplayString)(e.__("order.email")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{class:"form-control","onUpdate:modelValue":t[14]||(t[14]=function(e){return n.customer.email=e})},null,512),[[o.vModelText,n.customer.email]])])]})),_:1},8,["title","ok-title","cancel-title"]),(0,o.createVNode)(i,{id:"edit-address",title:e.__("order.update_address"),"ok-title":e.__("order.save"),"cancel-title":e.__("order.cancel"),onShown:l.shownEditAddress,onOk:t[28]||(t[28]=function(t){return e.$emit("update-order-address",t)})},{default:(0,o.withCtx)((function(){return[(0,o.createElementVNode)("div",To,[(0,o.createElementVNode)("div",Po,[(0,o.createElementVNode)("label",$o,(0,o.toDisplayString)(e.__("order.name")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control customer-address-name","onUpdate:modelValue":t[16]||(t[16]=function(e){return n.address.name=e})},null,512),[[o.vModelText,n.address.name]])]),(0,o.createElementVNode)("div",Ao,[(0,o.createElementVNode)("label",Uo,(0,o.toDisplayString)(e.__("order.phone")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control customer-address-phone","onUpdate:modelValue":t[17]||(t[17]=function(e){return n.address.phone=e})},null,512),[[o.vModelText,n.address.phone]])]),(0,o.createElementVNode)("div",Fo,[(0,o.createElementVNode)("label",Lo,(0,o.toDisplayString)(e.__("order.address")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control customer-address-address","onUpdate:modelValue":t[18]||(t[18]=function(e){return n.address.address=e})},null,512),[[o.vModelText,n.address.address]])]),(0,o.createElementVNode)("div",Io,[(0,o.createElementVNode)("label",Ro,(0,o.toDisplayString)(e.__("order.email")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control customer-address-email","onUpdate:modelValue":t[19]||(t[19]=function(e){return n.address.email=e})},null,512),[[o.vModelText,n.address.email]])]),(0,o.createElementVNode)("div",Ho,[(0,o.createElementVNode)("label",Qo,(0,o.toDisplayString)(e.__("order.country")),1),(0,o.withDirectives)((0,o.createElementVNode)("select",{class:"form-select customer-address-country","onUpdate:modelValue":t[20]||(t[20]=function(e){return n.address.country=e}),onChange:t[21]||(t[21]=function(e){return l.loadStates(e)})},[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.countries,(function(e,t){return(0,o.openBlock)(),(0,o.createElementBlock)("option",{selected:n.address.country===t,value:t,key:t},(0,o.toDisplayString)(e),9,Ko)})),128))],544),[[o.vModelSelect,n.address.country]])]),(0,o.createElementVNode)("div",Go,[(0,o.createElementVNode)("label",Jo,(0,o.toDisplayString)(e.__("order.state")),1),n.use_location_data?(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("select",{key:0,class:"form-select customer-address-state","onUpdate:modelValue":t[22]||(t[22]=function(e){return n.address.state=e}),onChange:t[23]||(t[23]=function(e){return l.loadCities(e)})},[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.states,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("option",{selected:n.address.state===e.id,value:e.id,key:e.id},(0,o.toDisplayString)(e.name),9,Wo)})),128))],544)),[[o.vModelSelect,n.address.state]]):(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("input",{key:1,type:"text",class:"form-control customer-address-state","onUpdate:modelValue":t[24]||(t[24]=function(e){return n.address.state=e})},null,512)),[[o.vModelText,n.address.state]])]),(0,o.createElementVNode)("div",Xo,[(0,o.createElementVNode)("label",Yo,(0,o.toDisplayString)(e.__("order.city")),1),n.use_location_data?(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("select",{key:0,"onUpdate:modelValue":t[25]||(t[25]=function(e){return n.address.city=e}),class:"form-select customer-address-city"},[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.cities,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("option",{value:e.id,key:e.id},(0,o.toDisplayString)(e.name),9,Zo)})),128))],512)),[[o.vModelSelect,n.address.city]]):(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("input",{key:1,type:"text",class:"form-control customer-address-city","onUpdate:modelValue":t[26]||(t[26]=function(e){return n.address.city=e})},null,512)),[[o.vModelText,n.address.city]])]),n.zip_code_enabled?((0,o.openBlock)(),(0,o.createElementBlock)("div",en,[(0,o.createElementVNode)("label",tn,(0,o.toDisplayString)(e.__("order.zip_code")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control customer-address-zip-code","onUpdate:modelValue":t[27]||(t[27]=function(e){return n.address.zip_code=e})},null,512),[[o.vModelText,n.address.zip_code]])])):(0,o.createCommentVNode)("",!0)])]})),_:1},8,["title","ok-title","cancel-title","onShown"])],64)}]]),AddProductModal:(0,Wt.A)(wn,[["render",function(e,t,n,r,a,l){var i=(0,o.resolveComponent)("ec-modal");return(0,o.openBlock)(),(0,o.createBlock)(i,{id:"add-product-item",title:e.__("order.add_product"),"ok-title":e.__("order.save"),"cancel-title":e.__("order.cancel"),onShown:t[7]||(t[7]=function(e){return l.resetProductData()}),onOk:t[8]||(t[8]=function(t){return e.$emit("create-product",t,e.product)})},{default:(0,o.withCtx)((function(){return[(0,o.createElementVNode)("div",nn,[(0,o.createElementVNode)("label",rn,(0,o.toDisplayString)(e.__("order.name")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control","onUpdate:modelValue":t[0]||(t[0]=function(t){return e.product.name=t})},null,512),[[o.vModelText,e.product.name]])]),(0,o.createElementVNode)("div",an,[(0,o.createElementVNode)("div",ln,[(0,o.createElementVNode)("label",cn,(0,o.toDisplayString)(e.__("order.price")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control","onUpdate:modelValue":t[1]||(t[1]=function(t){return e.product.price=t})},null,512),[[o.vModelText,e.product.price]])]),(0,o.createElementVNode)("div",sn,[(0,o.createElementVNode)("label",dn,(0,o.toDisplayString)(e.__("order.sku_optional")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control","onUpdate:modelValue":t[2]||(t[2]=function(t){return e.product.sku=t})},null,512),[[o.vModelText,e.product.sku]])])]),(0,o.createElementVNode)("div",un,[(0,o.createElementVNode)("label",mn,(0,o.toDisplayString)(e.__("order.status")),1),(0,o.withDirectives)((0,o.createElementVNode)("select",{class:"form-select","onUpdate:modelValue":t[3]||(t[3]=function(t){return e.product.status=t})},[(0,o.createElementVNode)("option",pn,(0,o.toDisplayString)(e.__("order.published")),1),(0,o.createElementVNode)("option",_n,(0,o.toDisplayString)(e.__("order.draft")),1),(0,o.createElementVNode)("option",hn,(0,o.toDisplayString)(e.__("order.pending")),1)],512),[[o.vModelSelect,e.product.status]])]),(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)({"position-relative":!0,"mb-3":e.product.with_storehouse_management||n.store&&n.store.id})},[(0,o.createElementVNode)("label",gn,[(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"checkbox",class:"form-check-input","onUpdate:modelValue":t[4]||(t[4]=function(t){return e.product.with_storehouse_management=t}),value:"1"},null,512),[[o.vModelCheckbox,e.product.with_storehouse_management]]),(0,o.createElementVNode)("span",vn,(0,o.toDisplayString)(e.__("order.with_storehouse_management")),1)])],2),e.product.with_storehouse_management?((0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,{key:0},[(0,o.createElementVNode)("div",fn,[(0,o.createElementVNode)("label",kn,(0,o.toDisplayString)(e.__("order.quantity")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"number",min:"1",class:"form-control","onUpdate:modelValue":t[5]||(t[5]=function(t){return e.product.quantity=t})},null,512),[[o.vModelText,e.product.quantity]])]),(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)({"position-relative":!0,"mb-3":n.store&&n.store.id})},[(0,o.createElementVNode)("label",yn,[(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"checkbox",class:"form-check-input","onUpdate:modelValue":t[6]||(t[6]=function(t){return e.product.allow_checkout_when_out_of_stock=t}),value:"1"},null,512),[[o.vModelCheckbox,e.product.allow_checkout_when_out_of_stock]]),(0,o.createElementVNode)("span",En,(0,o.toDisplayString)(e.__("order.allow_customer_checkout_when_this_product_out_of_stock")),1)])],2)],64)):(0,o.createCommentVNode)("",!0),n.store&&n.store.id?((0,o.openBlock)(),(0,o.createElementBlock)("div",bn,[(0,o.createElementVNode)("label",Vn,[(0,o.createTextVNode)((0,o.toDisplayString)(e.__("order.store"))+": ",1),(0,o.createElementVNode)("strong",Nn,(0,o.toDisplayString)(n.store.name),1)])])):(0,o.createCommentVNode)("",!0)]})),_:1},8,["title","ok-title","cancel-title"])}]])},mounted:function(){var e=this;$(document).on("click","body",(function(t){var o=$(".box-search-advance");o.is(t.target)||0!==o.has(t.target).length||(e.hidden_customer_search_panel=!0,e.hidden_product_search_panel=!0)})),e.product_ids&&e.checkDataBeforeCreateOrder()},methods:{loadListCustomersForSearch:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=this;o.hidden_customer_search_panel=!1,$(".textbox-advancesearch.customer").closest(".box-search-advance.customer").find(".panel").addClass("active"),(_.isEmpty(o.customers.data)||t)&&(o.loading=!0,o.customerSearchRequest&&o.customerSearchRequest.abort(),o.customerSearchRequest=new AbortController,axios.get(route("customers.get-list-customers-for-search",{keyword:o.customer_keyword,page:e}),{signal:o.customerSearchRequest.signal}).then((function(e){o.customers=e.data.data,o.loading=!1})).catch((function(e){axios.isCancel(e)||(o.loading=!1,Shaqi.handleError(e.response.data))})))},handleSearchCustomer:function(e){if(e!==this.customer_keyword){var t=this;this.customer_keyword=e,setTimeout((function(){t.loadListCustomersForSearch(1,!0)}),500)}},loadListProductsAndVariations:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],o=this;!(arguments.length>2&&void 0!==arguments[2])||arguments[2]?(o.hidden_product_search_panel=!1,$(".textbox-advancesearch.product").closest(".box-search-advance.product").find(".panel").addClass("active")):o.hidden_product_search_panel=!0,(_.isEmpty(o.list_products.data)||t)&&(o.loading=!0,o.productSearchRequest&&o.productSearchRequest.abort(),o.productSearchRequest=new AbortController,axios.get(route("products.get-all-products-and-variations",{keyword:o.product_keyword,page:e,product_ids:o.child_product_ids}),{signal:o.productSearchRequest.signal}).then((function(e){o.list_products=e.data.data,o.loading=!1})).catch((function(e){axios.isCancel(e)||(Shaqi.handleError(e.response.data),o.loading=!1)})))},handleSearchProduct:function(e){if(e!==this.product_keyword){var t=this;t.product_keyword=e,t.timeoutProductRequest&&clearTimeout(t.timeoutProductRequest),t.timeoutProductRequest=setTimeout((function(){t.loadListProductsAndVariations(1,!0)}),1e3)}},selectProductVariant:function(e,t){var o=this;if(_.isEmpty(e)&&e.is_out_of_stock)return Shaqi.showError(o.__("order.cant_select_out_of_stock_product")),!1;var n=e.product_options.filter((function(e){return e.required}));!e.is_variation&&e.variations.length||(t=o.$refs["product_actions_"+e.original_product_id][0].$refs["product_options_"+e.original_product_id]);var r=t.values;if(n.length){var a=[];if(n.forEach((function(e){r[e.id]||a.push(o.__("order.please_choose_product_option")+": "+e.name)})),a&&a.length)return void a.forEach((function(e){Shaqi.showError(e)}))}var l=[];e.product_options.map((function(e){r[e.id]&&(l[e.id]={option_type:e.option_type,values:r[e.id]})})),o.child_products.push({id:e.id,quantity:1,options:l}),o.checkDataBeforeCreateOrder(),o.hidden_product_search_panel=!0},selectCustomer:function(e){this.child_customer=e,this.child_customer_id=e.id,this.loadCustomerAddress(this.child_customer_id),this.getOrderNumbers()},checkDataBeforeCreateOrder:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=this,r=Dn(Dn({},n.getOrderFormData()),e);n.checking=!0,n.checkDataOrderRequest&&n.checkDataOrderRequest.abort(),n.checkDataOrderRequest=new AbortController,axios.post(route("orders.check-data-before-create-order"),r,{signal:n.checkDataOrderRequest.signal}).then((function(e){var r=e.data.data;r.update_context_data&&(n.child_products=r.products,n.child_product_ids=_.map(r.products,"id"),n.child_sub_amount=r.sub_amount,n.child_sub_amount_label=r.sub_amount_label,n.child_tax_amount=r.tax_amount,n.child_tax_amount_label=r.tax_amount_label,n.child_promotion_amount=r.promotion_amount,n.child_promotion_amount_label=r.promotion_amount_label,n.child_discount_amount=r.discount_amount,n.child_discount_amount_label=r.discount_amount_label,n.child_shipping_amount=r.shipping_amount,n.child_shipping_amount_label=r.shipping_amount_label,n.child_total_amount=r.total_amount,n.child_total_amount_label=r.total_amount_label,n.shipping_methods=r.shipping_methods,n.child_shipping_method_name=r.shipping_method_name,n.child_shipping_method=r.shipping_method,n.child_shipping_option=r.shipping_option,n.is_available_shipping=r.is_available_shipping,n.store=r.store&&r.store.id?r.store:{id:0,name:null}),e.data.error?(Shaqi.showError(e.data.message),o&&o()):t&&t(),n.checking=!1})).catch((function(e){axios.isCancel(e)||(n.checking=!1,Shaqi.handleError(e.response.data))}))},getOrderFormData:function(){var e=[];return _.each(this.child_products,(function(t){e.push({id:t.id,quantity:t.select_qty,options:t.options})})),{products:e,payment_method:this.child_payment_method,payment_status:this.child_payment_status,shipping_method:this.child_shipping_method,shipping_option:this.child_shipping_option,shipping_amount:this.child_shipping_amount,discount_amount:this.child_discount_amount,discount_description:this.child_discount_description,coupon_code:this.child_coupon_code,customer_id:this.child_customer_id,note:this.note,sub_amount:this.child_sub_amount,tax_amount:this.child_tax_amount,amount:this.child_total_amount,customer_address:this.child_customer_address,discount_type:this.discount_type,discount_custom_value:this.discount_custom_value,shipping_type:this.shipping_type,transaction_id:this.child_transaction_id}},removeCustomer:function(){this.child_customer=this.customer,this.child_customer_id=null,this.child_customer_addresses=[],this.child_customer_address={name:null,email:null,address:null,phone:null,country:null,state:null,city:null,zip_code:null,full_address:null},this.child_customer_order_numbers=0,this.checkDataBeforeCreateOrder()},handleRemoveVariant:function(e,t,o){e.preventDefault(),this.child_product_ids=this.child_product_ids.filter((function(e,t){return t!==o})),this.child_products=this.child_products.filter((function(e,t){return t!==o})),this.checkDataBeforeCreateOrder()},createOrder:function(e){e.preventDefault(),$(e.target).addClass("btn-loading"),axios.post(route("orders.create"),this.getOrderFormData()).then((function(e){var t=e.data.data;e.data.error?Shaqi.showError(e.data.message):(Shaqi.showSuccess(e.data.message),$event.emit("ec-modal:close","create-order"),setTimeout((function(){window.location.href=route("orders.edit",t.id)}),1e3))})).catch((function(e){Shaqi.handleError(e.response.data)})).then((function(){$(e.target).removeClass("btn-loading")}))},createProduct:function(e,t){e.preventDefault(),$(e.target).addClass("btn-loading");var o=this;o.store&&o.store.id&&(t.store_id=o.store.id),axios.post(route("products.create-product-when-creating-order"),t).then((function(e){if(e.data.error)Shaqi.showError(e.data.message);else{o.product=e.data.data,o.list_products={data:[]};var t=o.product;t.select_qty=1,o.child_products.push(t),o.child_product_ids.push(o.product.id),o.hidden_product_search_panel=!0,Shaqi.showSuccess(e.data.message),$event.emit("ec-modal:close","add-product-item"),o.checkDataBeforeCreateOrder()}})).catch((function(e){Shaqi.handleError(e.response.data)})).then((function(){$(e.target).removeClass("btn-loading")}))},updateCustomerEmail:function(e){e.preventDefault(),$(e.target).addClass("btn-loading");axios.post(route("customers.update-email",this.child_customer.id),{email:this.child_customer.email}).then((function(e){var t=e.data;t.error?Shaqi.showError(t.message):(Shaqi.showSuccess(t.message),$event.emit("ec-modal:close","edit-email"))})).catch((function(e){var t=e.response;Shaqi.handleError(t.data)})).then((function(){$(e.target).removeClass("btn-loading")}))},updateOrderAddress:function(e){e.preventDefault(),this.customer&&($(e.target).addClass("btn-loading"),this.checkDataBeforeCreateOrder({},(function(){setTimeout((function(){$(e.target).removeClass("btn-loading"),$event.emit("ec-modal:close","edit-address")}),500)}),(function(){setTimeout((function(){$(e.target).removeClass("btn-loading")}),500)})))},createNewCustomer:function(e){e.preventDefault();var t=this;$(e.target).addClass("btn-loading"),axios.post(route("customers.create-customer-when-creating-order"),{customer_id:t.child_customer_id,name:t.child_customer_address.name,email:t.child_customer_address.email,phone:t.child_customer_address.phone,address:t.child_customer_address.address,country:t.child_customer_address.country?t.child_customer_address.country.toString():"",state:t.child_customer_address.state?t.child_customer_address.state.toString():"",city:t.child_customer_address.city?t.child_customer_address.city.toString():"",zip_code:t.child_customer_address.zip_code}).then((function(e){e.data.error?Shaqi.showError(e.data.message):(t.child_customer_address=e.data.data.address,t.child_customer=e.data.data.customer,t.child_customer_id=t.child_customer.id,t.customers={data:[]},Shaqi.showSuccess(e.data.message),t.checkDataBeforeCreateOrder(),$event.emit("ec-modal:close","add-customer"))})).catch((function(e){Shaqi.handleError(e.response.data)})).then((function(){$(e.target).removeClass("btn-loading")}))},selectCustomerAddress:function(e){var t=this;_.each(this.child_customer_addresses,(function(o){parseInt(o.id)===parseInt(e.target.value)&&(t.child_customer_address=o)})),this.checkDataBeforeCreateOrder()},getOrderNumbers:function(){var e=this;axios.get(route("customers.get-customer-order-numbers",e.child_customer_id)).then((function(t){e.child_customer_order_numbers=t.data.data})).catch((function(e){Shaqi.handleError(e.response.data)}))},loadCustomerAddress:function(){var e=this,t=this;axios.get(route("customers.get-customer-addresses",t.child_customer_id)).then((function(o){t.child_customer_addresses=o.data.data,_.isEmpty(t.child_customer_addresses)||(t.child_customer_address=_.first(t.child_customer_addresses)),e.checkDataBeforeCreateOrder()})).catch((function(e){Shaqi.handleError(e.response.data)}))},selectShippingMethod:function(e){e.preventDefault();var t=this,o=$(e.target),n=o.closest(".modal");if(o.addClass("btn-loading"),t.child_is_selected_shipping=!0,"free-shipping"===t.shipping_type)t.child_shipping_method_name=t.__("order.free_shipping"),t.child_shipping_amount=0;else{var r=n.find(".form-select").val();if(!_.isEmpty(r)){var a=n.find(".form-select option:selected");t.child_shipping_method=a.data("shipping-method"),t.child_shipping_option=a.data("shipping-option")}}this.checkDataBeforeCreateOrder({},(function(){setTimeout((function(){o.removeClass("btn-loading"),$event.emit("ec-modal:close","add-shipping")}),500)}),(function(){setTimeout((function(){o.removeClass("btn-loading")}),500)}))},changeDiscountType:function(e){"amount"===$(e.target).val()?this.discount_type_unit=this.currency:this.discount_type_unit="%",this.discount_type=$(e.target).val()},handleAddDiscount:function(e){e.preventDefault();var t=$(e.target),o=this;o.has_applied_discount=!0,o.has_invalid_coupon=!1;var n=t.find(".btn-primary");n.addClass("btn-loading").prop("disabled",!0),o.child_coupon_code?o.discount_custom_value=0:(o.discount_custom_value=Math.max(parseFloat(o.discount_custom_value),0),"percentage"===o.discount_type&&(o.discount_custom_value=Math.min(o.discount_custom_value,100))),o.checkDataBeforeCreateOrder({},(function(){setTimeout((function(){o.child_coupon_code||o.discount_custom_value||(o.has_applied_discount=!1),n.removeClass("btn-loading").prop("disabled",!1),$event.emit("ec-modal:close","add-discounts")}),500)}),(function(){o.child_coupon_code&&(o.has_invalid_coupon=!0),n.removeClass("btn-loading").prop("disabled",!1)}))},handleChangeQuantity:function(e,t,o){e.preventDefault();var n=this;t.select_qty=parseInt(e.target.value),_.each(n.child_products,(function(e,r){o===r&&(t.with_storehouse_management&&parseInt(t.select_qty)>t.quantity&&(t.select_qty=t.quantity),n.child_products[r]=t)})),n.timeoutChangeQuantity&&clearTimeout(n.timeoutChangeQuantity),n.timeoutChangeQuantity=setTimeout((function(){n.checkDataBeforeCreateOrder()}),1500)}},watch:{child_payment_method:function(){this.checkDataBeforeCreateOrder()}}},Mn=(0,Wt.A)(Cn,[["render",function(e,t,_,$,Ft,Lt){var It=(0,o.resolveComponent)("ProductAction"),Rt=(0,o.resolveComponent)("AddProductModal"),Ht=(0,o.resolveComponent)("ec-modal"),Qt=(0,o.resolveComponent)("OrderCustomerAddress"),Kt=(0,o.resolveDirective)("ec-modal");return(0,o.openBlock)(),(0,o.createElementBlock)("div",n,[(0,o.createElementVNode)("div",r,[(0,o.createElementVNode)("div",a,[(0,o.createElementVNode)("div",l,[(0,o.createElementVNode)("h4",i,(0,o.toDisplayString)(e.__("order.order_information")),1)]),(0,o.createElementVNode)("div",c,[(0,o.createElementVNode)("div",s,[e.child_products.length?((0,o.openBlock)(),(0,o.createElementBlock)("div",{key:0,class:(0,o.normalizeClass)(["table-responsive",{"loading-skeleton":e.checking}])},[(0,o.createElementVNode)("table",d,[(0,o.createElementVNode)("thead",null,[(0,o.createElementVNode)("tr",null,[t[24]||(t[24]=(0,o.createElementVNode)("th",null,null,-1)),(0,o.createElementVNode)("th",null,(0,o.toDisplayString)(e.__("order.product_name")),1),(0,o.createElementVNode)("th",null,(0,o.toDisplayString)(e.__("order.price")),1),(0,o.createElementVNode)("th",u,(0,o.toDisplayString)(e.__("order.quantity")),1),(0,o.createElementVNode)("th",null,(0,o.toDisplayString)(e.__("order.total")),1),(0,o.createElementVNode)("th",null,(0,o.toDisplayString)(e.__("order.action")),1)])]),(0,o.createElementVNode)("tbody",null,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.child_products,(function(n,r){return(0,o.openBlock)(),(0,o.createElementBlock)("tr",{key:"".concat(n.id,"-").concat(r)},[(0,o.createElementVNode)("td",null,[(0,o.createElementVNode)("img",{src:n.image_url,alt:n.name,width:"50"},null,8,m)]),(0,o.createElementVNode)("td",null,[(0,o.createElementVNode)("a",{href:n.product_link,target:"_blank"},(0,o.toDisplayString)(n.name),9,p),n.variation_attributes?((0,o.openBlock)(),(0,o.createElementBlock)("p",h,[(0,o.createElementVNode)("small",null,(0,o.toDisplayString)(n.variation_attributes),1)])):(0,o.createCommentVNode)("",!0),n.option_values&&Object.keys(n.option_values).length?((0,o.openBlock)(),(0,o.createElementBlock)("ul",g,[(0,o.createElementVNode)("li",null,[(0,o.createElementVNode)("span",null,(0,o.toDisplayString)(e.__("order.price"))+": ",1),(0,o.createElementVNode)("span",null,(0,o.toDisplayString)(n.original_price_label),1)]),((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(n.option_values,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("li",{key:e.id},[(0,o.createElementVNode)("span",null,(0,o.toDisplayString)(e.title)+": ",1),((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.values,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("span",{key:e.id},[(0,o.createTextVNode)((0,o.toDisplayString)(e.value)+" ",1),(0,o.createElementVNode)("strong",null,"+"+(0,o.toDisplayString)(e.price_label),1)])})),128))])})),128))])):(0,o.createCommentVNode)("",!0)]),(0,o.createElementVNode)("td",null,[(0,o.createElementVNode)("span",null,(0,o.toDisplayString)(n.price_label),1)]),(0,o.createElementVNode)("td",v,[(0,o.createElementVNode)("input",{class:"form-control form-control-sm",value:n.select_qty,type:"number",min:"1",onInput:function(e){return Lt.handleChangeQuantity(e,n,r)}},null,40,f)]),(0,o.createElementVNode)("td",null,(0,o.toDisplayString)(n.total_price_label),1),(0,o.createElementVNode)("td",k,[(0,o.createElementVNode)("a",{href:"javascript:void(0)",onClick:function(e){return Lt.handleRemoveVariant(e,n,r)},class:"text-decoration-none"},t[25]||(t[25]=[(0,o.createStaticVNode)('<span class="icon-tabler-wrapper icon-sm icon-left"><svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-x" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M18 6l-12 12"></path><path d="M6 6l12 12"></path></svg></span>',1)]),8,y)])])})),128))])])],2)):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("div",E,[(0,o.createElementVNode)("input",{type:"text",class:"form-control textbox-advancesearch product",placeholder:e.__("order.search_or_create_new_product"),onClick:t[0]||(t[0]=function(e){return Lt.loadListProductsAndVariations()}),onKeyup:t[1]||(t[1]=function(e){return Lt.handleSearchProduct(e.target.value)})},null,40,b),(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["card position-absolute z-1 w-100",{active:e.list_products,hidden:e.hidden_product_search_panel}]),style:(0,o.normalizeStyle)([e.loading?{minHeight:"10rem"}:{}])},[e.loading?((0,o.openBlock)(),(0,o.createElementBlock)("div",V)):((0,o.openBlock)(),(0,o.createElementBlock)("div",N,[(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("a",w,[t[26]||(t[26]=(0,o.createElementVNode)("img",{width:"28",src:"/vendor/core/plugins/ecommerce/images/next-create-custom-line-item.svg",alt:"icon",class:"me-2"},null,-1)),(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.__("order.create_a_new_product")),1)])),[[Kt,void 0,void 0,{"add-product-item":!0}]]),((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.list_products.data,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("a",{class:(0,o.normalizeClass)({"list-group-item list-group-item-action":!0,"item-selectable":!e.variations.length,"item-not-selectable":e.variations.length}),key:e.id},[(0,o.createElementVNode)("div",B,[(0,o.createElementVNode)("div",S,[(0,o.createElementVNode)("span",{class:"avatar",style:(0,o.normalizeStyle)({backgroundImage:"url("+e.image_url+")"})},null,4)]),(0,o.createElementVNode)("div",D,[(0,o.createVNode)(It,{ref_for:!0,ref:"product_actions_"+e.id,product:e,onSelectProduct:Lt.selectProductVariant},null,8,["product","onSelectProduct"]),e.variations.length?((0,o.openBlock)(),(0,o.createElementBlock)("div",x,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.variations,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{class:"list-group-item p-2",key:e.id},[(0,o.createVNode)(It,{product:e,onSelectProduct:Lt.selectProductVariant},null,8,["product","onSelectProduct"])])})),128))])):(0,o.createCommentVNode)("",!0)])])],2)})),128)),e.list_products.data&&0===e.list_products.data.length?((0,o.openBlock)(),(0,o.createElementBlock)("div",C,[(0,o.createElementVNode)("p",M,(0,o.toDisplayString)(e.__("order.no_products_found")),1)])):(0,o.createCommentVNode)("",!0)])),(e.list_products.links&&e.list_products.links.next||e.list_products.links&&e.list_products.links.prev)&&!e.loading?((0,o.openBlock)(),(0,o.createElementBlock)("div",O,[(0,o.createElementVNode)("ul",q,[(0,o.createElementVNode)("li",{class:(0,o.normalizeClass)({"page-item":!0,disabled:1===e.list_products.meta.current_page})},[1===e.list_products.meta.current_page?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,class:"page-link","aria-disabled":1===e.list_products.meta.current_page},t[27]||(t[27]=[(0,o.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"icon"},[(0,o.createElementVNode)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,o.createElementVNode)("path",{d:"M15 6l-6 6l6 6"})],-1)]),8,z)):((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:1,href:"javascript:void(0)",class:"page-link",onClick:t[2]||(t[2]=function(t){return Lt.loadListProductsAndVariations(e.list_products.links.prev?e.list_products.meta.current_page-1:e.list_products.meta.current_page,!0)})},t[28]||(t[28]=[(0,o.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"icon"},[(0,o.createElementVNode)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,o.createElementVNode)("path",{d:"M15 6l-6 6l6 6"})],-1)])))],2),(0,o.createElementVNode)("li",{class:(0,o.normalizeClass)({"page-item":!0,disabled:!e.list_products.links.next})},[e.list_products.links.next?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:1,href:"javascript:void(0)",class:"page-link",onClick:t[3]||(t[3]=function(t){return Lt.loadListProductsAndVariations(e.list_products.links.next?e.list_products.meta.current_page+1:e.list_products.meta.current_page,!0)})},t[30]||(t[30]=[(0,o.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"icon"},[(0,o.createElementVNode)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,o.createElementVNode)("path",{d:"M9 6l6 6l-6 6"})],-1)]))):((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,class:"page-link","aria-disabled":!e.list_products.links.next},t[29]||(t[29]=[(0,o.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"icon"},[(0,o.createElementVNode)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,o.createElementVNode)("path",{d:"M9 6l6 6l-6 6"})],-1)]),8,j))],2)])])):(0,o.createCommentVNode)("",!0)],6)])]),(0,o.createElementVNode)("div",T,[(0,o.createElementVNode)("div",P,[(0,o.createElementVNode)("div",A,[(0,o.createElementVNode)("label",U,(0,o.toDisplayString)(e.__("order.note")),1),(0,o.withDirectives)((0,o.createElementVNode)("textarea",{"onUpdate:modelValue":t[4]||(t[4]=function(t){return e.note=t}),class:"form-control textarea-auto-height",id:"txt-note",rows:"2",placeholder:e.__("order.note_for_order")},null,8,F),[[o.vModelText,e.note]])])]),(0,o.createElementVNode)("div",L,[(0,o.createElementVNode)("table",I,[t[33]||(t[33]=(0,o.createElementVNode)("thead",null,[(0,o.createElementVNode)("tr",null,[(0,o.createElementVNode)("td"),(0,o.createElementVNode)("td",{width:"120"})])],-1)),(0,o.createElementVNode)("tbody",null,[(0,o.createElementVNode)("tr",null,[(0,o.createElementVNode)("td",null,(0,o.toDisplayString)(e.__("order.sub_amount")),1),(0,o.createElementVNode)("td",null,[e.checking?((0,o.openBlock)(),(0,o.createElementBlock)("span",R)):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("span",H,(0,o.toDisplayString)(e.child_sub_amount_label),1)])]),(0,o.createElementVNode)("tr",null,[(0,o.createElementVNode)("td",null,(0,o.toDisplayString)(e.__("order.tax_amount")),1),(0,o.createElementVNode)("td",null,[e.checking?((0,o.openBlock)(),(0,o.createElementBlock)("span",Q)):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("span",K,(0,o.toDisplayString)(e.child_tax_amount_label),1)])]),(0,o.createElementVNode)("tr",null,[(0,o.createElementVNode)("td",null,(0,o.toDisplayString)(e.__("order.promotion_discount_amount")),1),(0,o.createElementVNode)("td",null,[(0,o.withDirectives)((0,o.createElementVNode)("span",G,null,512),[[o.vShow,e.checking]]),(0,o.createElementVNode)("span",{class:(0,o.normalizeClass)({"fw-bold":!0,"text-success":e.child_promotion_amount})},(0,o.toDisplayString)(e.child_promotion_amount_label),3)])]),(0,o.createElementVNode)("tr",null,[(0,o.createElementVNode)("td",null,[(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("button",J,[e.has_applied_discount?((0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,{key:1},[(0,o.createTextVNode)((0,o.toDisplayString)(e.__("order.discount")),1)],64)):((0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,{key:0},[t[31]||(t[31]=(0,o.createElementVNode)("i",{class:"icon-sm ti ti-plus"},null,-1)),(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.__("order.add_discount")),1)],64))])),[[Kt,void 0,void 0,{"add-discounts":!0}]]),e.has_applied_discount?((0,o.openBlock)(),(0,o.createElementBlock)("span",W,(0,o.toDisplayString)(e.child_coupon_code||e.child_discount_description),1)):(0,o.createCommentVNode)("",!0)]),(0,o.createElementVNode)("td",null,[(0,o.withDirectives)((0,o.createElementVNode)("span",X,null,512),[[o.vShow,e.checking]]),(0,o.createElementVNode)("span",{class:(0,o.normalizeClass)({"text-success fw-bold":e.child_discount_amount})},(0,o.toDisplayString)(e.child_discount_amount_label),3)])]),e.is_available_shipping?((0,o.openBlock)(),(0,o.createElementBlock)("tr",Y,[(0,o.createElementVNode)("td",null,[(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("button",Z,[e.child_is_selected_shipping?((0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,{key:1},[(0,o.createTextVNode)((0,o.toDisplayString)(e.__("order.shipping")),1)],64)):((0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,{key:0},[t[32]||(t[32]=(0,o.createElementVNode)("i",{class:"icon-sm ti ti-plus"},null,-1)),(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.__("order.add_shipping_fee")),1)],64))])),[[Kt,void 0,void 0,{"add-shipping":!0}]]),e.child_shipping_method_name?((0,o.openBlock)(),(0,o.createElementBlock)("span",ee,(0,o.toDisplayString)(e.child_shipping_method_name),1)):(0,o.createCommentVNode)("",!0)]),(0,o.createElementVNode)("td",null,[(0,o.withDirectives)((0,o.createElementVNode)("span",te,null,512),[[o.vShow,e.checking]]),(0,o.createElementVNode)("span",{class:(0,o.normalizeClass)({"fw-bold":e.child_shipping_amount})},(0,o.toDisplayString)(e.child_shipping_amount_label),3)])])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("tr",null,[(0,o.createElementVNode)("td",null,(0,o.toDisplayString)(e.__("order.total_amount")),1),(0,o.createElementVNode)("td",null,[(0,o.withDirectives)((0,o.createElementVNode)("span",oe,null,512),[[o.vShow,e.checking]]),(0,o.createElementVNode)("h4",ne,(0,o.toDisplayString)(e.child_total_amount_label),1)])]),(0,o.createElementVNode)("tr",null,[(0,o.createElementVNode)("td",re,[(0,o.createElementVNode)("label",ae,(0,o.toDisplayString)(e.__("order.payment_method")),1),(0,o.withDirectives)((0,o.createElementVNode)("select",{class:"form-select",id:"payment-method","onUpdate:modelValue":t[5]||(t[5]=function(t){return e.child_payment_method=t})},[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(_.paymentMethods,(function(e,t){return(0,o.openBlock)(),(0,o.createElementBlock)("option",{key:t,value:t},(0,o.toDisplayString)(e),9,le)})),128))],512),[[o.vModelSelect,e.child_payment_method]])])]),(0,o.createElementVNode)("tr",null,[(0,o.createElementVNode)("td",ie,[(0,o.createElementVNode)("label",ce,(0,o.toDisplayString)(e.__("order.payment_status_label")),1),(0,o.withDirectives)((0,o.createElementVNode)("select",{class:"form-select",id:"payment-status","onUpdate:modelValue":t[6]||(t[6]=function(t){return e.child_payment_status=t})},[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(_.paymentStatuses,(function(e,t){return(0,o.openBlock)(),(0,o.createElementBlock)("option",{key:t,value:t},(0,o.toDisplayString)(e),9,se)})),128))],512),[[o.vModelSelect,e.child_payment_status]])])]),(0,o.createElementVNode)("tr",null,[(0,o.createElementVNode)("td",de,[(0,o.createElementVNode)("label",ue,(0,o.toDisplayString)(e.__("order.transaction_id")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"text",class:"form-control","onUpdate:modelValue":t[7]||(t[7]=function(t){return e.child_transaction_id=t})},null,512),[[o.vModelText,e.child_transaction_id]]),(0,o.createElementVNode)("small",me,(0,o.toDisplayString)(e.__("order.incomplete_order_transaction_id_placeholder")),1)])])])])])])]),(0,o.createElementVNode)("div",pe,[(0,o.createElementVNode)("div",_e,[(0,o.createElementVNode)("p",he,[t[34]||(t[34]=(0,o.createStaticVNode)('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M3 5m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z"></path><path d="M3 10l18 0"></path><path d="M7 15l.01 0"></path><path d="M11 15l2 0"></path></svg>',1)),(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.__("order.confirm_payment_and_create_order")),1)]),(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("button",{disabled:!e.child_product_ids.length||!e.child_customer_id,type:"submit",class:"btn btn-primary"},[(0,o.createTextVNode)((0,o.toDisplayString)(e.__("order.create_order")),1)],8,ge)),[[Kt,void 0,void 0,{"create-order":!0}]])])])])]),(0,o.createElementVNode)("div",ve,[(0,o.createElementVNode)("div",fe,[e.child_customer_id&&e.child_customer?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createElementBlock)("div",ke,[(0,o.createElementVNode)("div",ye,[(0,o.createElementVNode)("h4",Ee,(0,o.toDisplayString)(e.__("order.customer_information")),1)]),(0,o.createElementVNode)("div",be,[(0,o.createElementVNode)("div",Ve,[(0,o.createElementVNode)("input",{type:"text",class:"form-control textbox-advancesearch customer",onClick:t[8]||(t[8]=function(e){return Lt.loadListCustomersForSearch()}),onKeyup:t[9]||(t[9]=function(e){return Lt.handleSearchCustomer(e.target.value)}),placeholder:e.__("order.search_or_create_new_customer")},null,40,Ne),(0,o.createElementVNode)("div",{class:(0,o.normalizeClass)(["card position-absolute w-100 z-1",{active:e.customers,hidden:e.hidden_customer_search_panel}]),style:(0,o.normalizeStyle)([e.loading?{minHeight:"10rem"}:{}])},[e.loading?((0,o.openBlock)(),(0,o.createElementBlock)("div",we)):((0,o.openBlock)(),(0,o.createElementBlock)("div",Be,[(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("div",Se,[(0,o.createElementVNode)("div",De,[t[35]||(t[35]=(0,o.createElementVNode)("div",{class:"col-auto"},[(0,o.createElementVNode)("img",{width:"28",src:"/vendor/core/plugins/ecommerce/images/next-create-customer.svg",alt:"icon"})],-1)),(0,o.createElementVNode)("div",xe,[(0,o.createElementVNode)("span",null,(0,o.toDisplayString)(e.__("order.create_new_customer")),1)])])])),[[Kt,void 0,void 0,{"add-customer":!0}]]),((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.customers.data,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("a",{class:"list-group-item list-group-item-action",href:"javascript:void(0)",key:e.id,onClick:function(t){return Lt.selectCustomer(e)}},[(0,o.createElementVNode)("div",Me,[(0,o.createElementVNode)("div",Oe,[(0,o.createElementVNode)("div",qe,[(0,o.createElementVNode)("span",{class:"avatar",style:(0,o.normalizeStyle)({backgroundImage:"url("+e.avatar_url+")"})},null,4)]),(0,o.createElementVNode)("div",ze,[(0,o.createElementVNode)("div",je,(0,o.toDisplayString)(e.name),1),e.email?((0,o.openBlock)(),(0,o.createElementBlock)("div",Te,(0,o.toDisplayString)(e.email),1)):(0,o.createCommentVNode)("",!0)])])])],8,Ce)})),128)),e.customers.data&&0===e.customers.data.length?((0,o.openBlock)(),(0,o.createElementBlock)("div",Pe,(0,o.toDisplayString)(e.__("order.no_customer_found")),1)):(0,o.createCommentVNode)("",!0)])),!e.customers.next_page_url&&!e.customers.prev_page_url||e.loading?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createElementBlock)("div",$e,[(0,o.createElementVNode)("ul",Ae,[(0,o.createElementVNode)("li",{class:(0,o.normalizeClass)({"page-item":!0,disabled:1===e.customers.current_page})},[1===e.customers.current_page?((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,class:"page-link","aria-disabled":1===e.customers.current_page},t[36]||(t[36]=[(0,o.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"icon"},[(0,o.createElementVNode)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,o.createElementVNode)("path",{d:"M15 6l-6 6l6 6"})],-1)]),8,Ue)):((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:1,href:"javascript:void(0)",class:"page-link",onClick:t[10]||(t[10]=function(t){return Lt.loadListCustomersForSearch(e.customers.prev_page_url?e.customers.current_page-1:e.customers.current_page,!0)})},t[37]||(t[37]=[(0,o.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"icon"},[(0,o.createElementVNode)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,o.createElementVNode)("path",{d:"M15 6l-6 6l6 6"})],-1)])))],2),(0,o.createElementVNode)("li",{class:(0,o.normalizeClass)({"page-item":!0,disabled:!e.customers.next_page_url})},[e.customers.next_page_url?((0,o.openBlock)(),(0,o.createElementBlock)("a",{key:1,href:"javascript:void(0)",class:"page-link",onClick:t[11]||(t[11]=function(t){return Lt.loadListCustomersForSearch(e.customers.next_page_url?e.customers.current_page+1:e.customers.current_page,!0)})},t[39]||(t[39]=[(0,o.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"icon"},[(0,o.createElementVNode)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,o.createElementVNode)("path",{d:"M9 6l6 6l-6 6"})],-1)]))):((0,o.openBlock)(),(0,o.createElementBlock)("span",{key:0,class:"page-link","aria-disabled":!e.customers.next_page_url},t[38]||(t[38]=[(0,o.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"icon"},[(0,o.createElementVNode)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,o.createElementVNode)("path",{d:"M9 6l6 6l-6 6"})],-1)]),8,Fe))],2)])]))],6)])])])),e.child_customer_id&&e.child_customer?((0,o.openBlock)(),(0,o.createElementBlock)("div",Le,[(0,o.createElementVNode)("div",Ie,[(0,o.createElementVNode)("h4",Re,(0,o.toDisplayString)(e.__("order.customer")),1),(0,o.createElementVNode)("div",He,[(0,o.createElementVNode)("button",{type:"button","data-bs-toggle":"tooltip","data-placement":"top",title:"Delete customer",onClick:t[12]||(t[12]=function(e){return Lt.removeCustomer()}),class:"btn-action"},t[40]||(t[40]=[(0,o.createStaticVNode)('<span class="icon-tabler-wrapper icon-sm icon-left"><svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-x" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M18 6l-12 12"></path><path d="M6 6l12 12"></path></svg></span>',1)]))])]),(0,o.createElementVNode)("div",Qe,[(0,o.createElementVNode)("div",Ke,[(0,o.createElementVNode)("div",Ge,[(0,o.createElementVNode)("span",{class:"avatar avatar-lg avatar-rounded",style:(0,o.normalizeStyle)({backgroundImage:"url(".concat(e.child_customer.avatar_url||e.child_customer.avatar,")")})},null,4)]),(0,o.createElementVNode)("div",Je,[t[41]||(t[41]=(0,o.createElementVNode)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round",class:"icon"},[(0,o.createElementVNode)("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),(0,o.createElementVNode)("path",{d:"M4 4m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z"}),(0,o.createElementVNode)("path",{d:"M4 13h3l3 3h4l3 -3h3"})],-1)),(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.child_customer_order_numbers)+" "+(0,o.toDisplayString)(e.__("order.orders")),1)]),(0,o.createElementVNode)("div",We,(0,o.toDisplayString)(e.child_customer.name),1),e.child_customer.email?((0,o.openBlock)(),(0,o.createElementBlock)("div",Xe,[(0,o.createElementVNode)("span",null,(0,o.toDisplayString)(e.child_customer.email),1),(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("a",{href:"javascript:void(0)","data-placement":"top","data-bs-toggle":"tooltip","data-bs-original-title":e.__("order.edit_email"),class:"btn-action text-decoration-none"},t[42]||(t[42]=[(0,o.createStaticVNode)('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"></path><path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"></path><path d="M16 5l3 3"></path></svg>',1)]),8,Ye)),[[Kt,void 0,void 0,{"edit-email":!0}]])])):(0,o.createCommentVNode)("",!0)]),e.is_available_shipping?((0,o.openBlock)(),(0,o.createElementBlock)(o.Fragment,{key:0},[t[44]||(t[44]=(0,o.createElementVNode)("div",{class:"hr my-1"},null,-1)),(0,o.createElementVNode)("div",Ze,[(0,o.createElementVNode)("div",et,[(0,o.createElementVNode)("h4",tt,(0,o.toDisplayString)(e.__("order.shipping_address")),1),(0,o.withDirectives)(((0,o.openBlock)(),(0,o.createElementBlock)("button",ot,t[43]||(t[43]=[(0,o.createStaticVNode)('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"></path><path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"></path><path d="M16 5l3 3"></path></svg>',1)]))),[[Kt,void 0,void 0,{"edit-address":!0}]])]),e.child_customer_addresses.length>1?((0,o.openBlock)(),(0,o.createElementBlock)("div",nt,[(0,o.createElementVNode)("select",{class:"form-select",onChange:t[13]||(t[13]=function(e){return Lt.selectCustomerAddress(e)})},[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.child_customer_addresses,(function(e){return(0,o.openBlock)(),(0,o.createElementBlock)("option",{value:e.id,selected:e.id===_.customer_address.id,key:e.id},(0,o.toDisplayString)(e.full_address),9,rt)})),128))],32)])):(0,o.createCommentVNode)("",!0),(0,o.createElementVNode)("dl",at,[(0,o.createElementVNode)("dd",null,(0,o.toDisplayString)(e.child_customer_address.name),1),(0,o.createElementVNode)("dd",null,(0,o.toDisplayString)(e.child_customer_address.phone),1),(0,o.createElementVNode)("dd",null,(0,o.toDisplayString)(e.child_customer_address.email),1),(0,o.createElementVNode)("dd",null,(0,o.toDisplayString)(e.child_customer_address.address),1),(0,o.createElementVNode)("dd",null,(0,o.toDisplayString)(e.child_customer_address.city_name),1),(0,o.createElementVNode)("dd",null,(0,o.toDisplayString)(e.child_customer_address.state_name),1),(0,o.createElementVNode)("dd",null,(0,o.toDisplayString)(e.child_customer_address.country_name),1),_.zip_code_enabled?((0,o.openBlock)(),(0,o.createElementBlock)("dd",lt,(0,o.toDisplayString)(e.child_customer_address.zip_code),1)):(0,o.createCommentVNode)("",!0),e.child_customer_address.full_address?((0,o.openBlock)(),(0,o.createElementBlock)("dd",it,[(0,o.createElementVNode)("a",{target:"_blank",class:"hover-underline",href:"https://maps.google.com/?q="+e.child_customer_address.full_address},(0,o.toDisplayString)(e.__("order.see_on_maps")),9,ct)])):(0,o.createCommentVNode)("",!0)])])],64)):(0,o.createCommentVNode)("",!0)])])):(0,o.createCommentVNode)("",!0)])]),(0,o.createVNode)(Rt,{onCreateProduct:Lt.createProduct,store:e.store},null,8,["onCreateProduct","store"]),(0,o.createVNode)(Ht,{id:"add-discounts",title:e.__("order.add_discount"),"ok-title":e.__("order.add_discount"),"cancel-title":e.__("order.close"),onOk:t[19]||(t[19]=function(e){return Lt.handleAddDiscount(e)})},{default:(0,o.withCtx)((function(){return[(0,o.createElementVNode)("div",st,[(0,o.createElementVNode)("div",dt,[(0,o.createElementVNode)("div",ut,[(0,o.createElementVNode)("label",mt,(0,o.toDisplayString)(e.__("order.discount_based_on")),1),(0,o.createElementVNode)("div",pt,[(0,o.createElementVNode)("div",_t,[(0,o.createElementVNode)("button",{value:"amount",class:(0,o.normalizeClass)(["btn btn-active",{active:"amount"===e.discount_type}]),onClick:t[14]||(t[14]=function(e){return Lt.changeDiscountType(e)})},(0,o.toDisplayString)(_.currency||"$"),3),t[45]||(t[45]=(0,o.createTextVNode)("  ")),(0,o.createElementVNode)("button",{value:"percentage",class:(0,o.normalizeClass)(["btn btn-active",{active:"percentage"===e.discount_type}]),onClick:t[15]||(t[15]=function(e){return Lt.changeDiscountType(e)})}," % ",2)]),(0,o.createElementVNode)("div",ht,[(0,o.createElementVNode)("div",gt,[(0,o.withDirectives)((0,o.createElementVNode)("input",{class:"form-control","onUpdate:modelValue":t[16]||(t[16]=function(t){return e.discount_custom_value=t})},null,512),[[o.vModelText,e.discount_custom_value]]),(0,o.createElementVNode)("span",vt,(0,o.toDisplayString)(e.discount_type_unit),1)])])])])]),(0,o.createElementVNode)("div",ft,[(0,o.createElementVNode)("div",kt,[(0,o.createElementVNode)("label",yt,(0,o.toDisplayString)(e.__("order.or_coupon_code")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{class:"form-control coupon-code-input","onUpdate:modelValue":t[17]||(t[17]=function(t){return e.child_coupon_code=t})},null,512),[[o.vModelText,e.child_coupon_code]])]),(0,o.createElementVNode)("div",Et,[(0,o.createElementVNode)("label",bt,(0,o.toDisplayString)(e.__("order.description")),1),(0,o.withDirectives)((0,o.createElementVNode)("input",{placeholder:e.__("order.discount_description"),class:"form-control","onUpdate:modelValue":t[18]||(t[18]=function(t){return e.child_discount_description=t})},null,8,Vt),[[o.vModelText,e.child_discount_description]])])])])]})),_:1},8,["title","ok-title","cancel-title"]),(0,o.createVNode)(Ht,{id:"add-shipping",title:e.__("order.shipping_fee"),"ok-title":e.__("order.update"),"cancel-title":e.__("order.close"),onOk:t[22]||(t[22]=function(e){return Lt.selectShippingMethod(e)})},{default:(0,o.withCtx)((function(){return[e.child_products.length&&e.child_customer_address.phone?(0,o.createCommentVNode)("",!0):((0,o.openBlock)(),(0,o.createElementBlock)("div",Nt,[(0,o.createElementVNode)("div",wt,[(0,o.createElementVNode)("div",Bt,[t[46]||(t[46]=(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("i",{class:"icon alert-icon ti ti-alert-circle"})],-1)),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("h4",St,(0,o.toDisplayString)(e.__("order.how_to_select_configured_shipping")),1),(0,o.createElementVNode)("div",Dt,(0,o.toDisplayString)(e.__("order.please_products_and_customer_address_to_see_the_shipping_rates"))+". ",1)])])])])),(0,o.createElementVNode)("div",xt,[(0,o.createElementVNode)("label",Ct,[(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"radio",class:"form-check-input",value:"free-shipping",name:"shipping_type","onUpdate:modelValue":t[20]||(t[20]=function(t){return e.shipping_type=t})},null,512),[[o.vModelRadio,e.shipping_type]]),(0,o.createTextVNode)(" "+(0,o.toDisplayString)(e.__("order.free_shipping")),1)])]),e.child_products.length&&e.child_customer_address.phone?((0,o.openBlock)(),(0,o.createElementBlock)("div",Mt,[(0,o.createElementVNode)("div",Ot,[(0,o.createElementVNode)("label",qt,[(0,o.withDirectives)((0,o.createElementVNode)("input",{type:"radio",class:"form-check-input",value:"custom",name:"shipping_type","onUpdate:modelValue":t[21]||(t[21]=function(t){return e.shipping_type=t}),disabled:e.shipping_methods&&!Object.keys(e.shipping_methods).length},null,8,zt),[[o.vModelRadio,e.shipping_type]]),(0,o.createElementVNode)("span",jt,(0,o.toDisplayString)(e.__("order.custom")),1),e.shipping_methods&&!Object.keys(e.shipping_methods).length?((0,o.openBlock)(),(0,o.createElementBlock)("small",Tt,(0,o.toDisplayString)(e.__("order.shipping_method_not_found")),1)):(0,o.createCommentVNode)("",!0)])]),(0,o.withDirectives)((0,o.createElementVNode)("select",Pt,[((0,o.openBlock)(!0),(0,o.createElementBlock)(o.Fragment,null,(0,o.renderList)(e.shipping_methods,(function(t,n){return(0,o.openBlock)(),(0,o.createElementBlock)("option",{value:n,selected:n==="".concat(e.child_shipping_method,";").concat(e.child_shipping_option),key:n,"data-shipping-method":t.method,"data-shipping-option":t.option},(0,o.toDisplayString)(t.title),9,$t)})),128))],512),[[o.vShow,"custom"===e.shipping_type]])])):(0,o.createCommentVNode)("",!0)]})),_:1},8,["title","ok-title","cancel-title"]),(0,o.createVNode)(Ht,{id:"create-order",title:e.__("order.confirm_payment_title").replace(":status",_.paymentStatuses[e.child_payment_status]),"ok-title":e.__("order.create_order"),"cancel-title":e.__("order.close"),onOk:t[23]||(t[23]=function(e){return Lt.createOrder(e)})},{default:(0,o.withCtx)((function(){return[(0,o.createElementVNode)("div",At,(0,o.toDisplayString)(e.__("order.confirm_payment_description").replace(":status",_.paymentStatuses[e.child_payment_status]))+". ",1),(0,o.createElementVNode)("div",null,[(0,o.createElementVNode)("span",null,(0,o.toDisplayString)(e.__("order.order_amount"))+":",1),(0,o.createElementVNode)("h3",Ut,(0,o.toDisplayString)(e.child_total_amount_label),1)])]})),_:1},8,["title","ok-title","cancel-title"]),(0,o.createVNode)(Qt,{customer:e.child_customer,address:e.child_customer_address,zip_code_enabled:_.zip_code_enabled,use_location_data:_.use_location_data,onUpdateOrderAddress:Lt.updateOrderAddress,onUpdateCustomerEmail:Lt.updateCustomerEmail,onCreateNewCustomer:Lt.createNewCustomer},null,8,["customer","address","zip_code_enabled","use_location_data","onUpdateOrderAddress","onUpdateCustomerEmail","onCreateNewCustomer"])])}]]);var On=["id","aria-labelledby"],qn={class:"modal-dialog modal-dialog-centered",role:"document"},zn={class:"modal-content"},jn={class:"modal-header"},Tn=["id","textContent"],Pn={class:"modal-body"},$n={class:"modal-footer"},An=["textContent"],Un=["textContent"];const Fn=(0,o.defineComponent)({props:{id:{type:String,required:!0},title:String,okTitle:String,cancelTitle:String},data:function(){return{modal:null}},mounted:function(){var e=this;this.$emit("shown"),this.modal=new bootstrap.Modal(document.getElementById(this.id)),$event.on("ec-modal:open",(function(t){t===e.id&&e.modal.show()})),$event.on("ec-modal:close",(function(t){t===e.id&&e.modal.hide()}))}}),Ln=(0,Wt.A)(Fn,[["render",function(e,t,n,r,a,l){return(0,o.openBlock)(),(0,o.createElementBlock)("div",{class:"modal modal-blur fade",id:e.id,tabindex:"-1","aria-labelledby":"".concat(e.id,"Label"),"aria-hidden":"true"},[(0,o.createElementVNode)("div",qn,[(0,o.createElementVNode)("div",zn,[(0,o.createElementVNode)("header",jn,[(0,o.createElementVNode)("h5",{class:"modal-title",id:"".concat(e.id,"Label"),textContent:(0,o.toDisplayString)(e.title)},null,8,Tn),t[1]||(t[1]=(0,o.createElementVNode)("button",{type:"button",class:"btn-close","data-bs-dismiss":"modal","aria-label":"Close"},null,-1))]),(0,o.createElementVNode)("div",Pn,[(0,o.renderSlot)(e.$slots,"default")]),(0,o.createElementVNode)("div",$n,[(0,o.createElementVNode)("button",{type:"button",class:"btn","data-bs-dismiss":"modal",textContent:(0,o.toDisplayString)(e.cancelTitle)},null,8,An),(0,o.createElementVNode)("button",{type:"button",class:"btn btn-primary ms-auto",onClick:t[0]||(t[0]=function(t){return e.$emit("ok",t)}),textContent:(0,o.toDisplayString)(e.okTitle)},null,8,Un)])])])],8,On)}]]);"undefined"!=typeof vueApp&&vueApp.registerVuePlugins({install:function(e){e.config.globalProperties.$filters={formatPrice:function(e){return parseFloat(e).toFixed(2)}},e.directive("ec-modal",{mounted:function(e,t){t.modifiers&&Object.keys(t.modifiers).length>0&&e.addEventListener("click",(function(){Object.keys(t.modifiers).forEach((function(e){$event.emit("ec-modal:open",e)}))}))}}),e.component("ec-modal",Ln),e.component("create-order",Mn)}})})();
//# sourceMappingURL=order-create.js.map