<?php

namespace Database\Seeders;

use <PERSON><PERSON><PERSON>\Base\Supports\BaseSeeder;
use <PERSON><PERSON>qi\Team\Models\Team;

class TeamSeeder extends BaseSeeder
{
    public function run(): void
    {
        $this->uploadFiles('testimonials');

        Team::query()->truncate();

        $teams = [
            [
                'name' => '<PERSON><PERSON>',
                'title' => 'Founder',
                'location' => 'USA',
            ],
            [
                'name' => 'Ukolilix X. Xilanorix',
                'title' => 'CEO',
                'location' => 'Qatar',
            ],
            [
                'name' => '<PERSON>',
                'title' => 'Designer',
                'location' => 'India',
            ],
            [
                'name' => '<PERSON>',
                'title' => 'Developer',
                'location' => 'China',
            ],
        ];

        foreach ($teams as $index => $item) {
            Team::query()->create(array_merge($item, [
                'photo' => 'testimonials/team-' . ($index + 1) . '.jpg',
            ]));
        }
    }
}
