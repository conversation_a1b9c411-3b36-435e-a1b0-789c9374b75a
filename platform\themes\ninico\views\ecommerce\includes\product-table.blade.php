<div class="table-responsive mb-3">

    <table class="table product-table display responsive nowrap">
        <thead>
            <tr>
                <th class="text-left w-15"></th>
                <th class="text-left w-30">Part Name</th>
                <th class="text-left w-15">Part Number</th>
                <th class="text-left w-15">Price</th>
                <th class="text-center w-30">Buy</th>
            </tr>
        </thead>
        <tbody>
            @foreach($products as $product)
                <tr>
                    <td>
                        <img src="{{ RvMedia::getImageUrl($product->image, 'small', false, RvMedia::getDefaultImage()) }}" alt="{{ $product->name }}" style="max-width: 100px;height: 100px;object-fit: cover;">
                    </td>
                    <td>
                        <a href="{{ $product->url }}">{{ $product->name }}</a>
                    </td>
                    <td>
                        @if ($part_number = $product->getMetaData('part_number', true))
                        {{ $part_number }}
                        @endif
                    </td>
                    <td>
                        @include(EcommerceHelper::viewPath('includes.product-price'), [
                            'product' => $product,
                        ])
                    </td>
                    <td class="text-center">
                        <form
                        class="cart-form"
                        method="POST"
                        action="{{ route('public.cart.add-to-cart') }}"
                    >
                        @csrf
                        <input
                        class="hidden-product-id"
                        name="id"
                        type="hidden"
                        value="{{ $product->is_variation || !$product->defaultVariation->product_id ? $product->id : $product->defaultVariation->product_id }}"
                    />

                    <div class="tpproduct-details__count d-flex align-items-center justify-content-end flex-wrap gap-2">
                        @if (EcommerceHelper::isCartEnabled())
                            <div class="tpproduct-details__quantity">
                                <span class="cart-minus"><i class="far fa-minus"></i></span>
                                <input
                                    class="tp-cart-input"
                                    name="qty"
                                    type="text"
                                    value="1"
                                >
                                <span class="cart-plus"><i class="far fa-plus"></i></span>
                            </div>
                            <div class="d-flex gap-2 tpproduct-details__cart">
                                <button
                                    class="btn add-to-cart"
                                    name="add_to_cart"
                                    type="submit"
                                    value="1"
                                    @disabled($product->isOutOfStock())
                                >
                                    <i class="fal fa-shopping-cart"></i>
                                    {{ __('Add To Cart') }}
                                </button>
                            </div>
                        @endif
                    </div>

                        </form>

                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>


