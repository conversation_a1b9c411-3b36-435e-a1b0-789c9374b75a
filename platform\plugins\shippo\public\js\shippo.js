(()=>{"use strict";var e=e||{};e.init=function(){$(document).on("show.bs.modal","#shippo-view-n-create-transaction",(function(e){var a=$(e.currentTarget),n=$(e.relatedTarget);a.find(".modal-body").html(""),$.ajax({type:"GET",url:n.data("url"),beforeSend:function(){n.addClass("button-loading")},success:function(e){e.error?Shaqi.showError(e.message):a.find(".modal-body").html(e.data.html)},error:function(e){Shaqi.handleError(e)},complete:function(){n.removeClass("button-loading")}})})),$(document).on("click","#shippo-view-n-create-transaction .create-transaction",(function(e){var a=$(e.currentTarget);$.ajax({type:"POST",url:a.data("url"),beforeSend:function(){a.addClass("button-loading")},success:function(e){e.error?Shaqi.showError(e.message):($('[data-bs-target="#shippo-view-n-create-transaction"]').addClass("d-none"),$("#shippo-view-n-create-transaction").modal("hide"),Shaqi.showSuccess(e.message))},error:function(e){Shaqi.handleError(e)},complete:function(){a.removeClass("button-loading")}})})),$(document).on("click","#shippo-view-n-create-transaction .get-new-rates",(function(e){var a=$(e.currentTarget);$.ajax({type:"GET",url:a.data("url"),beforeSend:function(){a.addClass("button-loading")},success:function(e){e.error?Shaqi.showError(e.message):(Shaqi.showSuccess(e.message),a.addClass("d-none"),a.parent().append(e.data.html))},error:function(e){Shaqi.handleError(e)},complete:function(){a.removeClass("button-loading")}})})),$(document).on("submit",".update-rate-shipment",(function(e){e.preventDefault();var a=$(e.currentTarget),n=a.find("button[type=submit]");$.ajax({type:"POST",url:a.prop("action"),data:a.serializeArray(),beforeSend:function(){n.addClass("button-loading")},success:function(e){e.error?Shaqi.showError(e.message):(Shaqi.showSuccess(e.message),$("#shippo-view-n-create-transaction").find(".modal-body").html(e.data.html))},error:function(e){Shaqi.handleError(e)},complete:function(){n.removeClass("button-loading")}})}))},$((function(){e.init()}))})();
//# sourceMappingURL=shippo.js.map