$((function(){$(document).on("click",".btn-trigger-add-address",(function(e){e.preventDefault(),$("#add-address-modal").modal("show")})),$(document).on("click","#confirm-add-address-button",(function(e){e.preventDefault();var t=$(e.currentTarget);Shaqi.showButtonLoading(t);var o=t.closest(".modal-content").find("form"),a=o.prop("action"),r=o.serialize();$httpClient.make().post(a,r).then((function(e){var t=e.data;t.error?Shaqi.showNotice("error",t.message):(Shaqi.showNotice("success",t.message),$("#add-address-modal").modal("hide"),o.get(0).reset(),$("#address-histories").load($(".page-wrapper form.js-base-form").prop("action")+" #address-histories > *"))})).finally((function(){Shaqi.hideButtonLoading(t)}))})),$(document).on("click",".btn-trigger-edit-address",(function(e){e.preventDefault();var t=$(e.currentTarget),o=$("#edit-address-modal"),a=o.find(".modal-loading-block"),r=$("#edit-address-modal .modal-body .modal-form-content");r.html(""),a.removeClass("d-none"),o.modal("show"),Shaqi.showButtonLoading(t),$httpClient.make().get(t.data("section")).then((function(e){var t=e.data;t.error?Shaqi.showNotice("error",t.message):(a.addClass("d-none"),r.html(t))})).finally((function(){Shaqi.hideButtonLoading(t)}))})),$(document).on("click","#confirm-edit-address-button",(function(e){e.preventDefault();var t=$(e.currentTarget);Shaqi.showButtonLoading(t);var o=t.closest(".modal-content").find("form"),a=o.prop("action"),r=o.serialize();$httpClient.make().post(a,r).then((function(e){var t=e.data;t.error?Shaqi.showNotice("error",t.message):(Shaqi.showNotice("success",t.message),$("#edit-address-modal").modal("hide"),o.get(0).reset(),$("#address-histories").load($(".page-wrapper form.js-base-form").prop("action")+" #address-histories > *"))})).finally((function(){Shaqi.hideButtonLoading(t)}))})),$(document).on("click",".deleteDialog",(function(e){e.preventDefault();var t=$(e.currentTarget);$(".delete-crud-entry").data("section",t.data("section")),$(".modal-confirm-delete").modal("show")})),$(".delete-crud-entry").on("click",(function(e){e.preventDefault();var t=$(e.currentTarget);Shaqi.showButtonLoading(t);var o=t.data("section");$httpClient.make().post(o,{_method:"DELETE"}).then((function(e){var o=e.data;if(o.error)Shaqi.showError(o.message);else{Shaqi.showSuccess(o.message);var a=$(".page-wrapper form").prop("action");$("#address-histories").load(a+" #address-histories > *")}t.closest(".modal").modal("hide")})).finally((function(){Shaqi.hideButtonLoading(t)}))}))}));
//# sourceMappingURL=address.js.map