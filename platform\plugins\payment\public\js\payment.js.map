{"version": 3, "file": "/vendor/core/plugins/payment/js/payment.js", "mappings": "mBAEA,IAAIA,EAAWA,GAAY,CAAC,EAE5BA,EAASC,cAAgB,WACrB,IAAIC,EAAgBC,EAAEC,UAAUC,KAAK,sCAAsCC,QAEtEJ,EAAcK,SACfL,EAAgBC,EAAEC,UAAUC,KAAK,8BAA8BC,SACjDE,QAAQ,SAASA,QAAQ,UAGvCN,EAAcK,QACdL,EAAcO,QAAQ,oBAAoBJ,KAAK,0BAA0BK,SAAS,QAGlFP,EAAE,wBAAwBI,OAAS,GACnC,IAAII,KAAK,CAGLC,KAAM,yBAGNC,UAAW,uBAEXC,cAAe,CACXC,YAAa,sBACbC,YAAa,mBACbC,SAAU,mBACVC,UAAW,qBAGfC,MAAO,IACPC,YAAY,EAGZC,SAAU,CACNC,UAAW,cACXC,UAAW,WAIfC,aAAc,CACVC,OAAQ,sBACRC,KAAM,YACNC,OAAQ,QACRC,IAAK,OAGTC,MAAO,CACHC,WAAY,KAIhBC,OAAO,GAGnB,EAEA/B,EAASgC,KAAO,WACZhC,EAASC,gBAETE,EAAEC,UAAU6B,GAAG,SAAU,sBAAsB,SAAUC,GACrDA,EAAMC,iBAENhC,EAAE,0BAA0BiC,YAAY,YAAYA,YAAY,QAAQA,YAAY,UAEpFjC,EAAE+B,EAAMG,eACH5B,QAAQ,oBACRJ,KAAK,0BACLK,SAAS,QACTA,SAAS,SAClB,IAEAP,EAAEC,UACGkC,IAAI,QAAS,yBACbL,GAAG,QAAS,yBAAyB,SAAUC,GAC5CA,EAAMC,iBAEN,IAAMI,EAASpC,EAAE+B,EAAMG,eACjBzB,EAAO2B,EAAO9B,QAAQ,QACtB+B,EAAoBD,EAAOE,OAE7B7B,EAAK8B,QAAU9B,EAAK8B,UAIxBH,EAAOI,KAAK,YAAY,GACxBJ,EAAOE,KAAK,6EAADG,OACsEL,EAAOM,KAAK,qBAGvC,WAAlD1C,EAAE,sCAAsC2C,OAAsB3C,EAAE,wBAAwBI,OAAS,GACjGwC,OAAOC,kBAAkB7C,EAAE,uBAAuB0C,KAAK,UACvDE,OAAOE,KAAKC,YAAYtC,GAAM,SAAUuC,EAAQC,GACxCA,EAASC,OACW,oBAATC,MACPA,MAAMC,UAAUH,EAASC,MAAMG,QAASjB,EAAOM,KAAK,iBAEpDY,MAAML,EAASC,MAAMG,SAEzBjB,EAAOI,KAAK,YAAY,GACxBJ,EAAOE,KAAKD,KAEZ5B,EAAK8C,OAAOvD,EAAE,4CAA4C2C,IAAIM,EAASO,KACvE/C,EAAKgD,SAEb,KAEAhD,EAAKgD,SAEb,GACR,EAEAzD,EAAEC,UAAUyD,OAAM,WACd7D,EAASgC,OAET5B,SAAS0D,iBAAiB,yBAAyB,WAC/C9D,EAASC,eACb,GACJ,G", "sources": ["webpack:///./platform/plugins/payment/resources/js/payment.js"], "sourcesContent": ["'use strict'\n\nvar BPayment = BPayment || {}\n\nBPayment.initResources = function () {\n    let paymentMethod = $(document).find('input[name=payment_method]:checked').first()\n\n    if (!paymentMethod.length) {\n        paymentMethod = $(document).find('input[name=payment_method]').first()\n        paymentMethod.trigger('click').trigger('change')\n    }\n\n    if (paymentMethod.length) {\n        paymentMethod.closest('.list-group-item').find('.payment_collapse_wrap').addClass('show')\n    }\n\n    if ($('.stripe-card-wrapper').length > 0) {\n        new Card({\n            // a selector or DOM element for the form where users will\n            // be entering their information\n            form: '.payment-checkout-form', // *required*\n            // a selector or DOM element for the container\n            // where you want the card to appear\n            container: '.stripe-card-wrapper', // *required*\n\n            formSelectors: {\n                numberInput: 'input#stripe-number', // optional — default input[name=\"number\"]\n                expiryInput: 'input#stripe-exp', // optional — default input[name=\"expiry\"]\n                cvcInput: 'input#stripe-cvc', // optional — default input[name=\"cvc\"]\n                nameInput: 'input#stripe-name', // optional - defaults input[name=\"name\"]\n            },\n\n            width: 350, // optional — default 350px\n            formatting: true, // optional - default true\n\n            // Strings for translation - optional\n            messages: {\n                validDate: 'valid\\ndate', // optional - default 'valid\\nthru'\n                monthYear: 'mm/yyyy', // optional - default 'month/year'\n            },\n\n            // Default placeholders for rendered fields - optional\n            placeholders: {\n                number: '•••• •••• •••• ••••',\n                name: 'Full Name',\n                expiry: '••/••',\n                cvc: '•••',\n            },\n\n            masks: {\n                cardNumber: '•', // optional - mask card number\n            },\n\n            // if true, will log helpful messages for setting up Card\n            debug: false, // optional - default false\n        })\n    }\n}\n\nBPayment.init = function () {\n    BPayment.initResources()\n\n    $(document).on('change', '.js_payment_method', function (event) {\n        event.preventDefault()\n\n        $('.payment_collapse_wrap').removeClass('collapse').removeClass('show').removeClass('active')\n\n        $(event.currentTarget)\n            .closest('.list-group-item')\n            .find('.payment_collapse_wrap')\n            .addClass('show')\n            .addClass('active')\n    })\n\n    $(document)\n        .off('click', '.payment-checkout-btn')\n        .on('click', '.payment-checkout-btn', function (event) {\n            event.preventDefault()\n\n            const button = $(event.currentTarget)\n            const form = button.closest('form')\n            const submitInitialText = button.html()\n\n            if (form.valid && !form.valid()) {\n                return\n            }\n\n            button.prop('disabled', true)\n            button.html(\n                `<span class=\"spinner-border spinner-border-sm me-2\" role=\"status\"></span> ${button.data('processing-text')}`\n            )\n\n            if ($('input[name=payment_method]:checked').val() === 'stripe' && $('.stripe-card-wrapper').length > 0) {\n                Stripe.setPublishableKey($('#payment-stripe-key').data('value'))\n                Stripe.card.createToken(form, function (status, response) {\n                    if (response.error) {\n                        if (typeof Shaqi != 'undefined') {\n                            Shaqi.showError(response.error.message, button.data('error-header'))\n                        } else {\n                            alert(response.error.message)\n                        }\n                        button.prop('disabled', false)\n                        button.html(submitInitialText)\n                    } else {\n                        form.append($('<input type=\"hidden\" name=\"stripeToken\">').val(response.id))\n                        form.submit()\n                    }\n                })\n            } else {\n                form.submit()\n            }\n        })\n}\n\n$(document).ready(function () {\n    BPayment.init()\n\n    document.addEventListener('payment-form-reloaded', function () {\n        BPayment.initResources()\n    })\n})\n"], "names": ["BPayment", "initResources", "paymentMethod", "$", "document", "find", "first", "length", "trigger", "closest", "addClass", "Card", "form", "container", "formSelectors", "numberInput", "expiryInput", "cvcInput", "nameInput", "width", "formatting", "messages", "validDate", "monthYear", "placeholders", "number", "name", "expiry", "cvc", "masks", "cardNumber", "debug", "init", "on", "event", "preventDefault", "removeClass", "currentTarget", "off", "button", "submitInitialText", "html", "valid", "prop", "concat", "data", "val", "Stripe", "setPublishableKey", "card", "createToken", "status", "response", "error", "<PERSON><PERSON><PERSON>", "showError", "message", "alert", "append", "id", "submit", "ready", "addEventListener"], "sourceRoot": ""}