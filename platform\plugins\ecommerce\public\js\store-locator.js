(()=>{function t(o){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(o)}function o(t,o){for(var a=0;a<o.length;a++){var n=o[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e(n.key),n)}}function e(o){var e=function(o,e){if("object"!=t(o)||!o)return o;var a=o[Symbol.toPrimitive];if(void 0!==a){var n=a.call(o,e||"default");if("object"!=t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(o)}(o,"string");return"symbol"==t(e)?e:e+""}var a=function(){return t=function t(){!function(t,o){if(!(t instanceof o))throw new TypeError("Cannot call a class as a function")}(this,t)},(e=[{key:"init",value:function(){$(document).on("click",'[data-bb-toggle="store-locator-show"]',(function(t){t.preventDefault();var o,e=$(t.currentTarget);(o="update"===e.data("type")?$("#update-store-locator-modal .modal-body"):$("#add-store-locator-modal .modal-body")).html(""),$httpClient.make().get(e.data("load-form")).then((function(t){var e=t.data;o.html(e.data),Shaqi.initResources(),o.closest(".modal.fade").modal("show")}))}));var t=function(t){var o=t.closest(".modal-content").find("form");$httpClient.make().withButtonLoading(t).post(o.prop("action"),o.serialize()).then((function(o){var e=o.data;Shaqi.showSuccess(e.message),$(".store-locator-table").load("".concat(window.location.href," .store-locator-table > *")),t.closest(".modal.fade").modal("hide")}))};$("#add-store-locator-modal").on("click",'button[type="submit"]',(function(o){o.preventDefault(),t($(o.currentTarget))})),$("#update-store-locator-modal").on("click",'button[type="submit"]',(function(o){o.preventDefault(),t($(o.currentTarget))})),$(document).on("click",".btn-trigger-delete-store-locator",(function(t){t.preventDefault(),$("#delete-store-locator-button").data("target",$(t.currentTarget).data("target")),$("#delete-store-locator-modal").modal("show")})),$(document).on("click","#delete-store-locator-button",(function(t){t.preventDefault();var o=$(t.currentTarget);$httpClient.make().withButtonLoading(o).post(o.data("target")).then((function(t){var e=t.data;Shaqi.showSuccess(e.message),$(".store-locator-table").load("".concat(window.location.href," .store-locator-table > *")),o.removeClass("button-loading"),o.closest(".modal.fade").modal("hide")}))})),$(document).on("click","#change-primary-store-locator-button",(function(t){t.preventDefault();var o=$(t.currentTarget),e=o.closest(".modal-content").find("form");$httpClient.make().withButtonLoading(o).post(e.prop("action"),e.serialize()).then((function(t){var e=t.data;Shaqi.showSuccess(e.message),$(".store-locator-table").load("".concat(window.location.href," .store-locator-table > *")),o.removeClass("button-loading"),o.closest(".modal.fade").modal("hide")}))}))}}])&&o(t.prototype,e),a&&o(t,a),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,e,a}();$((function(){(new a).init()}))})();
//# sourceMappingURL=store-locator.js.map