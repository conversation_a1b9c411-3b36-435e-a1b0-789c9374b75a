(()=>{function t(a){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(a)}function a(t,a){for(var o=0;o<a.length;o++){var e=a[o];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(t,n(e.key),e)}}function n(a){var n=function(a,n){if("object"!=t(a)||!a)return a;var o=a[Symbol.toPrimitive];if(void 0!==o){var e=o.call(a,n||"default");if("object"!=t(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(a)}(a,"string");return"symbol"==t(n)?n:n+""}var o=function(){function t(){!function(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}(this,t)}return n=t,e=[{key:"setAnimation",value:function(t,a){t.each((function(){var t=$(this),n="animated "+t.data("animation-"+a);t.addClass(n).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",(function(){t.removeClass(n)}))}))}}],(o=[{key:"init",value:function(){var a=$(document).find(".owl-slider");a.length>0&&a.each((function(){var n=$(this),o=n.data("owl-auto"),e=n.data("owl-loop"),i=n.data("owl-speed"),r=n.data("owl-gap"),l=n.data("owl-nav"),d=n.data("owl-dots"),m=n.data("owl-animate-in")?n.data("owl-animate-in"):"",u=n.data("owl-animate-out")?n.data("owl-animate-out"):"",s=n.data("owl-item"),f=n.data("owl-item-xs"),c=n.data("owl-item-sm"),w=n.data("owl-item-md"),p=n.data("owl-item-lg"),v=n.data("owl-item-xl"),y=n.data("owl-nav-left")?n.data("owl-nav-left"):'<i class="fa fa-angle-left"></i>',h=n.data("owl-nav-right")?n.data("owl-nav-right"):'<i class="fa fa-angle-right"></i>',g=n.data("owl-duration"),b="on"===n.data("owl-mousedrag"),S=n.data("owl-center");a.children("div, span, a, img, h1, h2, h3, h4, h5, h5").length>=1&&(n.owlCarousel({rtl:"rtl"===$("body").prop("dir"),animateIn:m,animateOut:u,margin:r,autoplay:o,autoplayTimeout:i,autoplayHoverPause:!0,loop:e,nav:l,mouseDrag:b,touchDrag:!0,autoplaySpeed:g,navSpeed:g,dotsSpeed:g,dragEndSpeed:g,navText:[y,h],dots:d,items:s,center:Boolean(S),responsive:{0:{items:f},480:{items:c},768:{items:w},992:{items:p},1200:{items:v},1680:{items:s}}}),n.on("change.owl.carousel",(function(a){var o=$(".owl-item",n).eq(a.item.index).find("[data-animation-out]");t.setAnimation(o,"out")})),n.on("changed.owl.carousel",(function(a){var o=$(".owl-item",n).eq(a.item.index).find("[data-animation-in]");t.setAnimation(o,"in")})))}))}}])&&a(n.prototype,o),e&&a(n,e),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,o,e}();$((function(){(new o).init()}))})();
//# sourceMappingURL=simple-slider.js.map