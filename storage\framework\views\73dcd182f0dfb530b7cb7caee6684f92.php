<h1 class="d-none"><?php echo e(SeoHelper::getTitle()); ?></h1>
<?php

    Theme::asset()->add('dataTables-bs5', 'https://cdn.datatables.net/2.3.0/css/dataTables.bootstrap5.css');
    Theme::asset()->add('dataTables-responsive', 'https://cdn.datatables.net/responsive/3.0.4/css/responsive.bootstrap5.css');


Theme::asset()->container('footer')->add('dataTables', 'https://cdn.datatables.net/2.3.0/js/dataTables.js');
Theme::asset()->container('footer')->add('dataTables-bs5', 'https://cdn.datatables.net/2.3.0/js/dataTables.bootstrap5.js');
Theme::asset()->container('footer')->add('dataTables-responsive', 'https://cdn.datatables.net/responsive/3.0.4/js/dataTables.responsive.js');
Theme::asset()->container('footer')->add('dataTables-bs5-responsive', 'https://cdn.datatables.net/responsive/3.0.4/js/responsive.bootstrap5.js');
// Theme::asset()->container('footer')->add('dataTables-responsive', 'https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js');

?>
<style>
    .product-table th, .product-table td {
        vertical-align: middle !important;
    }
    .product-table tr > th {
        background-color:#f2f2f2 !important;
    }
    .product-table td {
        font-size: 15px;
        padding: 2px;
    }
    @media (max-width: 768px) {
        .product-table td:first-child {
            display: flex;
            align-items: center;
        }
    }
</style>


<div class="product-area pb-20">
    <div class="container">
        <div class="row">
            <div class="col">
                <h1><?php echo e($category->name); ?></h1>
            </div>
        </div>
        <?php $__currentLoopData = $category->activeChildren; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if($subCategory->count_all_products == 0): ?>
            <?php continue; ?>
        <?php endif; ?>
        <div class="row position-relative category-list-item pt-15 pb-15">
            <div class="col-lg-12 col-md-12 col-sm-12">
                <div class="category-list-item__image">
                    <h2 class="category-list-item__title mb-30"><?php echo e($subCategory->name); ?></h2>
                </div>
            </div>
            <div class="col-lg-12 product-list bb-product-items-wrapper">
                <?php
                    $products = $subCategory->products;
                    $currentLayout = (strpos(strtolower($category->name), 'accessories') !== false) ? 'grid' : 'table';
                ?>
                <?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.product-items'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
</div>

<?php echo $__env->make(Theme::getThemeNamespace('views.ecommerce.includes.quick-view-modal'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php /**PATH D:\laragon\www\tesmods\platform\themes/ninico/views/ecommerce/product-category-childe-cats-with-products.blade.php ENDPATH**/ ?>