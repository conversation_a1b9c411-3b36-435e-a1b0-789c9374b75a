<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use <PERSON>haqi\Ecommerce\Models\GlobalOption;
use Shaqi\Ecommerce\Models\Option;
use <PERSON>haqi\Ecommerce\Models\OptionValue;
use <PERSON>haqi\Ecommerce\Models\ProductCategory;

class TestProductOptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:product-options {--dry-run : Show what would be done without making changes} {--apply : Actually apply the changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test and optionally apply product options to Tesla parts categories';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Testing Product Options Assignment for Tesla Parts');
        $this->info('=' . str_repeat('=', 60));

        // Step 1: Check Global Options
        if (!$this->checkGlobalOptions()) {
            return 1;
        }

        // Step 2: Check Categories
        $relevantCategories = $this->checkCategories();
        if (empty($relevantCategories)) {
            return 1;
        }

        // Step 3: Check Products
        $targetProducts = $this->checkProducts($relevantCategories);
        if (empty($targetProducts)) {
            return 1;
        }

        // Step 4: Show Summary
        $this->showSummary($targetProducts);

        // Step 5: Apply changes if requested
        if ($this->option('apply')) {
            return $this->applyChanges($targetProducts);
        } elseif ($this->option('dry-run')) {
            $this->info('✅ Dry run completed. Use --apply to make actual changes.');
        } else {
            $this->info('💡 Use --dry-run to see what would be done, or --apply to make changes.');
        }

        return 0;
    }

    /**
     * Check if required global options exist
     */
    private function checkGlobalOptions(): bool
    {
        $this->info('📋 Step 1: Checking Global Options...');

        $requiredOptions = ['Painting', 'Paint Codes', 'Part Installation'];
        $foundOptions = [];

        foreach ($requiredOptions as $optionName) {
            $option = GlobalOption::where('name', $optionName)->first();
            if ($option) {
                $valuesCount = $option->values()->count();
                $this->line("  ✅ {$optionName} (ID: {$option->id}, Values: {$valuesCount})");
                $foundOptions[] = $option;
            } else {
                $this->error("  ❌ {$optionName} - NOT FOUND");
            }
        }

        if (count($foundOptions) !== count($requiredOptions)) {
            $this->error('❌ Required global options not found. Please create them in admin panel first.');
            return false;
        }

        $this->info('✅ All required global options found!');
        return true;
    }

    /**
     * Check relevant categories
     */
    private function checkCategories(): array
    {
        $this->info('📁 Step 2: Checking Tesla Parts Categories...');

        $relevantCategoryIds = [
            // 1001 Bumper and Fascia categories (all Tesla models)
            34, 86, 144, 272,

            // 1010 Body Panels categories (all Tesla models)
            41, 87, 145, 273,

            // Specific subcategories for bumpers and fascia
            35, 39, 112, 115, 171, 174, 300, 305, // Front/Rear Bumper Fascia
            36, 114, 173, 302, // Front Grille and Applique
            37, 40, 113, 116, 172, 175, 303, 304, // Energy Absorbers

            // Specific subcategories for body panels
            42, 117, 176, 306, // Body Side Panels
            43, 118, 178, 308, // Closure Panels
            44, 119, 179, 311, // Front Fenders
            46, 121, 182, 315, // Rear End Panels
            124, 184, 318, // Roof Panels

            // Hood related categories
            55, 131, 330, 333, // Hood Hinges and Fittings

            // Additional Tesla parts
            48, 123, 186, 320, // Rear Fenders
            49, 125, 187, 321, // Quarter Panels
            50, 126, 188, 322, // Rocker Panels
        ];

        $foundCategories = [];
        $missingCategories = [];

        foreach ($relevantCategoryIds as $categoryId) {
            $category = ProductCategory::find($categoryId);
            if ($category) {
                $productCount = DB::table('ec_product_category_product')
                    ->where('category_id', $categoryId)
                    ->count();

                $this->line("  ✅ ID {$categoryId}: {$category->name} ({$productCount} products)");
                $foundCategories[] = $categoryId;
            } else {
                $this->line("  ⚠️  ID {$categoryId}: Category not found");
                $missingCategories[] = $categoryId;
            }
        }

        $this->info("✅ Found " . count($foundCategories) . " categories, " . count($missingCategories) . " missing");

        if (!empty($missingCategories)) {
            $this->warn('⚠️  Some categories not found. This is normal if database structure is different.');
        }

        return $foundCategories;
    }

    /**
     * Check products in relevant categories
     */
    private function checkProducts(array $categoryIds): array
    {
        $this->info('📦 Step 3: Checking Products...');

        $productIds = collect();
        foreach ($categoryIds as $categoryId) {
            $categoryProductIds = DB::table('ec_product_category_product')
                ->where('category_id', $categoryId)
                ->pluck('product_id');
            $productIds = $productIds->merge($categoryProductIds);
        }

        $uniqueProductIds = $productIds->unique()->values()->toArray();
        $this->info("📊 Found " . count($uniqueProductIds) . " unique products in relevant categories");

        // Check how many already have options
        $productsWithOptions = Option::whereIn('name', ['Painting', 'Paint Codes', 'Part Installation'])
            ->whereIn('product_id', $uniqueProductIds)
            ->distinct('product_id')
            ->count('product_id');

        $this->info("📋 Products already with options: {$productsWithOptions}");
        $this->info("🆕 Products needing options: " . (count($uniqueProductIds) - $productsWithOptions));

        return $uniqueProductIds;
    }

    /**
     * Show summary of what will be done
     */
    private function showSummary(array $productIds): void
    {
        $this->info('📊 Step 4: Summary...');

        $globalOptions = GlobalOption::whereIn('name', ['Painting', 'Paint Codes', 'Part Installation'])->get();

        $totalOptionsToCreate = 0;
        foreach ($productIds as $productId) {
            foreach ($globalOptions as $globalOption) {
                $existingOption = Option::where('product_id', $productId)
                    ->where('name', $globalOption->name)
                    ->first();

                if (!$existingOption) {
                    $totalOptionsToCreate++;
                }
            }
        }

        $this->table([
            'Metric', 'Count'
        ], [
            ['Target Products', count($productIds)],
            ['Global Options Available', $globalOptions->count()],
            ['New Product Options to Create', $totalOptionsToCreate],
            ['Estimated Option Values to Create', $totalOptionsToCreate * 3], // Average values per option
        ]);
    }

    /**
     * Apply the changes
     */
    private function applyChanges(array $productIds): int
    {
        $this->info('🚀 Step 5: Applying Changes...');

        $globalOptions = GlobalOption::whereIn('name', ['Painting', 'Paint Codes', 'Part Installation'])->get();

        $progressBar = $this->output->createProgressBar(count($productIds));
        $progressBar->start();

        $optionsCreated = 0;

        foreach ($productIds as $productId) {
            foreach ($globalOptions as $globalOption) {
                if ($this->createProductOption($productId, $globalOption)) {
                    $optionsCreated++;
                }
            }
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        $this->info("✅ Successfully created {$optionsCreated} product options!");
        return 0;
    }

    /**
     * Create a product-specific option from a global option
     */
    private function createProductOption(int $productId, GlobalOption $globalOption): bool
    {
        // Check if this product already has this option
        $existingOption = Option::where('product_id', $productId)
            ->where('name', $globalOption->name)
            ->first();

        if ($existingOption) {
            return false; // Option already exists
        }

        // Create the product option
        $productOption = Option::create([
            'name' => $globalOption->name,
            'option_type' => $globalOption->option_type,
            'product_id' => $productId,
            'order' => 9999,
            'required' => $globalOption->required,
        ]);

        // Copy the option values
        foreach ($globalOption->values as $globalValue) {
            OptionValue::create([
                'option_id' => $productOption->id,
                'option_value' => $globalValue->option_value,
                'affect_price' => $globalValue->affect_price,
                'affect_type' => $globalValue->affect_type,
                'order' => $globalValue->order,
            ]);
        }

        return true;
    }
}
