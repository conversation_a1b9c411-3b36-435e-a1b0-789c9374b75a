{"version": 3, "file": "/vendor/core/plugins/newsletter/js/newsletter.js", "mappings": "AAAAA,GAAE,WACE,IAAMC,EAAmBD,EAAE,qBAErBE,EAAuD,IAAjCD,EAAiBE,KAAK,UAAmB,IAE/DC,EAAgB,SAACC,GACnB,IAAMC,EAAO,IAAIC,KACjBD,EAAKE,QAAQF,EAAKG,UAAYJ,GAC9BK,SAASC,OAAS,+BAAHC,OAAkCN,EAAKO,cAAa,WACvE,EAEA,GAAIZ,EAAiBa,OAAS,EAAG,EAC0B,IAAnDJ,SAASC,OAAOI,QAAQ,uBACxBC,MAAMf,EAAiBE,KAAK,OAAQ,CAChCc,OAAQ,MACRC,QAAS,CACL,eAAgB,mBAChB,OAAU,sBAGjBC,MAAK,SAAAC,GACF,IAAKA,EAASC,GACV,MAAM,IAAIC,MAAM,+BAEpB,OAAOF,EAASG,MACpB,IACCJ,MAAK,SAAAK,GAAc,IAAXrB,EAAIqB,EAAJrB,KACLF,EAAiBwB,KAAKtB,EAAKsB,MAEN,oBAAVC,YAA2D,IAA3BA,MAAMC,kBAC7CD,MAAMC,iBAAiBC,SAG3BC,YAAW,WACH5B,EAAiB6B,KAAK,6BAA6BhB,QACnDb,EAAiB8B,MAAM,OAE/B,GAAG7B,EACP,IAAE,OACK,SAAA8B,GACHC,QAAQD,MAAM,eAAgBA,EAClC,IAGJ/B,EACKiC,GAAG,iBAAiB,WACjB,IAAMC,EAASlC,EAAiB6B,KAAK,iBAErCK,EAAOC,IAAI,aAAeC,KAAKC,IAAI,GAAItC,EAAEuC,QAAQC,SAAWL,EAAOK,UAAY,GAAK,EACxF,IACCN,GAAG,iBAAiB,WACAjC,EAAiB6B,KAAK,QAAQA,KAAK,iCAEvCW,GAAG,YACZrC,EAAc,QAEdA,EAAc,KAEtB,IAEJM,SAASgC,iBAAiB,yBAAyB,kBAAMtC,GAAe,IAExE,IAAIuC,EAAY,SAAUC,GACtB5C,EAAE,6BAA6ByB,KAAKmB,GAASC,MACjD,EA8BIC,EAAwB,SAAUC,GAClC,IAAIH,EAAU,GACd5C,EAAEgD,KAAKD,GAAQ,SAACE,EAAOC,GACH,KAAZN,IACAA,GAAW,UAEfA,GAAWM,CACf,IACAP,EAAUC,EACd,EAEA5C,EAAEU,UAAUwB,GAAG,SAAU,iCAAiC,SAACiB,GACvDA,EAAEC,iBAEF,IAAMC,EAAQrD,EAAEmD,EAAEG,eACZC,EAAUF,EAAMvB,KAAK,uBAE3B9B,EAAE,+BAA+ByB,KAAK,IAAI+B,OAC1CxD,EAAE,6BAA6ByB,KAAK,IAAI+B,OAExCxD,EAAEyD,KAAK,CACHC,KAAM,OACNC,OAAO,EACPC,IAAKP,EAAMQ,KAAK,UAChB1D,KAAM,IAAI2D,SAAST,EAAM,IACzBU,aAAa,EACbC,aAAa,EACbC,WAAY,WAAF,OAAQV,EAAQM,KAAK,YAAY,GAAMK,SAAS,cAAc,EACxEC,QAAS,SAAFC,GAA0B,IAArBpC,EAAKoC,EAALpC,MAAOY,EAAOwB,EAAPxB,QACXZ,EACAW,EAAUC,IAKdS,EAAMvB,KAAK,uBAAuBuC,IAAI,IA/DhC,SAAUzB,GACxB5C,EAAE,+BAA+ByB,KAAKmB,GAASC,MACnD,CA+DYyB,CAAY1B,GAEZlC,SAAS6D,cAAc,IAAIC,YAAY,0BAEvC3C,YAAW,WACP5B,EAAiB8B,MAAM,OAC3B,GAAG,KACP,EACAC,MAAO,SAACA,GArEE,IAAU7B,OACG,KADHA,EAqEU6B,GApElBe,QAA0B5C,EAAK4C,OAAOjC,OAClDgC,EAAsB3C,EAAK4C,aAEM,IAAtB5C,EAAKsE,kBAC4B,IAA7BtE,EAAKsE,aAAa1B,OACL,MAAhB5C,EAAKuE,QACL5B,EAAsB3C,EAAKsE,aAAa1B,aAEA,IAA9B5C,EAAKsE,aAAa7B,QAChCD,EAAUxC,EAAKsE,aAAa7B,SAE5B5C,EAAEgD,KAAK7C,EAAKsE,cAAc,SAACxB,EAAO0B,GAC9B3E,EAAEgD,KAAK2B,GAAI,SAACC,EAAK1B,GACbP,EAAUO,EACd,GACJ,IAGJP,EAAUxC,EAAK0E,WAkDiB,EACpCC,SAAU,WAC0B,oBAArBC,kBACPA,mBAGJxB,EAAQM,KAAK,YAAY,GAAOmB,YAAY,cAChD,GAER,GACJ,CACJ", "sources": ["webpack:///./platform/plugins/newsletter/resources/js/newsletter.js"], "sourcesContent": ["$(() => {\n    const $newsletterPopup = $('#newsletter-popup')\n\n    const newsletterDelayTime = $newsletterPopup.data('delay') * 1000 || 5000\n\n    const dontShowAgain = (time) => {\n        const date = new Date()\n        date.setTime(date.getTime() + time)\n        document.cookie = `newsletter_popup=1; expires=${date.toUTCString()}; path=/`\n    }\n\n    if ($newsletterPopup.length > 0) {\n        if (document.cookie.indexOf('newsletter_popup=1') === -1) {\n            fetch($newsletterPopup.data('url'), {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Accept': 'application/json'\n                }\n            })\n            .then(response => {\n                if (!response.ok) {\n                    throw new Error('Network response was not ok');\n                }\n                return response.json();\n            })\n            .then(({ data }) => {\n                $newsletterPopup.html(data.html);\n\n                if (typeof Theme !== 'undefined' && typeof Theme.lazyLoadInstance !== 'undefined') {\n                    Theme.lazyLoadInstance.update()\n                }\n\n                setTimeout(() => {\n                    if ($newsletterPopup.find('.newsletter-popup-content').length) {\n                        $newsletterPopup.modal('show')\n                    }\n                }, newsletterDelayTime)\n            })\n            .catch(error => {\n                console.error('Fetch error:', error);\n            });\n        }\n\n        $newsletterPopup\n            .on('show.bs.modal', () => {\n                const dialog = $newsletterPopup.find('.modal-dialog')\n\n                dialog.css('margin-top', (Math.max(0, ($(window).height() - dialog.height()) / 2) / 2))\n            })\n            .on('hide.bs.modal', () => {\n                const checkbox = $newsletterPopup.find('form').find('input[name=\"dont_show_again\"]')\n\n                if (checkbox.is(':checked')) {\n                    dontShowAgain(3 * 24 * 60 * 60 * 1000) // 1 day\n                } else {\n                    dontShowAgain(60 * 60 * 1000) // 1 hour\n                }\n            })\n\n        document.addEventListener('newsletter.subscribed', () => dontShowAgain())\n\n        let showError = function (message) {\n            $('.newsletter-error-message').html(message).show()\n        }\n\n        let showSuccess = function (message) {\n            $('.newsletter-success-message').html(message).show()\n        }\n\n        let handleError = function (data) {\n            if (typeof data.errors !== 'undefined' && data.errors.length) {\n                handleValidationError(data.errors)\n            } else {\n                if (typeof data.responseJSON !== 'undefined') {\n                    if (typeof data.responseJSON.errors !== 'undefined') {\n                        if (data.status === 422) {\n                            handleValidationError(data.responseJSON.errors)\n                        }\n                    } else if (typeof data.responseJSON.message !== 'undefined') {\n                        showError(data.responseJSON.message)\n                    } else {\n                        $.each(data.responseJSON, (index, el) => {\n                            $.each(el, (key, item) => {\n                                showError(item)\n                            })\n                        })\n                    }\n                } else {\n                    showError(data.statusText)\n                }\n            }\n        }\n\n        let handleValidationError = function (errors) {\n            let message = ''\n            $.each(errors, (index, item) => {\n                if (message !== '') {\n                    message += '<br />'\n                }\n                message += item\n            })\n            showError(message)\n        }\n\n        $(document).on('submit', 'form.bb-newsletter-popup-form', (e) => {\n            e.preventDefault()\n\n            const $form = $(e.currentTarget)\n            const $button = $form.find('button[type=submit]')\n\n            $('.newsletter-success-message').html('').hide()\n            $('.newsletter-error-message').html('').hide()\n\n            $.ajax({\n                type: 'POST',\n                cache: false,\n                url: $form.prop('action'),\n                data: new FormData($form[0]),\n                contentType: false,\n                processData: false,\n                beforeSend: () => $button.prop('disabled', true).addClass('btn-loading'),\n                success: ({ error, message }) => {\n                    if (error) {\n                        showError(message)\n\n                        return\n                    }\n\n                    $form.find('input[name=\"email\"]').val('')\n\n                    showSuccess(message)\n\n                    document.dispatchEvent(new CustomEvent('newsletter.subscribed'))\n\n                    setTimeout(() => {\n                        $newsletterPopup.modal('hide')\n                    }, 5000)\n                },\n                error: (error) => handleError(error),\n                complete: () => {\n                    if (typeof refreshRecaptcha !== 'undefined') {\n                        refreshRecaptcha()\n                    }\n\n                    $button.prop('disabled', false).removeClass('btn-loading')\n                },\n            })\n        })\n    }\n})\n"], "names": ["$", "$newsletterPopup", "newsletterDelayTime", "data", "dontShowAgain", "time", "date", "Date", "setTime", "getTime", "document", "cookie", "concat", "toUTCString", "length", "indexOf", "fetch", "method", "headers", "then", "response", "ok", "Error", "json", "_ref", "html", "Theme", "lazyLoadInstance", "update", "setTimeout", "find", "modal", "error", "console", "on", "dialog", "css", "Math", "max", "window", "height", "is", "addEventListener", "showError", "message", "show", "handleValidationError", "errors", "each", "index", "item", "e", "preventDefault", "$form", "currentTarget", "$button", "hide", "ajax", "type", "cache", "url", "prop", "FormData", "contentType", "processData", "beforeSend", "addClass", "success", "_ref2", "val", "showSuccess", "dispatchEvent", "CustomEvent", "responseJSON", "status", "el", "key", "statusText", "complete", "refreshRecaptcha", "removeClass"], "sourceRoot": ""}