(()=>{function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,a(r.key),r)}}function a(t){var a=function(t,a){if("object"!=e(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,a||"default");if("object"!=e(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(t)}(t,"string");return"symbol"==e(a)?a:a+""}var i=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.template=$("#currency_template").html(),this.totalItem=0,this.deletedItems=[],this.initData(),this.handleForm(),this.updateCurrency(),this.clearCacheRates(),this.changeOptionUsingExchangeRateCurrencyFormAPI()},(a=[{key:"initData",value:function(){var e=this,t=$.parseJSON($("#currencies").html());$.each(t,(function(t,a){var i=e.template.replace(/__id__/gi,a.id).replace(/__position__/gi,a.order).replace(/__isPrefixSymbolChecked__/gi,1==a.is_prefix_symbol?"selected":"").replace(/__notIsPrefixSymbolChecked__/gi,0==a.is_prefix_symbol?"selected":"").replace(/__isDefaultChecked__/gi,1==a.is_default?"checked":"").replace(/__title__/gi,a.title).replace(/__decimals__/gi,a.decimals).replace(/__exchangeRate__/gi,a.exchange_rate).replace(/__symbol__/gi,a.symbol);$(".swatches-container .swatches-list").append(i),e.totalItem++}))}},{key:"addNewAttribute",value:function(){var e=this,t=e.template.replace(/__id__/gi,0).replace(/__position__/gi,e.totalItem).replace(/__isPrefixSymbolChecked__/gi,"").replace(/__notIsPrefixSymbolChecked__/gi,"").replace(/__isDefaultChecked__/gi,0==e.totalItem?"checked":"").replace(/__title__/gi,"").replace(/__decimals__/gi,0).replace(/__exchangeRate__/gi,1).replace(/__symbol__/gi,"");$(".swatches-container .swatches-list").append(t),e.totalItem++}},{key:"exportData",value:function(){var e=[];return $(".swatches-container .swatches-list li").each((function(t,a){var i=$(a);e.push({id:i.data("id"),is_default:i.find("[data-type=is_default] input[type=radio]").is(":checked")?1:0,order:i.index(),title:i.find("[data-type=title] input").val(),symbol:i.find("[data-type=symbol] input").val(),decimals:i.find("[data-type=decimals] input").val(),exchange_rate:i.find("[data-type=exchange_rate] input").val(),is_prefix_symbol:i.find("[data-type=is_prefix_symbol] select").val()})})),e}},{key:"handleForm",value:function(){var e=this;$(".swatches-container .swatches-list").sortable(),$("body").on("submit",".currency-setting-form",(function(){var t=e.exportData();$("#currencies").val(JSON.stringify(t)),$("#deleted_currencies").val(JSON.stringify(e.deletedItems))})).on("click",".js-add-new-attribute",(function(t){t.preventDefault(),e.addNewAttribute()})).on("click",".swatches-container .swatches-list li .remove-item a",(function(t){t.preventDefault();var a=$(t.currentTarget).closest("li");e.deletedItems.push(a.data("id")),a.remove()}))}},{key:"updateCurrency",value:function(){$(document).on("click","#btn-update-currencies",(function(e){e.preventDefault();var t=$(e.currentTarget),a=$(".currency-setting-form");$httpClient.make().post(a.prop("action"),a.serialize()).then((function(e){var i=e.data;i.error?Shaqi.showError(i.message):$httpClient.make().withButtonLoading(t).withLoading(a.find(".swatches-container")).post(t.data("url")).then((function(e){var t=e.data;if(t.error)Shaqi.showNotice("error",t.message);else{Shaqi.showNotice("success",t.message);var a=$("#currency_template").html(),i="";$.each(t.data,(function(e,t){i+=a.replace(/__id__/gi,t.id).replace(/__position__/gi,t.order).replace(/__isPrefixSymbolChecked__/gi,1==t.is_prefix_symbol?"selected":"").replace(/__notIsPrefixSymbolChecked__/gi,0==t.is_prefix_symbol?"selected":"").replace(/__isDefaultChecked__/gi,1==t.is_default?"checked":"").replace(/__title__/gi,t.title).replace(/__decimals__/gi,t.decimals).replace(/__exchangeRate__/gi,t.exchange_rate).replace(/__symbol__/gi,t.symbol)})),setTimeout((function(){$(".swatches-container .swatches-list").html(i)}),1e3)}}))}))}))}},{key:"clearCacheRates",value:function(){$(document).on("click","#btn-clear-cache-rates",(function(e){e.preventDefault();var t=$(e.currentTarget);$httpClient.make().withButtonLoading(t).post(t.data("url")).then((function(e){var t=e.data;t.error?Shaqi.showError(t.message):Shaqi.showSuccess(t.message)}))}))}},{key:"changeOptionUsingExchangeRateCurrencyFormAPI",value:function(){$(document).on("change",'input[name="use_exchange_rate_from_api"]',(function(e){e.preventDefault();var t=$(".swatch-exchange-rate").find(".input-exchange-rate");e.target.checked?t.prop("disabled",!0):t.prop("disabled",!1)}))}}])&&t(e.prototype,a),i&&t(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,a,i}();$((function(){return new i}))})();
//# sourceMappingURL=currencies.js.map