(()=>{function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(a){return typeof a}:function(a){return a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},a(t)}function t(a,t){for(var n=0;n<t.length;n++){var l=t[n];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(a,e(l.key),l)}}function e(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var l=n.call(t,e||"default");if("object"!=a(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}var n=function(){function a(){!function(a,t){if(!(a instanceof t))throw new TypeError("Cannot call a class as a function")}(this,a)}return e=a,l=[{key:"formatState",value:function(a){return!a.id||a.element.value.toLowerCase().includes("...")?a.text:$('<div>\n                <span class="dropdown-item-indicator">\n                    <img src="'.concat($("#language_flag_path").val()).concat(a.element.value.toLowerCase(),'.svg" class="flag" style="height: 16px;" alt="').concat(a.text,'">\n                </span>\n                <span>').concat(a.text,"</span>\n            </div\n        "))}},{key:"createOrUpdateLanguage",value:function(a,t,e,n,l,o,i,r){var g=arguments.length>8&&void 0!==arguments[8]?arguments[8]:null,c=$("#btn-language-submit");g&&(c=g);var u=c.data("store-url");r&&(u=$("#btn-language-submit-edit").data("update-url")+"?lang_code=".concat(n)),Shaqi.showButtonLoading(c,!0),$httpClient.make().post(u,{lang_id:a.toString(),lang_name:t,lang_locale:e,lang_code:n,lang_flag:l,lang_order:o,lang_is_rtl:i}).then((function(t){var e=t.data;r?$(".table-language").find("tr[data-id="+a+"]").replaceWith(e.data):$(".table-language").append(e.data),Shaqi.showSuccess(e.message)})).finally((function(){$("#language_id").val("").trigger("change"),$("#lang_name").val(""),$("#lang_locale").val("").trigger("change"),$("#lang_code").val("").trigger("change"),$('input[name=lang_rtl][value="0"]').prop("checked",!0),$("#flag_list").val("").trigger("change"),$("#btn-language-submit-edit").prop("id","btn-language-submit").text($("#btn-language-submit").data("add-language-text")),Shaqi.hideButtonLoading(c)}))}}],(n=[{key:"init",value:function(){var t=this;Shaqi.select($(".select-search-language"),{templateResult:a.formatState,templateSelection:a.formatState});var e=$(".table-language");$(document).on("change","#language_id",(function(a){var t=$(a.currentTarget).find("option:selected").data("language");void 0!==t&&t.length>0&&($("#lang_name").val(t[2]).trigger("change"),$("#lang_locale").val(t[0]).trigger("change"),$("#lang_code").val(t[1]).trigger("change"),$('input[name=lang_rtl][value="'.concat("rtl"===t[3]?1:0,'"]')).prop("checked",!0),$("#flag_list").val(t[4]).trigger("change"),$("#btn-language-submit-edit").prop("id","btn-language-submit").text($("#btn-language-submit").data("add-language-text")))})),$(document).on("click","#btn-language-submit",(function(t){t.preventDefault();var e=$("#lang_name").val(),n=$("#lang_locale").val(),l=$("#lang_code").val(),o=$("#flag_list").val(),i=$("#lang_order").val(),r=$("input[name=lang_rtl]:checked").val();a.createOrUpdateLanguage(0,e,n,l,o,i,r,0)})),$(document).on("click","#btn-language-submit-edit",(function(t){t.preventDefault();var e=$("#lang_id").val(),n=$("#lang_name").val(),l=$("#lang_locale").val(),o=$("#lang_code").val(),i=$("#flag_list").val(),r=$("#lang_order").val(),g=$("input[name=lang_rtl]:checked").val(),c=$(t.currentTarget);a.createOrUpdateLanguage(e,n,l,o,i,r,g,1,c)})),e.on("click",".deleteDialog",(function(a){a.preventDefault(),$(".delete-crud-entry").data("section",$(a.currentTarget).data("section")),$(".modal-confirm-delete").modal("show")})),$(".delete-crud-entry").on("click",(function(a){a.preventDefault(),$(".modal-confirm-delete").modal("hide");var n=$(a.currentTarget).data("section");Shaqi.showButtonLoading($(t)),$httpClient.make().delete(n).then((function(a){var t=a.data;t.data&&(e.find("i[data-id=".concat(t.data,"]")).unwrap(),$(".tooltip").remove()),e.find('button[data-section="'.concat(n,'"]')).closest("tr").remove(),Shaqi.showSuccess(t.message)})).finally((function(){Shaqi.hideButtonLoading($(t))}))})),e.on("click",".set-language-default",(function(a){a.preventDefault();var t=$(a.currentTarget);$httpClient.make().get(t.data("section")).then((function(a){var n=a.data,l=e.find("td > svg");l.closest("td svg").removeClass("text-yellow"),l.replaceWith('<a href="javascript:void(0);" data-section="'.concat(route("languages.set.default"),"?lang_id=").concat(l.data("id"),'" class="set-language-default text-decoration-none" data-bs-toggle="tooltip" data-bs-original-title="Choose ').concat(l.data("name"),' as default language">').concat(l.closest("td").html(),"</a>")),t.find("svg").unwrap().addClass("text-yellow"),$(".tooltip").remove(),Shaqi.showSuccess(n.message)}))})),e.on("click",".edit-language-button",(function(a){a.preventDefault();var t=$(a.currentTarget);$httpClient.make().get(t.data("url")).then((function(a){var t=a.data.data;$("#lang_id").val(t.lang_id),$("#lang_name").val(t.lang_name),$("#lang_locale").val(t.lang_locale).trigger("change"),$("#lang_code").val(t.lang_code).trigger("change"),$("#flag_list").val(t.lang_flag).trigger("change"),$('input[name=lang_rtl][value="'.concat(t.lang_is_rtl?1:0,'"]')).prop("checked",!0),$("#lang_order").val(t.lang_order),$("#btn-language-submit").prop("id","btn-language-submit-edit").text($("#btn-language-submit-edit").data("update-language-text"))}))})),$(document).on("submit","form.language-settings-form",(function(a){a.preventDefault();var t=$(a.currentTarget),e=t.find("button[type=submit]");Shaqi.showButtonLoading(e),$httpClient.make().postForm(t.prop("action"),new FormData(t[0])).then((function(a){var e=a.data;Shaqi.showSuccess(e.message),t.removeClass("dirty")})).finally((function(){Shaqi.hideButtonLoading(e)}))}))}}])&&t(e.prototype,n),l&&t(e,l),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,l}();$((function(){(new n).init()}))})();
//# sourceMappingURL=language.js.map