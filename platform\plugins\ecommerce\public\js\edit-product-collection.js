$((function(){var t,a,e=!1;(a=$(".wrap-collection-products")).length&&$httpClient.make().withLoading(a).get(a.data("target")).then((function(t){var e=t.data;e.error?Shaqi.showError(e.message):a.html(e.data)})),$(document).on("click",".list-search-data .selectable-item",(function(t){t.preventDefault();var a=$(t.currentTarget),e=a.closest(".box-search-advance").find("input[type=hidden]"),r=e.val().split(",");if($.each(r,(function(t,a){r[t]=parseInt(a)})),$.inArray(a.data("id"),r)<0){e.val()?e.val("".concat(e.val(),",").concat(a.data("id"))):e.val(a.data("id"));var c=$(document).find("#selected_product_list_template").html().replace(/__name__/gi,a.data("name")).replace(/__id__/gi,a.data("id")).replace(/__url__/gi,a.data("url")).replace(/__image__/gi,a.data("image")).replace(/__attributes__/gi,a.find("a span").text());a.closest(".box-search-advance").find(".list-selected-products").show(),a.closest(".box-search-advance").find(".list-selected-products").append(c)}a.closest(".card").hide()})),$(document).on("click",'[data-bb-toggle="product-search-advanced"]',(function(t){var a=$(t.currentTarget),e=a.closest(".box-search-advance").find(".card");e.show(),e.addClass("active"),0===e.find(".card-body").length&&$httpClient.make().withLoading(e).get(a.data("bb-target")).then((function(t){var a=t.data;a.error?Shaqi.showError(a.message):e.html(a.data)}))})),$(document).on("keyup",'[data-bb-toggle="product-search-advanced"]',(function(a){a.preventDefault();var r=$(a.currentTarget),c=r.closest(".box-search-advance").find(".card");setTimeout((function(){e&&t.abort(),e=!0,t=$httpClient.make().withLoading(c).get(r.data("bb-target"),{keyword:r.val()}).then((function(t){var a=t.data;a.error?Shaqi.showError(a.message):c.html(a.data),e=!1})).catch((function(t){"abort"!==data.statusText&&Shaqi.handleError(t)}))}),500)})),$(document).on("click",".box-search-advance .page-link",(function(t){t.preventDefault();var a=$(t.currentTarget).closest(".box-search-advance").find('[data-bb-toggle="product-search-advanced"]');if(!a.closest(".page-item").hasClass("disabled")&&a.data("bb-target")){var e=a.closest(".box-search-advance").find(".card");$httpClient.make().withLoading(e).get($(t.currentTarget).prop("href"),{keyword:a.val()}).then((function(t){var a=t.data;a.error?Shaqi.showError(a.message):e.html(a.data)}))}})),$(document).on("click","body",(function(t){var a=$(".box-search-advance");a.is(t.target)||0!==a.has(t.target).length||a.find(".card").hide()})),$(document).on("click",'[data-bb-toggle="product-delete-item"]',(function(t){t.preventDefault();var a=$(t.currentTarget).closest(".box-search-advance").find("input[type=hidden]"),e=a.val().split(",");$.each(e,(function(t,a){a=a.trim(),_.isEmpty(a)||(e[t]=parseInt(a))}));var r=e.indexOf($(t.currentTarget).data("bb-target"));r>-1&&e.splice(r,1),a.val(e.join(",")),$(t.currentTarget).closest(".list-selected-products").find(".list-group-item").length<2&&$(t.currentTarget).closest(".list-selected-products").hide(),$(t.currentTarget).closest(".list-group-item").remove()}))}));
//# sourceMappingURL=edit-product-collection.js.map