(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){for(var o=0;o<e.length;o++){var a=e[o];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,n(a.key),a)}}function n(e){var n=function(e,n){if("object"!=t(e)||!e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var a=o.call(e,n||"default");if("object"!=t(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(e,"string");return"symbol"==t(n)?n:n+""}var o=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)},(n=[{key:"init",value:function(){$(document).on("click",".btn-confirm-order",(function(t){t.preventDefault();var e=$(t.currentTarget);$httpClient.make().withButtonLoading(e).post(e.closest("form").prop("action"),e.closest("form").serialize()).then((function(t){var n=t.data;n.error?Shaqi.showError(n.message):($("#main-order-content").load("".concat(window.location.href," #main-order-content > *")),e.closest("div").remove(),Shaqi.showSuccess(n.message))}))})),$(document).on("click",".btn-trigger-resend-order-confirmation-modal",(function(t){t.preventDefault(),$("#confirm-resend-confirmation-email-button").data("action",$(t.currentTarget).data("action")),$("#resend-order-confirmation-email-modal").modal("show")})),$(document).on("click","#confirm-resend-confirmation-email-button",(function(t){t.preventDefault();var e=$(t.currentTarget);$httpClient.make().withButtonLoading(e).post(e.data("action")).then((function(t){var e=t.data;e.error?Shaqi.showError(e.message):Shaqi.showSuccess(e.message),$("#resend-order-confirmation-email-modal").modal("hide")}))})),$(document).on("click",".btn-trigger-shipment",(function(t){t.preventDefault();var e=$(t.currentTarget),n=$(".shipment-create-wrap");n.slideToggle(),n.hasClass("shipment-data-loaded")||(Shaqi.showLoading(n),$httpClient.make().get(e.data("target")).then((function(t){var e=t.data;e.error?Shaqi.showError(e.message):(n.html(e.data),n.addClass("shipment-data-loaded"),Shaqi.initResources()),Shaqi.hideLoading(n)})))})),$(document).on("change","#store_id",(function(t){var e=$(".shipment-create-wrap");Shaqi.showLoading(e),$("#select-shipping-provider").load("".concat($(".btn-trigger-shipment").data("target"),"?view=true&store_id=").concat($(t.currentTarget).val()," #select-shipping-provider > *"),(function(){Shaqi.hideLoading(e),Shaqi.initResources()}))})),$(document).on("change",".shipment-form-weight",(function(t){var e=$(".shipment-create-wrap");Shaqi.showLoading(e),$("#select-shipping-provider").load("".concat($(".btn-trigger-shipment").data("target"),"?view=true&store_id=").concat($("#store_id").val(),"&weight=").concat($(t.currentTarget).val()," #select-shipping-provider > *"),(function(){Shaqi.hideLoading(e),Shaqi.initResources()}))})),$(document).on("click",".table-shipping-select-options .clickable-row",(function(t){var e=$(t.currentTarget);$(".input-hidden-shipping-method").val(e.data("key")),$(".input-hidden-shipping-option").val(e.data("option")),$(".input-show-shipping-method").val(e.find("span.name").text())})),$(document).on("click",".btn-create-shipment",(function(t){t.preventDefault();var e=$(t.currentTarget);$httpClient.make().withButtonLoading(e).post(e.closest("form").prop("action"),e.closest("form").serialize()).then((function(t){var e=t.data;e.error?Shaqi.showError(e.message):(Shaqi.showSuccess(e.message),$("#main-order-content").load("".concat(window.location.href," #main-order-content > *")),$(".btn-trigger-shipment").remove())}))})),$(document).on("click",".btn-cancel-shipment",(function(t){t.preventDefault(),$("#confirm-cancel-shipment-button").data("action",$(t.currentTarget).data("action")),$("#cancel-shipment-modal").modal("show")})),$(document).on("click","#confirm-cancel-shipment-button",(function(t){t.preventDefault();var e=$(t.currentTarget);$httpClient.make().withButtonLoading(e).post(e.data("action")).then((function(t){var e=t.data;e.error?Shaqi.showError(e.message):(Shaqi.showSuccess(e.message),$(".carrier-status").addClass("carrier-status-".concat(e.data.status)).text(e.data.status_text),$("#cancel-shipment-modal").modal("hide"),$("#order-history-wrapper").load("".concat(window.location.href," #order-history-wrapper > *")),$(".shipment-actions-wrapper").remove())}))})),$(document).on("click",".btn-close-shipment-panel",(function(t){t.preventDefault(),$(".shipment-create-wrap").slideUp()})),$(document).on("click",".btn-trigger-update-shipping-address",(function(t){t.preventDefault(),$("#update-shipping-address-modal").modal("show")})),$(document).on("click",".btn-trigger-update-tax-information",(function(t){t.preventDefault(),$("#update-tax-information-modal").modal("show")})),$(document).on("click","#confirm-update-shipping-address-button",(function(t){t.preventDefault();var e=$(t.currentTarget),n=e.closest(".modal-content").find("form");$httpClient.make().withLoading(n.find(".shipment-create-wrap")).withButtonLoading(e).post(n.prop("action"),n.serialize()).then((function(t){var e=t.data;e.error?Shaqi.showError(e.message):(Shaqi.showSuccess(e.message),$("#update-shipping-address-modal").modal("hide"),$(".shipment-address-box-1").html(e.data.line),$(".shipping-address-info").html(e.data.detail),$("#select-shipping-provider").load("".concat($(".btn-trigger-shipment").data("target"),"?view=true #select-shipping-provider > *"),(function(){Shaqi.initResources()})))}))})),$(document).on("click","#confirm-update-tax-information-button",(function(t){t.preventDefault();var e=$(t.currentTarget),n=e.closest(".modal-content").find("form");$httpClient.make().withButtonLoading(e).post(n.prop("action"),n.serialize()).then((function(t){var e=t.data;e.error?Shaqi.showError(e.message):($(".text-infor-subdued.tax-info").html(e.data),$("#update-tax-information-modal").modal("hide"),Shaqi.showSuccess(e.message))}))})),$(document).on("click",".btn-update-order",(function(t){t.preventDefault();var e=$(t.currentTarget);$httpClient.make().withButtonLoading(e).post(e.closest("form").prop("action"),e.closest("form").serialize()).then((function(t){var n=t.data;n.error?Shaqi.showError(n.message):Shaqi.showSuccess(n.message),e.closest(".modal")&&(e.closest(".modal").modal("hide"),$(".page-body").load("".concat(window.location.href," .page-body > *")))}))})),$(document).on("click",".btn-trigger-cancel-order",(function(t){t.preventDefault(),$("#confirm-cancel-order-button").data("target",$(t.currentTarget).data("target")),$("#cancel-order-modal").modal("show")})),$(document).on("click","#confirm-cancel-order-button",(function(t){t.preventDefault();var e=$(t.currentTarget);$httpClient.make().withButtonLoading(e).post(e.data("target")).then((function(t){var e=t.data;e.error?Shaqi.showError(e.message):(Shaqi.showSuccess(e.message),$("#main-order-content").load("".concat(window.location.href," #main-order-content > *")),$("#cancel-order-modal").modal("hide"))}))})),$(document).on("click",".btn-trigger-confirm-payment",(function(t){t.preventDefault(),$("#confirm-payment-order-button").data("target",$(t.currentTarget).data("target")),$("#confirm-payment-modal").modal("show")})),$(document).on("click","#confirm-payment-order-button",(function(t){t.preventDefault();var e=$(t.currentTarget);$httpClient.make().withButtonLoading(e).post(e.data("target")).then((function(t){var e=t.data;e.error?Shaqi.showError(e.message):(Shaqi.showSuccess(e.message),$("#main-order-content").load("".concat(window.location.href," #main-order-content > *")),$("#confirm-payment-modal").modal("hide"))}))})),$(document).on("click",".show-timeline-dropdown",(function(t){t.preventDefault(),$($(t.currentTarget).data("target")).slideToggle()})),$(document).on("keyup",".input-sync-item",(function(t){var e=$(t.currentTarget).val();e&&!isNaN(e)||(e=0),$(t.currentTarget).closest("body").find($(t.currentTarget).data("target")).text(Shaqi.numberFormat(parseFloat(e),2))})),$(document).on("click",".btn-trigger-refund",(function(t){t.preventDefault(),$("#confirm-refund-modal").modal("show")})),$(document).on("change",".j-refund-quantity",(function(){var t=0;$.each($(".j-refund-quantity"),(function(e,n){var o=$(n).val();o&&!isNaN(o)||(o=0),t+=parseFloat(o)})),$(".total-restock-items").text(t)})),$(document).on("click","#confirm-refund-payment-button",(function(t){t.preventDefault();var e=$(t.currentTarget),n=e.closest(".modal-dialog").find("form");$httpClient.make().withButtonLoading(e).post(n.prop("action"),n.serialize()).then((function(t){var n=t.data;n.error?Shaqi.showError(n.message):n.data&&n.data.refund_redirect_url?window.location.href=n.data.refund_redirect_url:($("#main-order-content").load("".concat(window.location.href," #main-order-content > *")),Shaqi.showSuccess(n.message),e.closest(".modal").modal("hide"))}))})),$(document).on("click",".btn-trigger-update-shipping-status",(function(t){t.preventDefault(),$("#update-shipping-status-modal").modal("show")})),$(document).on("click","#confirm-update-shipping-status-button",(function(t){t.preventDefault();var e=$(t.currentTarget),n=e.closest(".modal-dialog").find("form");$httpClient.make().withButtonLoading(e).post(n.prop("action"),n.serialize()).then((function(t){var n=t.data;n.error?Shaqi.showError(n.message):($("#main-order-content").load("".concat(window.location.href," #main-order-content > *")),Shaqi.showSuccess(n.message),e.closest(".modal").modal("hide"))}))}))}}])&&e(t.prototype,n),o&&e(t,o),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,n,o}();$((function(){(new o).init()}))})();
//# sourceMappingURL=order.js.map